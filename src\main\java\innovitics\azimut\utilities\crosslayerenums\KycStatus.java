package innovitics.azimut.utilities.crosslayerenums;

public enum KycStatus {
	PENDING(0,"pending","تحت المراجعة"),
	APPROVED(1,"approved","مقبول"),
	REJECTED(2,"rejected","مرفوض"),
	PENDING_CLIENT(4,"pending client","قيد التصحيح"),
	FIRST_TIME(5,"first time","أول مرة")
	;

	KycStatus(int statusId,String status,String statusAr) {
		this.statusId=statusId;
		this.status=status;
		this.statusAr = statusAr;
	}

	private final int statusId;
	private final String status;
	private final String statusAr;
	
	public int getStatusId() {
		return statusId;
	}
	public String getStatus() {
		return status;
	}
	public String getStatusAr() {
		return statusAr;
	}

	
	
}
