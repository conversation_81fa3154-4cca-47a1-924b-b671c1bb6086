package innovitics.azimut.rest.apis.teacomputers;

import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.controllers.users.DTOs.HoldClientBankAccountDto;
import innovitics.azimut.rest.entities.teacomputers.HoldClientBankAccountRequest;
import innovitics.azimut.rest.entities.teacomputers.HoldClientBankAccountResponse;
import innovitics.azimut.rest.entities.teacomputers.TeaComputerResponse;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

@Component
public class TeaComputersHoldBankAccountApi extends RestTeaComputersApi<HoldClientBankAccountRequest, HoldClientBankAccountResponse> {
    public static final String PATH="/HoldClientBankAcc";
    @Override
    protected String generateURL(String params) {
        return super.generateBaseURL()+PATH;
    }

    @Override
    protected HttpMethod chooseHttpMethod() {
        return HttpMethod.POST;
    }

    @Override
    protected String generateSignature(HoldClientBankAccountRequest request) {
        return this.teaComputersSignatureGenerator.generateSignature(request.getIdTypeId().toString(),request.getIdNumber(),request.getAccountID().toString());
    }

    @Override
    protected String generateResponseSignature(TeaComputerResponse teaComputerResponse) {
        HoldClientBankAccountResponse response = (HoldClientBankAccountResponse) teaComputerResponse;
        return this.teaComputersSignatureGenerator.generateSignature("",response.getMessage());
    }

    public HoldClientBankAccountRequest prepareRequest(HoldClientBankAccountDto businessAzimutClient,
                                                       BusinessUser tokenizedBusinessUser){
        HoldClientBankAccountRequest request = new HoldClientBankAccountRequest();
        request.setIdNumber(tokenizedBusinessUser.getUserId());
        request.setIdTypeId(tokenizedBusinessUser.getAzimutIdTypeId());
        request.setAccountID(businessAzimutClient.getAccountId());
        request.setSignature(this.generateSignature(request));

        return request;
    }
}
