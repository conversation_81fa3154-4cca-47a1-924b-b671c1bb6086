package innovitics.azimut.models.kyc;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.NamedSubgraph;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import innovitics.azimut.models.DbBaseEntity;
import innovitics.azimut.models.user.UserType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Entity
@NamedEntityGraph(name = "KYCPage.details", attributeNodes = @NamedAttributeNode(value = "questions", subgraph = "Question.details"),

    subgraphs = {
        @NamedSubgraph(name = "Question.details", attributeNodes = {
            @NamedAttributeNode(value = "answers", subgraph = "Answer.details"),
            @NamedAttributeNode(value = "subQuestions") }),
        @NamedSubgraph(name = "Answer.details", attributeNodes = { @NamedAttributeNode(value = "relatedAnswers") })

    })
@NamedEntityGraph(name = "KYCPage.compact", attributeNodes = {
    @NamedAttributeNode(value = "title"),
    @NamedAttributeNode(value = "pageOrder"),
    @NamedAttributeNode(value = "pageDetails"),
    @NamedAttributeNode(value = "pageDisclaimer"),
}

)
@Table(name = "kyc_pages")
@Getter
@Setter
@ToString
public class KYCPage extends DbBaseEntity implements Serializable {
  private static final long serialVersionUID = -5692495211725567689L;
  @ManyToOne
  @JoinColumn(name = "USER_ID_TYPE")
  private UserType userType;
  protected String title;
  protected Integer pageOrder;
  protected String pageDetails;
  protected String pageDisclaimer;
  protected int noOfQuestions;
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  protected Date deletedAt;

  @Transient
  protected Long appUserId;
  @Transient
  protected Boolean draw;

  @OneToMany(mappedBy = "kycPage", fetch = FetchType.LAZY)
  @Fetch(FetchMode.JOIN)
  @OrderBy("questionOrder ASC")
  private Set<Question> questions;

  // ADDED FIELD
  @Column(name = "next_id")
  private Long nextPageId;

  // ADDED FIELD
  @Column(name = "previous_id")
  private Long previousPageId;

  private Integer weight;
  protected String titleAr;
  protected String pageDetailsAr;
  protected String pageDisclaimerAr;

}
