package innovitics.azimut.models.teacomputers;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Entity
@Table(name = "azimut_currencies")
@Data
public class Currency {
  @Id
  @GeneratedValue(strategy = GenerationType.AUTO)
  private Long id;
  private Long systemCurrencyCode;
  private Long currencyId;
  private String englishCurrencyName;
  private String arabicCurrencyName;

}
