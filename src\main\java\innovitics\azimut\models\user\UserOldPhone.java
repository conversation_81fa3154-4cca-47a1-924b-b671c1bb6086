package innovitics.azimut.models.user;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import innovitics.azimut.models.DbBaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "user_old_phones")
@Setter
@Getter
@ToString
public class UserOldPhone extends DbBaseEntity {
  @ManyToOne
  @JoinColumn(name = "user_id", nullable = false)
  private User user;

  private String userPhone;

  private Date deletedAt;
}
