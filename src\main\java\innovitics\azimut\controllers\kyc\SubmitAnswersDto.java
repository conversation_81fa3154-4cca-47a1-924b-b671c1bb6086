package innovitics.azimut.controllers.kyc;

import javax.validation.constraints.NotNull;

import innovitics.azimut.businessmodels.kyc.BusinessUserSubmittedAnswer;
import lombok.Data;

@Data
public class SubmitAnswersDto {
  @NotNull(message = "pageId is required")
  Long pageId;

  @NotNull(message = "nextPageId is required")
  Long nextPageId;

  BusinessUserSubmittedAnswer[] userAnswers;
  Boolean isMobile;

}
