package innovitics.azimut.utilities.datautilities;

import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.utilities.crosslayerenums.UserIdType;

public class UserTypeUtility {

  public static long getRelevantIdType(BusinessUser businessUser)
	{
		long idType=0l;
		if(BooleanUtility.isTrue(businessUser.getIsInstitutional()))
		{
			idType=UserIdType.INSTITUTIONAL.getTypeId();
		}
		else 
		{
			idType=UserIdType.NATIONAL_ID.getTypeId();
		}
		return idType;
	}
  
}
