package innovitics.azimut.businessservices;

import java.io.IOException;
import java.util.LinkedList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import innovitics.azimut.businessmodels.kyc.BusinessKYCPage;
import innovitics.azimut.businessmodels.kyc.BusinessQuestion;
import innovitics.azimut.businessmodels.kyc.BusinessUserAnswerSubmission;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.controllers.kyc.SubmitAnswersDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.models.kyc.KYCPage;
import innovitics.azimut.models.kyc.UserAnswer;
import innovitics.azimut.services.kyc.KYCPageService;
import innovitics.azimut.services.kyc.UserAnswerSubmissionService;
import innovitics.azimut.utilities.crosslayerenums.AnswerType;
import innovitics.azimut.utilities.crosslayerenums.UserStep;
import innovitics.azimut.utilities.datautilities.DateUtility;
import innovitics.azimut.utilities.datautilities.ListUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.datautilities.UserTypeUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.fileutilities.BlobData;
import innovitics.azimut.utilities.kycutilities.AnswerTypeUtility;
import innovitics.azimut.utilities.logging.MyLogger;
import innovitics.azimut.utilities.mapping.kyc.KYCPageMapper;
import innovitics.azimut.utilities.mapping.kyc.UserAnswerMapper;
import innovitics.azimut.utilities.mapping.kyc.UserAnswersIntermediary;

@Service
public class BusinessUserAnswerSubmissionService extends AbstractBusinessService<BusinessUserAnswerSubmission> {

  protected @Autowired UserAnswerSubmissionService userAnswerSubmissionService;
  protected @Autowired UserAnswerMapper userAnswerMapper;
  protected @Autowired ListUtility<UserAnswer> userAnswerListUtility;
  protected @Autowired AnswerTypeUtility answerTypeUtility;
  protected @Autowired BusinessUserService businessUserService;
  protected @Autowired KYCPageService kycPageService;
  protected @Autowired ListUtility<BusinessQuestion> questionListUtility;
  protected @Autowired KYCPageMapper kycPageMapper;
  protected @Autowired ListUtility<Integer> integerListUtility;

  @Transactional(rollbackFor = BusinessException.class)
  public BusinessKYCPage submitAnswers(BusinessUser businessUser,
      SubmitAnswersDto businessUserAnswerSubmission) throws BusinessException {
    this.validation.validateUserKYCCompletion(businessUser);
    BusinessKYCPage businessKYCPage = new BusinessKYCPage();
    this.validation.checkUserAnswersValidity(businessUserAnswerSubmission);
    try {

      KYCPage pageDetails = this.kycPageService.getById(businessUserAnswerSubmission.getPageId(),
          UserTypeUtility.getRelevantIdType(businessUser));
      List<UserAnswer> oldUserAnswers = this.userAnswerSubmissionService
          .getOldUserAnswers(businessUserAnswerSubmission.getPageId(), businessUser.getId());
      if (!userAnswerListUtility.isListPopulated(oldUserAnswers)) {
        int weight = 0;
        int order = 0;
        weight = pageDetails.getWeight().intValue();
        order = pageDetails.getPageOrder().intValue();
        businessUser.setLastSolvedPageId(businessUserAnswerSubmission.getPageId());
        businessUser.setNextPageId(
            businessUserAnswerSubmission.getNextPageId() != null
                ? businessUserAnswerSubmission.getNextPageId()
                : businessUserAnswerSubmission.getPageId());
        StringBuffer stringBuffer = new StringBuffer(
            businessUser != null && StringUtility.isStringPopulated(businessUser.getSolvedPages())
                ? businessUser.getSolvedPages()
                : "");
        // businessUser.setSolvedPages(stringBuffer.append(String.valueOf(order)+",").toString());
        businessUser.setSolvedPages(addSolvedPageOrder(order, stringBuffer));
        businessKYCPage.setVerificationPercentage(this.updateUserProgress(businessUser, weight));
      } else {
        businessKYCPage.setVerificationPercentage(businessUser.getVerificationPercentage());
      }
      this.userAnswerSubmissionService.deleteOldUserAnswers(businessUserAnswerSubmission.getPageId(),
          businessUser.getId());
      // this.generatePdf(this.kycPageMapper.convertBasicUnitToBusinessUnit(pageDetails,
      // businessUserAnswerSubmission.getLanguage(),
      // false),businessUserAnswerSubmission,businessUser);
      this.processAndSaveAnswers(businessUser.getId(), businessUserAnswerSubmission, oldUserAnswers);
      // businessUser=this.userUtility.isOldUserStepGreaterThanNewUserStep(businessUser,
      // businessUserAnswerSubmission.getUserStep());
      var nextUserStep = this.reviewUtility.calculateUserStepUnderReview(businessUser);
      if (nextUserStep < UserStep.CHOOSE_CONTRACT_MAP.getStepId())
        businessUser.setUserStep(nextUserStep);
      this.businessUserService.editUser(businessUser);
      businessKYCPage.setNextUserStep(nextUserStep);
    } catch (Exception exception) {
      MyLogger.info("Could not submit the user answers.");
      throw handleBusinessException(exception, ErrorCode.ANSWER_SUBMISSION_FAILED);
    }

    return businessKYCPage;
  }

  String addSolvedPageOrder(int order, StringBuffer stringBuffer) {
    List<Integer> pageOrders = StringUtility.splitStringUsingCharacterToIntegerArray(stringBuffer.toString(), ",");
    if (integerListUtility.isListPopulated(pageOrders) && pageOrders.contains(order)) {
      return "";
    } else {
      return stringBuffer.append(String.valueOf(order) + ",").toString();
    }

  }

  public void processAndSaveAnswers(Long userId, SubmitAnswersDto businessUserAnswerSubmission,
      List<UserAnswer> oldUserAnswers) throws BusinessException {
    List<String> textAnswerTypes = AnswerType.getAnswerTypes(true);
    List<String> choiceAnswerTypes = AnswerType.getAnswerTypes(false);
    boolean relatedAnswersExist = false;
    List<UserAnswersIntermediary> userAnswersIntermediaries = userAnswerMapper
        .convertBusinessUserAnswerSubmissionToUserAnswerList(userId, businessUserAnswerSubmission);
    LinkedList<UserAnswer> userAnswers = new LinkedList<>();
    LinkedList<UserAnswer> relatedAnswers = new LinkedList<>();

    for (UserAnswersIntermediary userAnswersIntermediary : userAnswersIntermediaries) {
      MyLogger.info("User Answer Int:::" + userAnswersIntermediary.toString());
      this.copyAnswerIfDocument(userAnswersIntermediary.getParentAnswer());
      userAnswers.add(userAnswersIntermediary.getParentAnswer());

      if (userAnswerListUtility.isListPopulated(userAnswersIntermediary.getRelatedAnswers())) {
        relatedAnswersExist = true;
        relatedAnswers.addAll(userAnswersIntermediary.getRelatedAnswers());
      }

    }
    this.submitAnswers(userAnswers, oldUserAnswers, textAnswerTypes, choiceAnswerTypes);

    if (relatedAnswersExist) {
      for (UserAnswer userAnswer : relatedAnswers) {
        this.copyAnswerIfDocument(userAnswer);
      }
      relatedAnswers.removeIf(n -> n.getAnswerId() == null);
      this.submitAnswers(relatedAnswers, oldUserAnswers, textAnswerTypes, choiceAnswerTypes);
    }

  }

  public BusinessKYCPage uploadDocumentAndOverwriteOldDocumentIfExisting(BusinessUser businessUser, Long answerId,
      MultipartFile file) throws BusinessException {
    BusinessKYCPage businessKYCPage = new BusinessKYCPage();
    if (businessUser != null) {
      UserAnswer oldUserAnswer = this.checkForPreviousAnswer(businessUser, answerId);
      if (oldUserAnswer != null && StringUtility.isStringPopulated(oldUserAnswer.getDocumentName())) {
        try {
          this.deleteDocument(oldUserAnswer.getDocumentSubDirectory(), oldUserAnswer.getDocumentName());
        } catch (Exception exception) {
          this.handleBusinessException(exception, ErrorCode.UPLOAD_FAILURE);
        }
      }
      this.uploadDocument(businessUser, answerId, file);
    }
    return businessKYCPage;
  }

  public UserAnswer checkForPreviousAnswer(BusinessUser businessUser, Long answerId) {
    try {
      return this.userAnswerSubmissionService.getPreviousAnswerIfExisting(businessUser.getId(), answerId);
    } catch (Exception exception) {
      if (this.exceptionHandler.isABusinessException(exception)) {
        return null;
      }
    }
    return null;
  }

  public BusinessKYCPage uploadDocument(BusinessUser businessUser, Long answerId, MultipartFile file)
      throws BusinessException {

    String[] extensions = { "pdf", "jpg", "jpeg", "png", "webp" };
    this.validation.validateFileExtension(file, extensions);
    BusinessKYCPage businessKYCPage = new BusinessKYCPage();
    UserAnswer userAnswer = this.checkForPreviousAnswer(businessUser, answerId);
    if (userAnswer != null) {
      this.updateAnswer(userAnswer);
    }
    try {

      BlobData blobData = this.storageService.uploadFile(file, true,
          this.configProperties.getBlobKYCDocumentsTemp(),
          DateUtility.getCurrentDayMonthYear() + "/" + businessUser.getUserId(), true);
      businessKYCPage.setDocumentURL(blobData.getConcatinated(false));
      businessKYCPage.setDocumentSize(blobData.getFileSize());
      businessKYCPage.setDocumentName(this.aes.encrypt(blobData.getFileName()));
      businessKYCPage.setDocumentSubDirectory(this.aes.encrypt(blobData.getSubDirectory()));

    } catch (IOException exception) {
      MyLogger.info("Failed to upload the document.");
      MyLogger.logStackTrace(exception);
      this.handleBusinessException(exception, ErrorCode.UPLOAD_FAILURE);
    }
    return businessKYCPage;
  }

  public void deleteDocument(String subDiretory, String fileName) throws IOException {
    this.storageService.deleteFile(this.configProperties.getBlobKYCDocuments(), fileName, subDiretory, false, null);
  }

  public void copyDocument(String subDiretory, String fileName) {
    try {
      this.storageService.copyFile(this.configProperties.getBlobKYCDocumentsTemp(),
          this.configProperties.getBlobKYCDocuments(), subDiretory, fileName, false);
      this.storageService.deleteFile(this.configProperties.getBlobKYCDocumentsTemp(), fileName, subDiretory,
          false,
          null);
    } catch (Exception exception) {
      this.handleBusinessException(exception, ErrorCode.COPY_FAILURE);
    }
  }

  void copyAnswerIfDocument(UserAnswer userAnswer) {
    String decryptedSubDirectory = this.storageService.getStorageType()
        .decrypt(userAnswer.getDocumentSubDirectory());
    String decryptedDocumentName = this.storageService.getStorageType().decrypt(userAnswer.getDocumentName());

    userAnswer.setDocumentSubDirectory(decryptedSubDirectory);
    userAnswer.setDocumentName(decryptedDocumentName);

    if (this.answerTypeUtility.isAnswerTypeMatching(userAnswer, AnswerType.DOCUMENT)
        && StringUtility.isStringPopulated(decryptedDocumentName)
        && StringUtility.isStringPopulated(decryptedSubDirectory)) {
      this.copyDocument(userAnswer.getDocumentSubDirectory(), userAnswer.getDocumentName());
    }
  }

  UserAnswer updateAnswer(UserAnswer userAnswer) {
    try {
      if (StringUtility.isStringPopulated(userAnswer.getDocumentName()))
        this.deleteDocument(userAnswer.getDocumentSubDirectory(), userAnswer.getDocumentName());
    } catch (IOException exception) {
      this.handleBusinessException(exception, ErrorCode.UPLOAD_FAILURE);
    }

    if (userAnswer != null) {
      userAnswer.setDocumentName(null);
      userAnswer.setDocumentSize(null);
      userAnswer.setDocumentSubDirectory(null);
      userAnswer.setDocumentUrl(null);
    }

    return this.userAnswerSubmissionService.updateAnswer(userAnswer);
  }

  Integer updateUserProgress(BusinessUser businessUser, Integer pageWeight) {
    if (businessUser != null && businessUser.getVerificationPercentage() == null) {
      businessUser.setVerificationPercentage(pageWeight);
    } else if (businessUser != null && businessUser.getVerificationPercentage() != null) {
      int currentValue = businessUser.getVerificationPercentage();
      businessUser.setVerificationPercentage(currentValue + pageWeight.intValue());
    }
    return businessUser.getVerificationPercentage();
  }

  KYCPage findPageDetails(Long id) throws BusinessException {
    try {
      return this.kycPageService.findPageDetailsById(id);
    } catch (Exception exception) {
      this.exceptionHandler.getNullIfNonExistent(exception);
    }
    return null;
  }

  protected void submitAnswers(List<UserAnswer> userAnswers, List<UserAnswer> oldUserAnswers,
      List<String> textAnswerTypes, List<String> choiceAnswerTypes) {
    /*
     * List<UserAnswer> userAnswersToBeRemoved=new ArrayList<UserAnswer>();
     * for(UserAnswer userAnswer:userAnswers)
     * {
     * for(UserAnswer oldUserAnswer:oldUserAnswers)
     * {
     * if(userAnswer!=null&&oldUserAnswer!=null)
     * {
     * if(
     * NumberUtility.areLongValuesMatching(userAnswer.getQuestionId(),
     * oldUserAnswer.getQuestionId())&&NumberUtility.areLongValuesMatching(
     * userAnswer.getAnswerId(), oldUserAnswer.getAnswerId())
     * ||
     * NumberUtility.areLongValuesMatching(userAnswer.getRelatedAnswerId(),
     * oldUserAnswer.getRelatedAnswerId())&&NumberUtility.areLongValuesMatching(
     * userAnswer.getAnswerId(), oldUserAnswer.getAnswerId())
     * )
     * {
     * if(StringUtility.stringsMatch(userAnswer.getAnswerType(),oldUserAnswer.
     * getAnswerType())&&textAnswerTypes.contains(userAnswer.getAnswerType()))
     * {
     * if(StringUtility.stringsMatch(userAnswer.getAnswerValue(),oldUserAnswer.
     * getAnswerValue()))
     * {
     * userAnswersToBeRemoved.add(userAnswer);
     * }
     * }
     * else if(StringUtility.stringsMatch(userAnswer.getAnswerType(),oldUserAnswer.
     * getAnswerType())&&choiceAnswerTypes.contains(userAnswer.getAnswerType()))
     * {
     * userAnswersToBeRemoved.add(userAnswer);
     * }
     * }
     * }
     * }
     * }
     * userAnswers.removeAll(userAnswersToBeRemoved);
     */
    this.userAnswerSubmissionService.submitAnswers(userAnswers);
  }
}
