package innovitics.azimut.security;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.password.NoOpPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

@EnableWebSecurity
public class SecurityConfigurer extends WebSecurityConfigurerAdapter {
  @Autowired
  private MyUserDetailsService myUserDetailsService;

  @Autowired
  private JwtRequestFilter jwtRequestFilter;

  @Override
  protected void configure(AuthenticationManagerBuilder authenticationManagerBuilder) throws Exception {
    authenticationManagerBuilder.parentAuthenticationManager(authenticationManagerBean())
        .userDetailsService(myUserDetailsService);
  }

  @Override
  @Bean
  public AuthenticationManager authenticationManagerBean() throws Exception {
    return super.authenticationManagerBean();
  }

  @Override
  protected void configure(HttpSecurity httpSecurity) throws Exception {
    httpSecurity.csrf().disable()
        .authorizeRequests()
        .antMatchers("/api/authenticate", "/api/forgotPassword", "/api/user/getByUserPhone", "/api/saveUserTemporarily",
            "/api/azimut/user/getAzimutLookUpData",
            "/api/azimut/trading/incrementUserBlockage",
            "/api/azimut/trading/getUserBlockage",
            "/api/v1/token/introspect",
            "/api/paytabs/callback",
            "/api/paytabs/paymobCallback",
            "/api/paytabs/instantCallback",
            "/api/user/addUserInteraction",
            "/api/azimut/information/**",
            "/admin/authenticate",
            "/admin/forgotPassword",
            "/admin/users/verifyUserByTC",
            "/admin/users/fitsCallback",
            "/api/checkSocialIdExistence",
            "/api/user/checkReferralCode",
            "/api/directUser",
            "/api/otp/sendOtp",
            "/api/otp/verifyOtp",
            "/api/user/contract.pdf",
            "/admin/users/contract.pdf",
            "/api/azimut/user/getDocuments",
            "/api/user/getFile",
            "/api/user/getTempFile",
            "/api/enroll/getAccessToken",
            "/api/enroll/enrollCallback",
            "/swagger-ui/*",
            "/v3/api-docs",
            "/v3/api-docs/*",
            "/api/digital-registry**",
            "/api/digital-registry/**",
            "/api/partner/**", "/api/user/appVersion")
        .permitAll()
        .anyRequest().authenticated()
        .and().sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS);
    httpSecurity.addFilterBefore(jwtRequestFilter, UsernamePasswordAuthenticationFilter.class);
  }

  @Bean
  public PasswordEncoder passwordEncoder() {
    return NoOpPasswordEncoder.getInstance();
  }

  /*
   * @Bean
   * public CorsConfigurationSource corsConfigurationSource() {
   * final CorsConfiguration config = new CorsConfiguration();
   *
   * config.setAllowedOrigins(Arrays.asList("*"));
   * config.setAllowedMethods(Arrays.asList("GET", "POST", "OPTIONS", "DELETE",
   * "PUT", "PATCH"));
   * //config.setAllowCredentials(true);
   * config.setAllowedHeaders(Arrays.asList("Authorization", "Cache-Control",
   * "Content-Type"));
   *
   * final UrlBasedCorsConfigurationSource source = new
   * UrlBasedCorsConfigurationSource();
   * source.registerCorsConfiguration("/**", config);
   *
   * MyLogger.info("After origin Headers::::");
   * return source;
   * }
   */
}
