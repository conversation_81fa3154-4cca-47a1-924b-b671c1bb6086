package innovitics.azimut.models.user;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Entity
@Table(name = "gender")
@Data
public class Gender {
  @Id
  @GeneratedValue(strategy = GenerationType.AUTO)
  private Long id;
  private String genderType;
  private String possibleAcronyms;

}
