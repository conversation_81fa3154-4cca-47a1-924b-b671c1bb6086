package innovitics.azimut.rest.entities.enroll;

import java.time.Instant;

import innovitics.azimut.utilities.CustomJsonRootName;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@CustomJsonRootName(plural = "auth", singular = "auth")
public class EnrollAuthOutput {
  String token;
  Instant expirationDate;

  @Override
  public String toString() {
    return "EnrolAuthOutput [expirationDate=" + expirationDate.toString() + "]";
  }
}
