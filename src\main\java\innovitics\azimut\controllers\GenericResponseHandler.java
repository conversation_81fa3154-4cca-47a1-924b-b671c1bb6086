package innovitics.azimut.controllers;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.utilities.CustomJsonRootName;
import innovitics.azimut.utilities.datautilities.ListUtility;
import innovitics.azimut.utilities.datautilities.PaginatedEntity;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;

@Component
public class GenericResponseHandler<T> {

  @Autowired
  ListUtility<T> listUtility;

  public ResponseEntity<BaseGenericResponse<T>> generateBaseGenericResponse(Class<?> clazz, T data,
      List<T> dataList, PaginatedEntity<T> paginatedList) throws BusinessException {

    BaseGenericResponse<T> baseGenericResponse = new BaseGenericResponse<T>();

    var annotation = clazz.getAnnotation(CustomJsonRootName.class);
    String singleAnnotation = annotation == null ? clazz.getSimpleName() : annotation.singular();
    String pluralAnnotation = annotation == null ? clazz.getSimpleName() : annotation.plural();

    if (data != null) {
      Map<String, T> object = new HashMap<String, T>();
      object.put(singleAnnotation, data);
      // baseGenericResponse.setObject(object);
      baseGenericResponse.setResponse(object);
      MyLogger.info("Data Response " + object.toString());

    } else if (listUtility.isListPopulated(dataList)) {
      Map<String, List<T>> result = new HashMap<String, List<T>>();
      result.put(pluralAnnotation, dataList);
      // baseGenericResponse.setResult(result);
      baseGenericResponse.setResponse(result);
      MyLogger.info("List Response " + result.toString());
    } else if (this.listUtility.isPaginatedListPopulated(paginatedList)) {

      Map<String, PaginatedEntity<T>> page = new HashMap<String, PaginatedEntity<T>>();
      page.put(pluralAnnotation, paginatedList);
      baseGenericResponse.setResponse(page);
      MyLogger.info("Paginated Response " + page.toString());
    }

    else if (data == null && (listUtility.isListEmptyOrNull(dataList))
        && (listUtility.isPaginatedListEmpty(paginatedList))) {
      MyLogger.info("No data found:::" + baseGenericResponse.toString());
      MyLogger.info("Base Response:::" + baseGenericResponse.toString());
      throw new BusinessException(ErrorCode.NO_DATA_FOUND);
    }

    MyLogger.info("Base Response:::" + baseGenericResponse.toString());

    MyLogger.info("Data found:::");
    return new ResponseEntity<BaseGenericResponse<T>>(
        this.constructGenericBaseResponseCodeAndMessage(baseGenericResponse),
        this.validateGenericResponseSuccess(baseGenericResponse));

  }

  private BaseGenericResponse<T> constructGenericBaseResponseCodeAndMessage(BaseGenericResponse<T> baseResponse) {
    baseResponse.setMessage(StringUtility.SUCCESS);
    baseResponse.setStatus(StringUtility.SUCCESS_CODE);
    baseResponse.setTransactionId(Thread.currentThread().getName());
    MyLogger.info(baseResponse.toString());
    return baseResponse;
  }

  private HttpStatus validateGenericResponseSuccess(BaseGenericResponse<T> baseGenericResponse) {
    if (baseGenericResponse != null
        && checkIfErrorCodeInBaseGenericResponseIsSuccessful(baseGenericResponse.getStatus())) {
      return HttpStatus.OK;
    } else
      return HttpStatus.INTERNAL_SERVER_ERROR;
  }

  private boolean checkIfErrorCodeInBaseGenericResponseIsSuccessful(Integer errorCode) {
    return (errorCode != null && errorCode.intValue() == 0);
  }

}
