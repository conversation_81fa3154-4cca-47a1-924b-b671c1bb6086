package innovitics.azimut.rest.apis.enroll;

import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import innovitics.azimut.rest.entities.enroll.EnrollGetApplicationsByDateInput;
import innovitics.azimut.rest.entities.enroll.EnrollGetApplicationsOutput;

@Service
public class EnrollGetApplicationsByDate
    extends RestEnrollApiBase<EnrollGetApplicationsByDateInput, EnrollGetApplicationsOutput> {

  @Override
  protected String generateURL(String params) {
    return this.configProperties.getEnrollUrl() + "/api/v1/Applicant/GetApplicationsByDatePaged";
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.POST;
  }

  @Override
  protected Boolean showOutput() {
    return false;
  }

}
