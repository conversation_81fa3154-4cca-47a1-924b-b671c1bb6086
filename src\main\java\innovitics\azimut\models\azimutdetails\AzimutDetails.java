package innovitics.azimut.models.azimutdetails;

public class AzimutDetails {
	
	private String getDirections;
	private String contactUs;
	private String workingDays;
	private String longt;
	private String lat;
	private String workingHours;
	private String email;
	private String phoneNumber;
	private String whatsappNumber;
	private String whatsappLink;
	public String getGetDirections() {
		return getDirections;
	}
	public void setGetDirections(String getDirections) {
		this.getDirections = getDirections;
	}
	public String getContactUs() {
		return contactUs;
	}
	public void setContactUs(String contactUs) {
		this.contactUs = contactUs;
	}
	public String getWorkingDays() {
		return workingDays;
	}
	public void setWorkingDays(String workingDays) {
		this.workingDays = workingDays;
	}
	public String getLongt() {
		return longt;
	}
	public void setLongt(String longt) {
		this.longt = longt;
	}
	public String getLat() {
		return lat;
	}
	public void setLat(String lat) {
		this.lat = lat;
	}
	public String getWorkingHours() {
		return workingHours;
	}
	public void setWorkingHours(String workingHours) {
		this.workingHours = workingHours;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getPhoneNumber() {
		return phoneNumber;
	}
	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}
	public String getWhatsappNumber() {
		return whatsappNumber;
	}
	public void setWhatsappNumber(String whatsappNumber) {
		this.whatsappNumber = whatsappNumber;
	}
	public String getWhatsappLink() {
		return whatsappLink;
	}
	public void setWhatsappLink(String whatsappLink) {
		this.whatsappLink = whatsappLink;
	}
	
	
	
	
	
}
