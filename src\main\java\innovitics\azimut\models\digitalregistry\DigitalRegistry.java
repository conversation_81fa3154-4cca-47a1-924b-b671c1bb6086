package innovitics.azimut.models.digitalregistry;

import java.util.Date;

import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIncludeProperties;

import innovitics.azimut.models.user.User;
import lombok.Data;

@Entity
@Table(name = "digital_registry")
@Data
public class DigitalRegistry {
  @Id
  @GeneratedValue(strategy = GenerationType.AUTO)
  Long id;

  Date createdAt;

  @Enumerated(EnumType.STRING)
  DigitalRegistryAction action;

  String ip;

  @JsonIncludeProperties({ "id", "firstName", "lastName", "userId", "userPhone", "emailAddress", "userStep",
      "deviceId", "deviceName", "enrollApplicantId", "enrollRequestId", "teacomputersAddressEn", "idData",
      "city", "country", "dateOfBirth", "dateOfIdExpiry", "dateOfRelease", "genderId", "teacomputersClientaml",
      "teacomputersOccupation", "nickName", "isOld", "createdAt", "cso", "ntra", "aml" })
  @ManyToOne
  @JoinColumn(name = "user_id", foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT))
  User user;

  Float latitude;
  Float longitude;
  String otp;

  private String sessionId;

  public DigitalRegistry(DigitalRegistryAction action, String ip, User user, String jwtToken) {
    this.sessionId = jwtToken != null ? jwtToken.split(" ")[1] : null;
    this.action = action;
    this.ip = ip;
    this.user = user;
    this.createdAt = new Date();
  }

  public DigitalRegistry() {
  }

}
