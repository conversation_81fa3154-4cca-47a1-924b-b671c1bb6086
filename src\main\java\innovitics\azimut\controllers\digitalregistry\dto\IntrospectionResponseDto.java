package innovitics.azimut.controllers.digitalregistry.dto;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class IntrospectionResponseDto {
  boolean valid;
  @JsonProperty("national_id")
  String nationalId;
  @JsonProperty("identity_level")
  String identityLevel;
  @JsonProperty("issued_at")
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
  Date issuedAt;
  @JsonProperty("expires_at")
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
  Date expiresAt;
}
