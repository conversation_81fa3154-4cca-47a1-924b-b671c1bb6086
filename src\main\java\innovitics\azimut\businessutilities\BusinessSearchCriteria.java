package innovitics.azimut.businessutilities;

import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;

import innovitics.azimut.utilities.datautilities.StringUtility;
import lombok.Data;

@Data
public class BusinessSearchCriteria {

  private Integer pageSize;
  private Integer pageNumber;
  private Boolean asc;
  private String sortingParam;
  private SearchFilter[] searchesAndFilters;

  public PageRequest generatePaginationCriteria() {
    if (StringUtility.isStringPopulated(this.getSortingParam())) {
      if (this.getAsc() != null) {
        if (this.getAsc()) {
          Sort sort = Sort.by(this.getSortingParam()).ascending();
          return PageRequest.of(this.getPageNumber(), this.getPageSize(), sort);
        } else {
          Sort sort = Sort.by(this.getSortingParam()).descending();
          return PageRequest.of(this.getPageNumber(), this.getPageSize(), sort);
        }
      }
    }
    return PageRequest.of(this.getPageNumber(), this.getPageSize());
  }

}
