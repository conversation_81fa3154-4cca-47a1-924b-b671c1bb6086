package innovitics.azimut.models.user;

import java.util.List;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.NamedSubgraph;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Entity
@Table(name = "azimut_entities")
@Data
@NamedEntityGraph(name = "AzimutEntity.details", attributeNodes = { @NamedAttributeNode("details") }, subgraphs = {
    @NamedSubgraph(name = "AzimutDataLookup.details", attributeNodes = { @NamedAttributeNode("entity"),
        @NamedAttributeNode("entityKey"), @NamedAttributeNode("entityValue") }) })
public class AzimutEntity {
  @Id
  @GeneratedValue(strategy = GenerationType.AUTO)
  private Long id;
  private String entity;
  private Long entityType;
  @OneToMany(mappedBy = "parent")
  @Fetch(FetchMode.JOIN)
  private List<AzimutDataLookup> details;

}
