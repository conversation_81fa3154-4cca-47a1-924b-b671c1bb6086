package innovitics.azimut.security;

import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.api.gax.core.FixedCredentialsProvider;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.cloud.recaptchaenterprise.v1.RecaptchaEnterpriseServiceClient;
import com.google.cloud.recaptchaenterprise.v1.RecaptchaEnterpriseServiceSettings;
import com.google.recaptchaenterprise.v1.AccountDefenderAssessment.AccountDefenderLabel;
import com.google.recaptchaenterprise.v1.AnnotateAssessmentRequest;
import com.google.recaptchaenterprise.v1.AnnotateAssessmentRequest.Annotation;
import com.google.recaptchaenterprise.v1.AnnotateAssessmentRequest.Reason;
import com.google.recaptchaenterprise.v1.Assessment;
import com.google.recaptchaenterprise.v1.AssessmentName;
import com.google.recaptchaenterprise.v1.CreateAssessmentRequest;
import com.google.recaptchaenterprise.v1.Event;
import com.google.recaptchaenterprise.v1.ProjectName;
import com.google.recaptchaenterprise.v1.RiskAnalysis.ClassificationReason;
import com.google.recaptchaenterprise.v1.UserId;
import com.google.recaptchaenterprise.v1.UserInfo;

import innovitics.azimut.businessmodels.user.BusinessUserOTP;
import innovitics.azimut.rest.apis.recaptcha.RecaptchaAnnotateApi;
import innovitics.azimut.rest.entities.recaptcha.PhoneAuthenticationEvent;
import innovitics.azimut.rest.entities.recaptcha.RecaptchaAnnotateRequest;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class RecaptchaUtility {
  RecaptchaEnterpriseServiceClient client;
  GoogleCredentials credentialsRecaptcha;
  Set<String> whitelistSet;

  @Autowired
  RecaptchaAnnotateApi recaptchaAnnotateApi;

  public RecaptchaUtility() throws IOException {
    // Load the service account key from resources
    InputStream credentialsStream = RecaptchaUtility.class.getClassLoader()
        .getResourceAsStream("azimut-77319-12bf0f18edfd.json");

    // Load credentials from the InputStream
    GoogleCredentials credentials = GoogleCredentials.fromStream(credentialsStream);

    String[] whitelist = { "+***********" };
    this.whitelistSet = new HashSet<>(Arrays.asList(whitelist));

    this.credentialsRecaptcha = credentials.createScoped("https://www.googleapis.com/auth/cloud-platform");

    //
    // Create RecaptchaEnterpriseServiceSettings with custom credentials
    RecaptchaEnterpriseServiceSettings recaptchaSettings = RecaptchaEnterpriseServiceSettings.newBuilder()
        .setCredentialsProvider(FixedCredentialsProvider.create(credentials))
        .build();

    // Create the reCAPTCHA client.
    this.client = RecaptchaEnterpriseServiceClient.create(recaptchaSettings);
    client.getSettings().getHeaderProvider().getHeaders();
  }

  public RecaptchaResult createAssessment(String token, String platform, String id, String recaptchaAction,
      Boolean isPhone) {
    return createAssessment(token, platform, id, recaptchaAction, isPhone, id.startsWith("+20"));
  }

  public RecaptchaResult createAssessment(String token, String platform, String id, String recaptchaAction,
      Boolean isPhone, Boolean isEgypt) {
    String recaptchaKeyWeb = "6Leb5mEqAAAAAEph9CY4EFc2jae8T-9KbjUNEeIG";
    String recaptchaKeyAndroid = "6Lf6EWIqAAAAAJms1ANlzkbd_xrMU0XaBODHf-WK";
    String recaptchaKeyIos = "6Lc4_2EqAAAAAHq6uKVFRwyBykFgBJdOfPh9hjTE";
    if (platform == null) {
      return null;
    } else
      switch (platform) {
        case "web":
          return createAssessmentInternal(token, recaptchaKeyWeb, id, recaptchaAction, isPhone, isEgypt);
        case "android":
          return createAssessmentInternal(token, recaptchaKeyAndroid, id, recaptchaAction, isPhone, isEgypt);
        case "ios":
          return createAssessmentInternal(token, recaptchaKeyIos, id, recaptchaAction, isPhone, isEgypt);
        default:
          return null;
      }
  }

  public RecaptchaResult createAssessmentInternal(String token, String recaptchaKey, String phoneNumber,
      String recaptchaAction, Boolean isPhone, boolean isEgypt) {

    var assessmentRes = new RecaptchaResult();
    if (this.whitelistSet.contains(phoneNumber)) {
      assessmentRes.setResponse(true);
      return assessmentRes;
    }
    String projectID = "azimut-77319";

    // Set the properties of the event to be tracked.
    var infoBuilder = UserInfo.newBuilder()
        .setAccountId(phoneNumber);

    if (isPhone)
      infoBuilder.addUserIds(UserId.newBuilder().setPhoneNumber(phoneNumber));
    Event event = Event.newBuilder().setSiteKey(recaptchaKey).setToken(token).setUserInfo(infoBuilder).build();

    // Build the assessment request.
    CreateAssessmentRequest createAssessmentRequest = CreateAssessmentRequest.newBuilder()
        .setParent(ProjectName.of(projectID).toString())
        .setAssessment(Assessment.newBuilder().setEvent(event).build())
        .build();

    Assessment response = client.createAssessment(createAssessmentRequest);

    // Check if the token is valid.
    if (!response.getTokenProperties().getValid()) {
      MyLogger.info(
          "The CreateAssessment call failed because the token was: "
              + response.getTokenProperties().getInvalidReason().name());
      assessmentRes.setResponse(false);
      return assessmentRes;
    }

    // Check if the expected action was executed.
    if (!response.getTokenProperties().getAction().equals(recaptchaAction)) {
      MyLogger.info(
          "The action attribute in reCAPTCHA tag is: "
              + response.getTokenProperties().getAction());
      MyLogger.info(
          "The action attribute in the reCAPTCHA tag "
              + "does not match the action ("
              + recaptchaAction
              + ") you are expecting to score");
      assessmentRes.setResponse(false);
      return assessmentRes;
    }

    MyLogger.info(response.toString());
    // Get the reason(s) and the reCAPTCHA risk score.
    // For more information on interpreting the assessment,
    // see: https://cloud.google.com/recaptcha-enterprise/docs/interpret-assessment
    for (ClassificationReason reason : response.getRiskAnalysis().getReasonsList()) {
      MyLogger.info(reason.toString());
    }
    String assessmentName = response.getName();
    assessmentName = assessmentName.substring(assessmentName.lastIndexOf("/") + 1);
    assessmentRes.setAssessmentId(assessmentName);
    // Get the Account Defender result.
    var accountDefenderAssessment = response.getAccountDefenderAssessment();
    MyLogger.info(accountDefenderAssessment.toString());

    // Get Account Defender label.
    var defenderResult = response.getAccountDefenderAssessment().getLabelsList();
    for (var defenderLabel : defenderResult) {
      if (!isEgypt && (defenderLabel.equals(AccountDefenderLabel.RELATED_ACCOUNTS_NUMBER_HIGH) ||
          defenderLabel.equals(AccountDefenderLabel.SUSPICIOUS_ACCOUNT_CREATION) ||
          defenderLabel.equals(AccountDefenderLabel.SUSPICIOUS_LOGIN_ACTIVITY))) {
        MyLogger.info("AccountDefenderLabel is suspicious");
        assessmentRes.setResponse(false);
        return assessmentRes;
      }
    }
    // Based on the result, can you choose next steps.
    // If the 'defenderResult' field is empty, it indicates that Account Defender
    // did not have
    // anything to add to the score.
    // Few result labels: ACCOUNT_DEFENDER_LABEL_UNSPECIFIED, PROFILE_MATCH,
    // SUSPICIOUS_LOGIN_ACTIVITY, SUSPICIOUS_ACCOUNT_CREATION,
    // RELATED_ACCOUNTS_NUMBER_HIGH.
    // For more information on interpreting the assessment, see:
    // https://cloud.google.com/recaptcha-enterprise/docs/account-defender#interpret-assessment-details
    MyLogger.info("Account Defender Assessment Result: " + Arrays.toString(defenderResult.toArray()));

    MyLogger.info("riskAssessment score " + response.getRiskAnalysis().getScore());
    var threshold = isEgypt ? 0.2 : 0.7;
    // response.getRiskAnalysis().getScore()
    // The score 1.0 indicates that the interaction poses low risk and is very
    // likely legitimate,
    // whereas 0.0 indicates that the interaction poses high risk and might be
    // fraudulent.
    // https://cloud.google.com/recaptcha/docs/interpret-assessment-website
    if (response.getRiskAnalysis().getScore() < threshold) {
      MyLogger
          .info("riskAssessment score " + response.getRiskAnalysis().getScore() + " less than threshold " + threshold);
      assessmentRes.setResponse(false);
      return assessmentRes;
    }
    // response.getPhoneFraudAssessment().getSmsTollFraudVerdict()
    // The higher the score, the more likely the phone number is risky;
    // the lower the score, the more likely the phone number is legitimate
    // https://cloud.google.com/recaptcha/docs/sms-fraud-detection
    var smsThreshold = isEgypt ? 0.8 : 0.33;
    if (!isEgypt && response.getPhoneFraudAssessment() != null &&
        response.getPhoneFraudAssessment().getSmsTollFraudVerdict() != null &&
        response.getPhoneFraudAssessment().getSmsTollFraudVerdict().getRisk() > smsThreshold &&
        !defenderResult.contains(AccountDefenderLabel.PROFILE_MATCH)) {
      MyLogger.info("sms Toll risk score " + response.getPhoneFraudAssessment().getSmsTollFraudVerdict().getRisk()
          + " more than threshold " + smsThreshold);
      assessmentRes.setResponse(false);
      return assessmentRes;
    }
    if (StringUtility.stringsMatch("TRIGGER_MFA", recaptchaAction))
      annotateInitiate2FA(phoneNumber, assessmentName);
    assessmentRes.setResponse(true);
    return assessmentRes;
  }

  public void annotateOTPAssessment(BusinessUserOTP otp, boolean isLegitimate) {
    String projectID = "azimut-77319";
    String accountId = otp.getUserPhone();
    String fullAssessmentId = "projects/" + projectID + "/assessments/" + otp.getAssessmentId();
    RecaptchaAnnotateRequest req = new RecaptchaAnnotateRequest();
    req.setAccountId(accountId);
    req.setAnnotation(isLegitimate ? Annotation.LEGITIMATE.name() : Annotation.FRAUDULENT.name());
    String[] reasons = { isLegitimate ? Reason.PASSED_TWO_FACTOR.name() : Reason.FAILED_TWO_FACTOR.name() };
    req.setReasons(reasons);
    PhoneAuthenticationEvent phoneAuthenticationEvent = new PhoneAuthenticationEvent();
    phoneAuthenticationEvent.setPhoneNumber(otp.getUserPhone());
    req.setPhoneAuthenticationEvent(phoneAuthenticationEvent);
    try {
      credentialsRecaptcha.refreshIfExpired();
      var token = credentialsRecaptcha.getAccessToken();
      recaptchaAnnotateApi.setToken(token.getTokenValue());
      String response = recaptchaAnnotateApi.getData(req, fullAssessmentId);
      MyLogger.info("Annotated OTP response sent successfully ! " + response);
    } catch (Exception e) {
      MyLogger.info("Failed to Annotate OTP");
      MyLogger.logStackTrace(e);
    }
    // Build the annotation request.
    // For more info on when/how to annotate, see:
    // https://cloud.google.com/recaptcha-enterprise/docs/annotate-assessment#when_to_annotate
    // AnnotateAssessmentRequest annotateAssessmentRequest =
    // AnnotateAssessmentRequest.newBuilder()
    // .setName(AssessmentName.of(projectID, otp.getAssessmentId()).toString())
    // .setAnnotation(isLegitimate ? Annotation.LEGITIMATE : Annotation.FRAUDULENT)
    // .addReasons(isLegitimate ? Reason.PASSED_TWO_FACTOR :
    // Reason.FAILED_TWO_FACTOR)
    // .setAccountId(accountId)
    // .build();

    // // Empty response is sent back.
    // var response = client.annotateAssessment(annotateAssessmentRequest);
    // MyLogger.info("Annotated OTP response sent successfully ! " + response);
  }

  public void annotateInitiate2FA(String userPhone, String assessmentId) {
    String projectID = "azimut-77319";
    String fullAssessmentId = "projects/" + projectID + "/assessments/" + assessmentId;
    RecaptchaAnnotateRequest req = new RecaptchaAnnotateRequest();
    String accountId = userPhone;
    req.setAccountId(accountId);
    String[] reasons = { Reason.INITIATED_TWO_FACTOR.name() };
    req.setReasons(reasons);
    PhoneAuthenticationEvent phoneAuthenticationEvent = new PhoneAuthenticationEvent();
    phoneAuthenticationEvent.setPhoneNumber(userPhone);
    req.setPhoneAuthenticationEvent(phoneAuthenticationEvent);
    try {
      credentialsRecaptcha.refreshIfExpired();
      var token = credentialsRecaptcha.getAccessToken();
      recaptchaAnnotateApi.setToken(token.getTokenValue());
      String response = recaptchaAnnotateApi.getData(req, fullAssessmentId);
      MyLogger.info("Annotated Init 2FA response sent successfully ! " + response);
    } catch (Exception e) {
      MyLogger.info("Failed to Annotate Init 2FA ");
      MyLogger.logStackTrace(e);
    }
    // Build the annotation request.
    // For more info on when/how to annotate, see:
    // https://cloud.google.com/recaptcha-enterprise/docs/annotate-assessment#when_to_annotate
    // AnnotateAssessmentRequest annotateAssessmentRequest =
    // AnnotateAssessmentRequest.newBuilder()
    // .setName(AssessmentName.of(projectID, assessmentId).toString())
    // .setAnnotation(Annotation.LEGITIMATE)
    // .addReasons(Reason.INITIATED_TWO_FACTOR)
    // .setAccountId(accountId)
    // .build();

    // // Empty response is sent back.
    // var response = client.annotateAssessment(annotateAssessmentRequest);
    // MyLogger.info("Annotated Init 2FA response sent successfully ! " + response);
  }

  public void annotateLoginAssessment(String userPhone, String assessmentId, boolean isLegitimate) {
    String projectID = "azimut-77319";
    String accountId = userPhone;
    // Build the annotation request.
    // For more info on when/how to annotate, see:
    // https://cloud.google.com/recaptcha-enterprise/docs/annotate-assessment#when_to_annotate
    AnnotateAssessmentRequest annotateAssessmentRequest = AnnotateAssessmentRequest.newBuilder()
        .setName(AssessmentName.of(projectID, assessmentId).toString())
        .setAnnotation(isLegitimate ? Annotation.LEGITIMATE : Annotation.FRAUDULENT)
        .addReasons(isLegitimate ? Reason.CORRECT_PASSWORD : Reason.INCORRECT_PASSWORD)
        .setAccountId(accountId)
        .build();

    // Empty response is sent back.
    var response = client.annotateAssessment(annotateAssessmentRequest);
    MyLogger.info("Annotated Login response sent successfully ! " + response);
  }
}
