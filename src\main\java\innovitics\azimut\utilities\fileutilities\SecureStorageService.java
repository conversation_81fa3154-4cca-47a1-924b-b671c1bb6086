package innovitics.azimut.utilities.fileutilities;

import java.io.IOException;
import java.io.InputStream;
import java.util.Map.Entry;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.repositories.DBConfigurationRepository;
import innovitics.azimut.security.AES;
import innovitics.azimut.security.FileTokenizationUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;

@Component
public class SecureStorageService implements ISecureStorage {
  @Autowired
  DiskStorageUtility diskStorageUtility;
  @Autowired
  FileTokenizationUtility fileTokenizationUtility;
  @Autowired
  FileRetrievalServiceImpl fileRetrievalService;
  @Autowired
  AES aes;
  @Autowired
  DBConfigurationRepository dbConfigurationRepository;

  @Value("${is.production}")
  private boolean isProduction;

  @Override
  public BlobData uploadFile(MultipartFile file, boolean generateSasToken, String containerName, String subDirectory,
      boolean useOriginalFileName) throws BusinessException, IOException {
    return getStorageType().uploadFile(file, generateSasToken, containerName, subDirectory, useOriginalFileName);
  }

  @Override
  public BlobData uploadFile(MultipartFile file, boolean generateSasToken, String containerName, String subDirectory,
      boolean useOriginalFileName, Object... options) throws BusinessException, IOException {
    return getStorageType().uploadFile(file, generateSasToken, containerName, subDirectory, useOriginalFileName,
        options);
  }

  @Override
  public String generateFileRetrievalUrl(String path, String fileName, String subDirectory, boolean generateWithToken,
      Object... options) throws IOException {
    return getStorageType().generateFileRetrievalUrl(path, fileName, subDirectory, generateWithToken);
  }

  @Override
  public String generateFileRetrievalUrl(String path, String fileName, String subDirectory, boolean generateWithToken,
      Long tokenValidityMinutes, Object... options) throws IOException {
    return getStorageType().generateFileRetrievalUrl(path, fileName, subDirectory, generateWithToken,
        tokenValidityMinutes, options);
  }

  @Override
  public void deleteFile(String path, String fileName, String subDirectory, boolean generateWithToken,
      Long tokenValidityInMinutes) throws IOException {
    this.getStorageType().deleteFile(path, fileName, subDirectory, generateWithToken, tokenValidityInMinutes);
  }

  @Override
  public void copyFile(String sourceContainerName, String destinationContainerName, String subDirectory,
      String fileName, boolean generateSasToken) throws IOException {
    this.getStorageType().copyFile(sourceContainerName, destinationContainerName, subDirectory, fileName, false);
  }

  @Override
  public String generateLocalPath(String containerName, String subDirectory, String fileName, boolean generateSasToken)
      throws IOException {
    return this.getStorageType().generateLocalPath(containerName, fileName, subDirectory, generateSasToken, null);
  }

  @Override
  public void copyWithAbsolutePath(String sourcePath, String destinationPath) throws IOException {
    // TODO Auto-generated method stub

  }

  public ParentStorage getStorageType() {
    return this.diskStorageUtility;
  }

  public boolean validateTempFileToken(String token, String fileLocation, String fileName) {
    return this.fileTokenizationUtility.validateToken(token, this.getStorageType().decrypt(fileLocation),
        this.getStorageType().decrypt(fileName));
  }

  public boolean validateFileToken(String token, String documentType, String documentId) {
    return this.fileTokenizationUtility.validateToken(token, this.getStorageType().decrypt(documentType),
        this.getStorageType().decrypt(documentId));
  }

  @Override
  public IFileRetrievalService setFileRetrievalService() {
    return this.fileRetrievalService;
  }

  @Override
  public Entry<MediaType, byte[]> getFileWithAbsolutePath(String path) throws IOException {

    return this.getStorageType().getFileWithAbsolutePath(this.getStorageType().decrypt(path));
  }

  @Override
  public Entry<MediaType, byte[]> getFileUsingIdAndType(BusinessUser businessUser, String validityToken,
      String documentId, String documentType) throws ClassNotFoundException, BusinessException, IOException {
    Long longDocumentId = StringUtility.isStringPopulated(documentId)
        ? Long.valueOf(this.getStorageType().decrypt(documentId))
        : null;
    return this.setFileRetrievalService().getFile(this.getStorageType(), businessUser, validityToken, longDocumentId,
        this.getStorageType().decrypt(documentType));
  }

  @Override
  public BlobData uploadFileToBlob(InputStream inputStream, boolean generateSasToken, String containerName,
      String subDirectory, String extension) throws IOException, BusinessException {
    return this.getStorageType().uploadFileToBlob(inputStream, generateSasToken, containerName, subDirectory,
        extension);
  }

  @Override
  public void emptySubDirectory(String container, String subDiectory) throws IOException, BusinessException {
    this.getStorageType().emptySubDirectory(container, subDiectory);

  }

  @Override
  public BlobData uploadFileToBlob(String fileName, InputStream inputStream, boolean generateSasToken,
      String containerName, String subDirectory, String extension) throws IOException, BusinessException {
    return this.getStorageType().uploadFileToBlob(fileName, inputStream, generateSasToken, containerName, subDirectory,
        extension);
  }

  public String getEncryptionOrDecryption(String value, boolean choice) {
    if (choice)
      return this.aes.encrypt(value);
    else
      return this.aes.decrypt(value);
  }

}
