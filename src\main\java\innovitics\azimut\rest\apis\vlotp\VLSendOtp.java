package innovitics.azimut.rest.apis.vlotp;

import java.util.Date;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.rest.RestBaseApi;
import innovitics.azimut.rest.entities.vl.VLOtpRequest;
import innovitics.azimut.rest.entities.vl.VLResponse;
import innovitics.azimut.utilities.datautilities.NumberUtility;

@Service
public class VLSendOtp extends RestBaseApi<VLOtpRequest, VLResponse> {

  @Override
  protected String generateURL(String params) {
    return this.configProperties.getVlUrl() + "/PinRequestAPI/api/PinManager/RequestPIN";
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.POST;
  }

  @Override
  protected HttpEntity<?> generateRequestFromInput(VLOtpRequest input) {
    input.setUsername(this.configProperties.getVlUsername());
    input.setPassword(this.configProperties.getVlPassword());
    input.setService(this.configProperties.getVlService());
    return this.stringify(input);
  };

  @Override
  protected void validateResponse(ResponseEntity<VLResponse> responseEntity) throws IntegrationException {
    super.validateResponse(responseEntity);
    if (!NumberUtility.areIntegerValuesMatching(responseEntity.getBody().getCode(), 0)) {
      IntegrationException integrationException = new IntegrationException(responseEntity.getBody().getCode(),
          new Date(), responseEntity.getBody().getMessage(), null,
          responseEntity.getBody().getMessage(), null);
      throw integrationException;
    }
  }

}
