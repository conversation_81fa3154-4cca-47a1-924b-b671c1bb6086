package innovitics.azimut.controllers.azimut;

import java.io.IOException;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import innovitics.azimut.businessmodels.BusinessAzimutInformationType;
import innovitics.azimut.businessservices.BusinessAzimutInformationService;
import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.controllers.GenericResponseHandler;
import innovitics.azimut.controllers.users.DTOs.GetByIdDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.utilities.datautilities.StringUtility;

@RestController
@RequestMapping(value = "/api/azimut/information", produces = {
    MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE })
public class AzimutInformationController {

  @Autowired
  BusinessAzimutInformationService businessAzimutInformationService;

  @Autowired
  GenericResponseHandler<BusinessAzimutInformationType> businessAzimutInformationResponseHandler;

  @GetMapping(value = "/getAll")
  protected ResponseEntity<BaseGenericResponse<BusinessAzimutInformationType>> getAll(
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language)
      throws BusinessException, IOException, IntegrationException {
    return businessAzimutInformationResponseHandler.generateBaseGenericResponse(BusinessAzimutInformationType.class,
        null,
        this.businessAzimutInformationService.getAzimutInformations(language), null);

  }

  @PostMapping(value = "/getById", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessAzimutInformationType>> getById(
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody GetByIdDto businessAzimutInformationType)
      throws BusinessException, IOException, IntegrationException {
    return businessAzimutInformationResponseHandler.generateBaseGenericResponse(BusinessAzimutInformationType.class,
        this.businessAzimutInformationService.getById(businessAzimutInformationType.getId(), language), null, null);

  }
}
