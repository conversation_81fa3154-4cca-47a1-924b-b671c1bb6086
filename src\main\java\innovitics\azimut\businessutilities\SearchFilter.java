package innovitics.azimut.businessutilities;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import lombok.Data;

@Data
public class SearchFilter {

  public final static Logger logger = LogManager.getLogger(SearchFilter.class.getName());
  private String key;
  private Object[] values;
  private String value;
  private Boolean search;
  private String operation;
  private String parentColumn;
  private Long id;

  public String getKey() {
    return key;
  }

  public SearchFilter(String key, String operation, String value, Object[] values,
      String parentColumn, Boolean search) {
    super();
    this.key = key;
    this.values = values;
    this.value = value;
    this.search = search;
    this.operation = operation;
    this.parentColumn = parentColumn;
  }

}
