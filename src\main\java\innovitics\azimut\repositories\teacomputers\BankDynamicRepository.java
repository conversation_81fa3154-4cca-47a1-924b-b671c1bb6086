package innovitics.azimut.repositories.teacomputers;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;

import innovitics.azimut.models.teacomputers.Bank;

@Repository
public interface BankDynamicRepository extends JpaRepository<Bank, Long>, EntityGraphJpaSpecificationExecutor<Bank> {
  public Bank findByBankId(Long bankId);
}
