package innovitics.azimut.utilities.businessutilities;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import innovitics.azimut.businessmodels.admin.BusinessAdminUser;
import innovitics.azimut.businessmodels.kyc.BusinessReview;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.controllers.admin.DTOs.GetReviewsDto;
import innovitics.azimut.controllers.admin.DTOs.ReasonDto;
import innovitics.azimut.controllers.users.DTOs.EditUserAndSubmitReviewDto;
import innovitics.azimut.models.kyc.Reason;
import innovitics.azimut.models.kyc.Review;
import innovitics.azimut.services.kyc.QuestionService;
import innovitics.azimut.services.kyc.ReasonService;
import innovitics.azimut.services.kyc.ReviewService;
import innovitics.azimut.services.kyc.UserAnswerSubmissionService;
import innovitics.azimut.utilities.ParentUtility;
import innovitics.azimut.utilities.crosslayerenums.KycStatus;
import innovitics.azimut.utilities.crosslayerenums.ReviewResult;
import innovitics.azimut.utilities.crosslayerenums.UserStep;
import innovitics.azimut.utilities.datautilities.ArrayUtility;
import innovitics.azimut.utilities.datautilities.ListUtility;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.datautilities.UserTypeUtility;
import innovitics.azimut.utilities.logging.MyLogger;
import innovitics.azimut.utilities.mapping.ReviewMapper;

@Component
public class ReviewUtility extends ParentUtility {

  @Autowired
  public ReviewService reviewService;
  @Autowired
  ListUtility<BusinessReview> businessReviewListUtility;
  @Autowired
  public ListUtility<Review> reviewListUtility;
  @Autowired
  ListUtility<Reason> reasonListUtility;
  @Autowired
  ListUtility<Long> longListUtility;
  @Autowired
  UserAnswerSubmissionService userAnswerSubmissionService;
  @Autowired
  ReasonService reasonService;
  @Autowired
  ReviewMapper reviewMapper;
  @Autowired
  AzimutDataLookupUtility azimutDataLookupUtility;
  @Autowired
  ArrayUtility arrayUtility;
  @Autowired
  QuestionService questionService;

  public void addReviews(BusinessAdminUser businessAdminUser, EditUserAndSubmitReviewDto baseBusinessEntity,
      String language, BusinessUser businessUser) {
    if (baseBusinessEntity.getPageId() != null)
      reviewService.deleteOldReviews(baseBusinessEntity.getPageId(), baseBusinessEntity.getAppUserId());
    else
      reviewService.deleteOldReviews(baseBusinessEntity.getAppUserId());

    List<Review> reviews = new ArrayList<Review>();
    for (BusinessReview businessReview : baseBusinessEntity.getReviews()) {
      Review review = new Review();
      review.setActionMaker(businessAdminUser.getId());
      review.setPageId(baseBusinessEntity.getPageId());
      review.setPageOrder(baseBusinessEntity.getPageOrder());
      review.setUserId(baseBusinessEntity.getAppUserId());
      reviews.add(this.convertBusinessReviewToReview(businessReview, review, language));
    }
    reviewService.submitReviews(reviews);
    this.calculateUserKycStatus(businessUser, userAnswerSubmissionService, language);
  }

  public List<Review> getReviews(GetReviewsDto baseBusinessEntity) {
    return reviewService.getReviewsByUserId(baseBusinessEntity.getAppUserId(), baseBusinessEntity.getPageId());
  }

  public List<Review> getTotalKYCReviewsByUserId(Long userId) {
    return reviewService.getKYCReviewsByUserId(userId);
  }

  public List<Review> getAllKYCReviewsByUserId(Long userId) {
    return reviewService.getAllReviewsByUserId(userId);
  }

  public List<Review> getReviewsByReasonId(ReasonDto businessReview) {
    return reviewService.getReviewsByReasonId(businessReview.getReason().getId());
  }

  public List<Review> getReviews(Long userId, Long pageId) {
    return reviewService.getReviewsByUserId(userId, pageId);
  }

  public List<Review> getFraReviewsByUserId(Long userId) {
    return reviewService.getFraReviewsByUserId(userId);
  }

  public List<Review> getAllUserReviews(Long userId) {
    return reviewService.getReviewsByUserId(userId);
  }

  public List<Review> getAnyRejectedReviews(Long userId) {
    return this.reviewService.getAnyRejectedReviewsByUserId(userId);
  }

  public boolean isListPopulated(List<Review> reviews) {
    return this.reviewListUtility.isListPopulated(reviews);
  }

  public BusinessReview convertReviewToBusinessReview(Review review, String language) {
    BusinessReview businessReview = new BusinessReview();
    if (review != null) {
      businessReview.setId(review.getId());
      businessReview.setQuestionId(review.getQuestionId());
      if (review.getReason() != null) {
        businessReview.setReason(review.getReason());
        businessReview.setReasonId(review.getReason().getId());
      }
      businessReview.setCreatedAt(review.getCreatedAt());
      businessReview.setPageId(review.getPageId());
      businessReview.setAppUserId(review.getAppUserId());
      businessReview.setActionMaker(review.getActionMaker());
      businessReview.setStatus(review.getResult());
      businessReview.setComment(review.getComment());
      businessReview.setAccountId(review.getAccountId());
      businessReview.setMandatoryQuestion(review.getMandatoryQuestion());
    }
    return businessReview;

  }

  public Review convertBusinessReviewToReview(BusinessReview businessReview, Review review, String language) {
    if (businessReview != null) {
      MyLogger.info("Business Review::" + businessReview.toString());
      review.setQuestionId(businessReview.getQuestionId());
      if (businessReview.getReasonId() != null) {
        Reason reason = new Reason();
        reason.setId(businessReview.getReasonId());
        review.setReason(reason);
      }
      review.setResult(businessReview.getStatus());
      review.setCreatedAt(new Date());
      review.setComment(businessReview.getComment());
      review.setAccountId(businessReview.getBankAccountId());
      review.setMandatoryQuestion(businessReview.getMandatoryQuestion());
      MyLogger.info("Review::" + review.toString());
    }
    return review;

  }

  public Reason[] findAllReasons() {
    List<Reason> reasonList = this.reasonService.findAll();
    Reason[] reasons = new Reason[reasonList.size()];
    reasonList.toArray(reasons);
    return reasons;
  }

  public Reason[] findAllReasonsByKeyword(String keyword, String language) {
    List<Reason> reasonList = this.reasonService.findByKeyword(keyword, language);
    if (this.reasonListUtility.isListPopulated(reasonList)) {
      Reason[] reasons = new Reason[reasonList.size()];
      reasonList.toArray(reasons);
      return reasons;
    } else {
      Reason[] reasons = new Reason[0];
      return reasons;
    }

  }

  public void addReason(BusinessAdminUser businessAdminUser, ReasonDto businessReview) {
    if (businessReview != null && businessReview.getReason() != null) {
      Reason reason = businessReview.getReason();
      reason.setAddedBy(businessAdminUser.getId());
      reason.setCreatedAt(new Date());
      this.reasonService.save(reason);
    }
  }

  public void editReason(BusinessAdminUser businessAdminUser, ReasonDto businessReview) {
    if (businessReview != null && businessReview.getReason() != null) {
      Reason reason = businessReview.getReason();
      reason.setEditedBy(businessAdminUser.getId());
      reason.setEditedAt(new Date());
      this.reasonService.save(reason);
    }
  }

  public void deleteReason(BusinessAdminUser businessAdminUser, ReasonDto businessReview) {
    if (businessReview != null && businessReview.getReason() != null) {
      Reason reason = businessReview.getReason();
      this.reasonService.delete(reason);
    }
  }

  public void softDeleteReview(BusinessReview businessReview) {
    Review review = new Review();
    review.setId(businessReview.getId());
    review.setQuestionId(businessReview.getQuestionId());
    review.setPageId(businessReview.getPageId());
    review.setPageOrder(businessReview.getPageOrder());
    review.setComment(businessReview.getComment());
    review.setResult(businessReview.getStatus());
    if (businessReview.getReason() != null) {
      Reason reason = new Reason();
      reason.setId(businessReview.getReason().getId());
      review.setReason(reason);
    }
    this.reviewService.removeReview(review);
  }

  public void softDeleteReviews(List<BusinessReview> businessReviews) {
    List<Review> reviews = new ArrayList<Review>();

    if (businessReviewListUtility.isListPopulated(businessReviews)) {
      for (BusinessReview businessReview : businessReviews) {
        reviews.add(this.reviewMapper.convertBusinessUnitToBasicUnit(businessReview, false));
      }
      this.reviewService.removeOldReviews(reviews);
    }
  }

  public void softDeleteReviewsUsingIds(List<Long> businessReviewIds) {
    this.reviewService.softDeleteBulkReviews(businessReviewIds);
  }

  public List<BusinessReview> getFirstReview(List<Review> reviews, String language) {
    List<BusinessReview> businessReviews = new ArrayList<BusinessReview>();
    if (reviewListUtility.isListPopulated(reviews)) {

      businessReviews.add(this.convertReviewToBusinessReview(reviews.get(0), language));
    }
    return businessReviews;
  }

  public List<BusinessReview> convertBasicListToBusinessList(List<Review> reviews, String language) {
    List<BusinessReview> businessReviews = new ArrayList<BusinessReview>();
    if (reviewListUtility.isListPopulated(reviews)) {
      for (Review review : reviews) {
        businessReviews.add(this.convertReviewToBusinessReview(review, language));
      }
    }
    return businessReviews;
  }

  public void calculateUserKycStatus(BusinessUser businessUser, UserAnswerSubmissionService userAnswerSubmissionService,
      String language) {
    boolean areUserDetailsRejected = false;
    boolean areClientBankAccountsRejected = false;
    boolean areKycPagesRejected = false;

    boolean areUserDetailsPending = false;
    boolean areClientBankPending = false;
    boolean areKycPagesPending = false;
    boolean noPendingReviews = false;
    List<BusinessReview> userDetailsReviews = new ArrayList<BusinessReview>();
    List<BusinessReview> clientBankAccountsReviews = new ArrayList<BusinessReview>();
    List<BusinessReview> kycReviews = new ArrayList<BusinessReview>();

    List<BusinessReview> totalBusinessReviews = this.getAllUserReviews(businessUser.getId(), language);

    if (businessReviewListUtility.isListPopulated(totalBusinessReviews)) {
      for (BusinessReview businessReview : totalBusinessReviews) {
        if (businessReview != null) {
          if (businessReview.getPageId() == null)
            userDetailsReviews.add(businessReview);
          else if (businessReview.getPageId() != null
              && NumberUtility.areLongValuesMatching(0l, businessReview.getPageId()))
            clientBankAccountsReviews.add(businessReview);
          else if (businessReview.getPageId() != null
              && !NumberUtility.areLongValuesMatching(0l, businessReview.getPageId()))
            kycReviews.add(businessReview);
        }
      }
    }
    areUserDetailsPending = this.isPending(userDetailsReviews);
    areClientBankPending = this.arrayUtility
        .isArrayPopulated(this.azimutDataLookupUtility.getKYCClientBankAccountData(businessUser))
        && this.isPending(clientBankAccountsReviews);
    areKycPagesPending = this.isPending(kycReviews);

    boolean kycReviewFinished = false;
    kycReviewFinished = this.isKycReviewFinished(businessUser, kycReviews, userAnswerSubmissionService);

    MyLogger.info("areUserDetailsPending" + areUserDetailsPending);
    MyLogger.info("areClientBankPending" + areClientBankPending);
    MyLogger.info("areKycPagesPending" + areKycPagesPending);
    MyLogger.info("kycReviewFinished" + kycReviewFinished);

    if (areUserDetailsPending || areClientBankPending || areKycPagesPending || !kycReviewFinished) {
      businessUser.setKycStatus(KycStatus.PENDING.getStatusId());
      businessUser.setUserStep(UserStep.UNDER_REVIEW.getStepId());
      return;
    }
    noPendingReviews = !areUserDetailsPending && !areClientBankPending && !areKycPagesPending && kycReviewFinished;

    areUserDetailsRejected = this.doesReviewListContainRejection(userDetailsReviews);
    areClientBankAccountsRejected = this.doesReviewListContainRejection(clientBankAccountsReviews);
    areKycPagesRejected = this.doesReviewListContainRejection(kycReviews);

    MyLogger.info("areUserDetailsRejected" + areClientBankAccountsRejected);
    MyLogger.info("areClientBankAccountsRejected" + areClientBankAccountsRejected);
    MyLogger.info("areKycPagesRejected" + areKycPagesRejected);
    MyLogger.info("noPendingReviews" + noPendingReviews);

    if (noPendingReviews && (areUserDetailsRejected || areClientBankAccountsRejected || areKycPagesRejected)) {
      businessUser.setKycStatus(KycStatus.REJECTED.getStatusId());
      businessUser.setUserStep(UserStep.UNDER_REVIEW.getStepId());
      businessUser.setIsVerified(null); // if we want to keep user verified but rejected, remove this line
      return;
    }
    businessUser.setKycStatus(KycStatus.APPROVED.getStatusId());
    businessUser.setUserStep(UserStep.REVIEWED.getStepId());

    return;

  }

  List<BusinessReview> getAllUserReviews(Long userId, String language) {
    List<Review> reviews = this.getAllUserReviews(userId);
    return this.convertBasicListToBusinessList(reviews, language);
  }

  List<BusinessReview> getKycReviews(Long userId, String language) {
    List<Review> reviews = this.getTotalKYCReviewsByUserId(userId);
    return this.convertBasicListToBusinessList(reviews, language);
  }

  public List<BusinessReview> getAllKycReviews(Long userId, String language) {
    List<Review> reviews = this.getAllKYCReviewsByUserId(userId);
    return this.convertBasicListToBusinessList(reviews, language);
  }

  void checkListPopulation(boolean value, List<BusinessReview> businessReviews) {
    if (businessReviewListUtility.isListPopulated(businessReviews)) {
      value = true;
    }
  }

  public boolean doesReviewListContainRejection(List<BusinessReview> businessReviews) {
    boolean result = false;
    if (businessReviewListUtility.isListPopulated(businessReviews)) {
      for (BusinessReview businessReview : businessReviews) {
        if (NumberUtility.areLongValuesMatching(businessReview.getStatus(), ReviewResult.REJECTED.getResultId())) {
          result = true;
          break;
        }
      }
    } else {
      result = false;
    }
    MyLogger.info("doesReviewListContainRejection?::" + result);
    return result;
  }

  boolean isPending(List<BusinessReview> businessReviews) {
    return businessReviewListUtility.isListEmptyOrNull(businessReviews);
  }

  boolean isKycReviewFinished(BusinessUser businessUser, List<BusinessReview> kycReviews,
      UserAnswerSubmissionService userAnswerSubmissionService) {
    boolean result = false;

    List<Long> distinctAnsweredQuestionIds = userAnswerSubmissionService
        .getDistinctQuestionIdsByUserId(businessUser.getId());
    List<Long> distinctQuestionIds = questionService
        .getQuestionIdsByUserIdType(UserTypeUtility.getRelevantIdType(businessUser));

    List<Long> totalReviewQuestionIds = new ArrayList<Long>();

    if (businessReviewListUtility.isListEmptyOrNull(kycReviews)) {
      return false;
    } else {

      totalReviewQuestionIds = kycReviews.stream().map(BusinessReview::getQuestionId).collect(Collectors.toList());
    }

    if (!this.longListUtility.isListPopulated(distinctAnsweredQuestionIds))

    {
      return false;
    }

    result =
        /*
         * NumberUtility.areTheTwoListsIdentical(distinctAnsweredQuestionIds,
         * mandatoryReviewQuestionIds)
         * || NumberUtility.areTheTwoListsIdentical(distinctAnsweredQuestionIds,
         * totalReviewQuestionIds)
         * ||
         */
        NumberUtility.areTheTwoListsIdentical(distinctQuestionIds, totalReviewQuestionIds);
    ;

    return result;
  }

  public int calculateUserStepUnderReview(BusinessUser businessUser) {
    int nextUserStep = UserStep.CONTRACT_MAP.getStepId();
    MyLogger.info("Calculating:::");
    if (!NumberUtility.areIntegerValuesMatching(businessUser.getKycStatus(), KycStatus.FIRST_TIME.getStatusId())) {
      List<Review> rejectedReviews = this.getAnyRejectedReviews(businessUser.getId());
      for (Review review : rejectedReviews) {
        if (review != null && review.getPageId() == null && review.getReason() != null
            && review.getReason().getReasonType() == null) {
          MyLogger.info("Id images Wrong:::");
          nextUserStep = UserStep.ID_IMAGES.getStepId();
          break;
        }
        if (review != null && NumberUtility.areLongValuesMatching(review.getPageId(), 0l)) {
          MyLogger.info("Bank References wrong:::");
          nextUserStep = UserStep.BANK_REFERENCES_SHOW.getStepId();
          break;
        }
        if (review != null && review.getPageId() != null
            && !NumberUtility.areLongValuesMatching(review.getPageId(), 0l)) {
          MyLogger.info("kyc wrong:::");
          nextUserStep = UserStep.KYC.getStepId();
          break;
        }
      }
      return nextUserStep;
    } else

      return businessUser.getNextUserStep().intValue();
  }

  public List<BusinessReview> getReviewByUserIdAndQuestionId(Long userId, Long questionId, String language) {
    return this.convertBasicListToBusinessList(
        this.reviewService.getKYCReviewsByUserIdAndQuestionId(userId, questionId), language);
  }
}
