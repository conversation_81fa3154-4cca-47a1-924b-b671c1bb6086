package innovitics.azimut.utilities.exceptionhandling;

import java.lang.reflect.InvocationTargetException;
import java.net.ConnectException;
import java.util.NoSuchElementException;

import javax.persistence.EntityNotFoundException;
import javax.validation.ConstraintViolationException;

import org.apache.http.conn.HttpHostConnectException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.tomcat.util.json.JSONParser;
/*import org.slf4j.Logger;
import org.slf4j.LoggerFactory;*/
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.ResourceAccessException;

import com.google.firebase.auth.FirebaseAuthException;

import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.utilities.datautilities.DateUtility;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class ExceptionHandler {

  protected static final Logger logger = LogManager.getLogger(ExceptionHandler.class);

  // protected static final Logger logger =
  // LoggerFactory.getLogger(ExceptionHandler.class);

  public void logException(Exception exception) {
    MyLogger.error("Exception Caught");
    MyLogger.logStackTrace(exception);
  }

  public BusinessException handleAsBusinessException(Exception exception, ErrorCode errorCode) {
    MyLogger.info("Handling Exception as Business Exception in the Exception Handler::::");
    logException(exception);
    String message = this.getErrorMessage(exception);
    BusinessException businessException = new BusinessException(errorCode.getCode(), DateUtility.getCurrentDate(),
        message != null ? message : errorCode.getMessage(), message != null ? message : errorCode.getMessageAr(),
        exception.getMessage(), exception.getStackTrace());

    return businessException;
  }

  public BusinessException handleIntegrationExceptionAsBusinessException(IntegrationException integrationException,
      ErrorCode errorCode) {
    MyLogger.info("Handling Integration Exception as an Integration Exception in the Exception Handler::::");
    MyLogger.error("Exception Error Code:" + integrationException.getErrorCode());
    MyLogger.error("Exception Message:" + integrationException.getErrorMessage());
    logException(integrationException);
    BusinessException businessException = new BusinessException(integrationException.getErrorCode(),
        DateUtility.getCurrentDate(), integrationException.getErrorMessage(), integrationException.getErrorMessageAr(),
        integrationException.getDescription(), integrationException.getStackTrace());

    return businessException;
  }

  public IntegrationException handleAsIntegrationException(Exception exception, ErrorCode errorCode) {
    MyLogger.info("Handling Exception as Integration Exception in the Exception Handler::::");
    logException(exception);
    String message = this.getErrorMessage(exception);
    IntegrationException integrationException = new IntegrationException(errorCode.getCode(),
        DateUtility.getCurrentDate(), message != null ? message : errorCode.getMessage(),
        message != null ? message : errorCode.getMessageAr(), exception.getMessage(),
        exception.getStackTrace());

    return integrationException;
  }

  private String getErrorMessage(Exception exception) {
    try {
      if (exception instanceof HttpClientErrorException) {
        var httpException = (HttpClientErrorException) exception;
        var jsonRequestBody = new JSONParser(httpException.getResponseBodyAsString()).parseObject();
        return (String) jsonRequestBody.get("message");
      }
    } catch (Exception e) {
    }
    return null;

  }

  public boolean isExceptionOfTypeEntityNotFoundException(Exception exception) {
    boolean result = exception instanceof EntityNotFoundException
        || (exception.getMessage() != null && exception.getMessage().contains("EntityNotFoundException"));
    MyLogger.info("exception instanceof EntityNotFoundException:::" + result);
    return result;
  }

  public boolean isExceptionOfTypeNoResultException(Exception exception) {
    boolean result = exception instanceof EntityNotFoundException
        || (exception.getMessage() != null && exception.getMessage().contains("NoResultException"));
    MyLogger.info("exception instanceof NoResultException:::" + result);
    return (result);
  }

  public boolean isExceptionOfTypeNoSuchElementException(Exception exception) {
    boolean result = exception instanceof NoSuchElementException
        || (exception.getMessage() != null && exception.getMessage().contains("NoSuchElementException"));
    MyLogger.info("exception instanceof NoSuchElementException:::" + result);
    return (result);
  }

  public boolean isExceptionValidationException(ErrorCode errorCode) {
    boolean result = errorCode != null && errorCode.getCode() == ErrorCode.INVALID_FIELD_VALUE.getCode();
    MyLogger.info("exception instanceof ValidationException:::" + result);
    return (result);

  }

  public boolean isABusinessException(Exception exception) {
    boolean result = false;

    result = this.isExceptionOfTypeEntityNotFoundException(exception)
        || this.isExceptionOfTypeNoResultException(exception)
        || this.isExceptionOfTypeNoSuchElementException(exception);
    MyLogger.info("No data was found");

    return result;
  }

  public boolean isNonTechnicalException(Exception exception, ErrorCode errorCode) {
    return this.isExceptionOfTypeEntityNotFoundException(exception)
        || this.isExceptionOfTypeNoResultException(exception) || this.isExceptionOfTypeNoSuchElementException(exception)
        || this.isExceptionValidationException(errorCode);
  }

  public boolean isConnectionTimeOutException(Exception exception) {
    boolean result = false;

    if (exception instanceof ResourceAccessException && exception.getCause() instanceof HttpHostConnectException) {
      result = true;
    } else if (exception instanceof ResourceAccessException && exception.getCause() instanceof ConnectException) {
      result = true;
    } else {
      result = false;
    }
    MyLogger.info("Did the connection timeout?::" + result);
    return result;
  }

  public BusinessException handleBusinessException(Exception exception, ErrorCode errorCode) {
    MyLogger.info("Handling Business Exception in exception handler!");
    if (this.isNonTechnicalException(exception, errorCode))
      return this.handleAsBusinessException(exception, errorCode);

    else
      return this.handleAsBusinessException(exception, ErrorCode.OPERATION_NOT_PERFORMED);
  }

  public BusinessException handleIntegrationException(IntegrationException integrationException, ErrorCode errorCode) {
    MyLogger.info("Handling Exception as an Integration Exception::::" + integrationException.getMessage());
    return this.handleIntegrationExceptionAsBusinessException(integrationException, errorCode);
  }

  public BusinessException handleBusinessExceptionAsIs(Exception exception, ErrorCode errorCode) {
    MyLogger.info("Handling Exception as Is" + exception.getMessage());
    return this.handleAsBusinessException(exception, errorCode);
  }

  public boolean checkIfIntegrationExceptinWithSpecificErrorCode(Exception exception, ErrorCode errorCode) {
    if ((exception instanceof IntegrationException) && NumberUtility
        .areIntegerValuesMatching(((IntegrationException) exception).getErrorCode().intValue(), errorCode.getCode())) {
      return true;
    }
    return false;
  }

  public BusinessException handleException(Exception exception) {
    MyLogger.logStackTrace(exception);
    if (exception instanceof IntegrationException)
      return this.handleIntegrationExceptionAsBusinessException((IntegrationException) exception,
          ErrorCode.FAILED_TO_INTEGRATE);
    else if (exception instanceof BusinessException)
      return (BusinessException) exception;
    else if (exception instanceof FirebaseAuthException) {
      return new BusinessException(ErrorCode.FAILED_TO_VALIDATE_TOKEN);
    } else
      return this.handleBusinessException((Exception) exception, ErrorCode.OPERATION_NOT_PERFORMED);
  }

  public Object getNullIfNonExistent(Exception exception) throws BusinessException {
    MyLogger.logStackTrace(exception);
    if (this.isABusinessException(exception)) {
      return null;
    } else {
      throw new BusinessException(ErrorCode.OPERATION_NOT_PERFORMED);
    }
  }

  public boolean isInvocationException(Exception exception) {
    boolean result = true;
    if (exception instanceof NoSuchMethodException ||
        exception instanceof SecurityException ||
        exception instanceof IllegalAccessException ||
        exception instanceof IllegalArgumentException) {
      result = false;
    } else if (exception instanceof InvocationTargetException) {
      result = true;
    }

    return result;
  }

  public boolean isConstraintViolationException(Exception exception) {
    return (exception instanceof ConstraintViolationException
        || (exception.getMessage() != null && exception.getMessage().contains("ConstraintViolationException")));
  }

}
