package innovitics.azimut.rest.entities.teacomputers;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class AddAccountRequest extends TeaComputerRequest {

  private Long idTypeId;
  private String idNumber;
  private String idDate;
  private String idMaturityDate;
  private Long clientAML;
  @JsonProperty("iDIssueCityId")
  private Long iDIssueCityId;
  @JsonProperty("iDIssueCountryId")
  private Long iDIssueCountryId;
  @JsonProperty("iDIssueDistrictId")
  private Long iDIssueDistrictId;
  private String customerNameAr;
  private String customerNameEn;
  private String birthDate;
  private Long sexId;
  private Long clientTypeId;
  private String email;
  private String phone;
  private String addressAr;
  private String addressEn;
  private Long cityId;
  private Long countryId;
  private Long nationalityId;
  private String externalcode;
  private String occupation;
  private String postalNo;
  private String mobile;
}
