package innovitics.azimut.rest.apis.enroll;

import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import innovitics.azimut.rest.entities.enroll.EnrollGetApplicationsByIdInput;
import innovitics.azimut.rest.entities.enroll.EnrollGetApplicationsOutput;

@Service
public class EnrollGetApplicationsById
    extends RestEnrollApiBase<EnrollGetApplicationsByIdInput, EnrollGetApplicationsOutput> {

  @Override
  protected String generateURL(String params) {
    return this.configProperties.getEnrollUrl() + "/api/v1/Applicant/GetApplicationsByIdsPaged";
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.POST;
  }

  @Override
  protected Boolean showOutput() {
    return false;
  }

}
