package innovitics.azimut.businessmodels.funds;

import java.util.List;
import java.util.Map;

import innovitics.azimut.businessmodels.Document;
import innovitics.azimut.businessmodels.PortfolioManager;
import innovitics.azimut.businessmodels.ReceivingEntity;
import lombok.Data;

@Data
public class BusinessFund {
  private String id;
  private String image;
  private String name;
  private String type;
  private String amount;
  private String lastUpdateDate;
  private String ric;
  private String bbgTicker;
  private String redemption;
  private String subscription;
  private String investmentObjectives;
  private Map<String, String> fundDetails;
  private String performancePortfolio;
  private String investmentGuideLines;
  private List<ReceivingEntity> receivingEntities;
  private List<PortfolioManager> portfolioManagers;
  private List<Document> relatedDocuments;
  private String currency;

}
