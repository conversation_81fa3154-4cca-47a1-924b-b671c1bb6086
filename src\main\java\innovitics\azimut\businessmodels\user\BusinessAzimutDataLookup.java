package innovitics.azimut.businessmodels.user;

import java.util.List;

import innovitics.azimut.models.teacomputers.Bank;
import innovitics.azimut.models.teacomputers.Branch;
import innovitics.azimut.models.teacomputers.City;
import innovitics.azimut.models.teacomputers.Country;
import innovitics.azimut.models.teacomputers.Currency;
import innovitics.azimut.models.teacomputers.Nationality;
import innovitics.azimut.models.user.UserType;
import lombok.Data;

@Data
public class BusinessAzimutDataLookup {

  private String entityType;
  private Long entityTypeId;

  private List<Country> countries;
  private List<City> cities;
  private List<Nationality> nationalities;
  private List<Currency> currencies;
  private List<Bank> banks;
  private List<Branch> branches;
  private List<UserType> userTypes;
  private List<BusinessCompanyBankAccount> companyBankAccounts;

}
