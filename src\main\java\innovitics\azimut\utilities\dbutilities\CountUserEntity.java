package innovitics.azimut.utilities.dbutilities;

import innovitics.azimut.utilities.CustomJsonRootName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@CustomJsonRootName(plural = "stats", singular = "stats")
public class CountUserEntity {
  private Integer count;
  private Integer kycStatus;
  private Integer isVerified;
  private String signedPdf;
  private Integer isOld;
}
