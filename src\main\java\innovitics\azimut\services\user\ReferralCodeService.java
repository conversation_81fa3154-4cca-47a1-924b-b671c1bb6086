package innovitics.azimut.services.user;

import java.util.NoSuchElementException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import innovitics.azimut.models.user.ReferralCode;
import innovitics.azimut.repositories.user.ReferralCodeRepository;

@Service
public class ReferralCodeService {
  @Autowired
  ReferralCodeRepository referralCodeRepository;

  public ReferralCode findByCode(String code) throws NoSuchElementException {
    return referralCodeRepository.findByCode(code).orElseThrow();
  }
}