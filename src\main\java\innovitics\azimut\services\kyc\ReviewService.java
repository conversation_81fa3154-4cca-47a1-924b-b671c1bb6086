package innovitics.azimut.services.kyc;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaUpdate;
import javax.persistence.criteria.Root;
import javax.transaction.Transactional;
import javax.transaction.Transactional.TxType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import innovitics.azimut.models.kyc.Reason;
import innovitics.azimut.models.kyc.Review;
import innovitics.azimut.repositories.kyc.ReviewDynamicRepository;
import innovitics.azimut.services.AbstractService;
import innovitics.azimut.utilities.crosslayerenums.ReviewResult;
import innovitics.azimut.utilities.dbutilities.SearchCriteria;
import innovitics.azimut.utilities.dbutilities.SearchOperation;
import innovitics.azimut.utilities.dbutilities.specifications.child.ReviewSpecification;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class ReviewService extends AbstractService<Review, String> {

  @Autowired
  ReviewDynamicRepository reviewDynamicRepository;
  @Autowired
  ReviewSpecification reviewSpecification;
  @Autowired
  private NamedParameterJdbcTemplate namedJdbcTemplate;

  public void deleteOldReviews(Long pageId, Long userId) {
    this.reviewDynamicRepository.deleteOldReviewsForThePage(pageId, userId);
  }

  public int countByUserIdAndDeletedAtIsNull(Long userId) {
    return this.reviewDynamicRepository.countByUserIdAndDeletedAtIsNull(userId);
  }

  public void deleteOldReviews(Long userId) {
    this.reviewDynamicRepository.deleteOldReviewsForTheUser(userId);
  }

  public void deleteFraOldReviews(Long userId, String reasonType) {
    this.reviewDynamicRepository.deleteFraOldReviewsForTheUser(userId, reasonType);
  }

  public void submitReviews(List<Review> reviews) {
    this.reviewDynamicRepository.saveAll(reviews);
  }

  public void addRejectReview(Long userId, String message, Reason reason) {
    Review review = new Review();
    review.setReason(reason);
    review.setResult(ReviewResult.REJECTED.getResultId());
    review.setCreatedAt(new Date());
    review.setComment(message);
    review.setUserId(userId);
    this.reviewDynamicRepository.save(review);
  }

  public List<Review> getReviewsByUserId(Long userId, Long pageId) {
    MyLogger.info("Retrieving Reviews:::");
    MyLogger.info("userId:" + userId);
    MyLogger.info("pageId:" + pageId);
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("userId", userId, SearchOperation.EQUAL, null));
    if (pageId != null) {
      searchCriteriaList.add(new SearchCriteria("pageId", pageId, SearchOperation.EQUAL, null));
    } else {
      searchCriteriaList.add(new SearchCriteria("pageId", null, SearchOperation.IS_NULL, null));
    }
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));
    return this.reviewDynamicRepository.findAll(reviewSpecification.findByCriteria(searchCriteriaList));

  }

  public List<Review> getFraReviewsByUserId(Long userId) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("userId", userId, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("pageId", null, SearchOperation.IS_NULL, null));
    searchCriteriaList.add(new SearchCriteria("accountId", null, SearchOperation.IS_NULL, null));
    searchCriteriaList
        .add(new SearchCriteria("result", Long.valueOf(ReviewResult.REJECTED.getResultId()), SearchOperation.EQUAL,
            null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));
    return this.reviewDynamicRepository.findAll(reviewSpecification.findByCriteria(searchCriteriaList));
  }

  public List<Review> getAllNonDeletedReviewsByUserId(Long userId) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("userId", userId, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));
    return this.reviewDynamicRepository.findAll(reviewSpecification.findByCriteria(searchCriteriaList));
  }

  public List<Review> getAllReviewsByUserId(Long userId) {
    MyLogger.info("Retrieving KYC Reviews for user: " + userId + " ::::");
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("userId", userId, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("pageId", 0l, SearchOperation.NOT_EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("pageId", null, SearchOperation.IS_NOT_NULL, null));

    return this.reviewDynamicRepository.findAll(reviewSpecification.findByCriteria(searchCriteriaList));
  }

  public List<Review> getKYCReviewsByUserId(Long userId) {
    MyLogger.info("Retrieving KYC Reviews for user: " + userId + " ::::");
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("userId", userId, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("pageId", 0l, SearchOperation.NOT_EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("pageId", null, SearchOperation.IS_NOT_NULL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));

    return this.reviewDynamicRepository.findAll(reviewSpecification.findByCriteria(searchCriteriaList));

  }

  public List<Review> getAnyRejectedReviewsByUserId(Long userId) {
    MyLogger.info("Retrieving KYC Reviews for user: " + userId + " ::::");
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("userId", userId, SearchOperation.EQUAL, null));
    searchCriteriaList
        .add(new SearchCriteria("result", ReviewResult.REJECTED.getResultId(), SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));
    return this.reviewDynamicRepository.findAll(reviewSpecification.findByCriteria(searchCriteriaList),
        Sort.by("pageId").ascending());

  }

  public List<Review> getReviewsByReasonId(Long reasonId) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("id", reasonId, SearchOperation.PARENT_EQUAL, "reason"));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));
    return this.reviewDynamicRepository.findAll(reviewSpecification.findByCriteria(searchCriteriaList));

  }

  public Long getIdOfThePageWithLeastOrder(Long userId) {
    return this.reviewDynamicRepository.getIdOfThePageWithLeastOrder(userId, ReviewResult.REJECTED.getResultId(),
        userId);

  }

  public Review removeReview(Review review) {
    review.setDeletedAt(new Date());
    return this.reviewDynamicRepository.save(review);
  }

  public int batchUpdate(String ids) {
    final String update = "UPDATE reviews r SET r.deleted_at=sysdate() WHERE r.id IN (?1)";
    return this.entityManager.createQuery(update).setParameter(1, ids).executeUpdate();
  }

  @Transactional(value = TxType.REQUIRES_NEW)
  public void removeOldReviews(List<Review> reviews) {
    StopWatch timer = new StopWatch();
    String sql = "UPDATE reviews SET deleted_at=sysdate() WHERE id=:id";

    List<MapSqlParameterSource> params = new ArrayList<MapSqlParameterSource>();

    for (Review review : reviews) {
      MapSqlParameterSource source = new MapSqlParameterSource();
      source.addValue("id", review.getId());
      params.add(source);
    }

    timer.start();
    namedJdbcTemplate.batchUpdate(sql, params.toArray(MapSqlParameterSource[]::new));

    timer.stop();
    MyLogger.info("batchUpdate -> Total time in seconds: " + timer.getTotalTimeSeconds());
  }

  public List<Review> getReviewsByUserId(Long userId) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("userId", userId, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));
    return this.reviewDynamicRepository.findAll(reviewSpecification.findByCriteria(searchCriteriaList));

  }

  @Transactional
  public void softDeleteBulkReviews(List<Long> ids) {
    StopWatch timer = new StopWatch();
    CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
    CriteriaUpdate<Review> criteriaUpdate = criteriaBuilder.createCriteriaUpdate(Review.class);
    Root<Review> root = criteriaUpdate.from(Review.class);
    criteriaUpdate.set("deletedAt", new Date());
    criteriaUpdate.where(criteriaBuilder.in(root.get("id")).value(ids));
    entityManager.createQuery(criteriaUpdate).executeUpdate();
    MyLogger.info("batchUpdate -> Total time in seconds: " + timer.getTotalTimeSeconds());
  }

  public List<Review> getKYCReviewsByUserIdAndQuestionId(Long userId, Long questionId) {
    MyLogger.info("Retrieving KYC Reviews for user: " + userId + " ::::");
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("userId", userId, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("questionId", questionId, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));

    return this.reviewDynamicRepository.findAll(reviewSpecification.findByCriteria(searchCriteriaList));

  }
}
