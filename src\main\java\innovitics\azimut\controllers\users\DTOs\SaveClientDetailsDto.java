package innovitics.azimut.controllers.users.DTOs;

import innovitics.azimut.businessmodels.user.AzimutAccount;
import innovitics.azimut.businessmodels.user.BusinessUser;
import lombok.Data;

@Data
public class SaveClientDetailsDto {
  Long otherIdType;
  String otherUserId;
  String otherNationality;
  String address;
  Integer userStep;

  public BusinessUser toBusinessUser() {
    BusinessUser businessUser = new BusinessUser();
    businessUser.setOtherIdType(otherIdType);
    businessUser.setOtherUserId(otherUserId);
    businessUser.setOtherNationality(otherNationality);
    businessUser.setUserStep(userStep);
    AzimutAccount account = new AzimutAccount();
    account.setAddressAr(address);
    account.setAddressEn(address);
    businessUser.setAzimutAccount(account);
    return businessUser;
  }
}
