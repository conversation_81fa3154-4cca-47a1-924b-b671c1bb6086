package innovitics.azimut.rest.apis.teacomputers;

import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import innovitics.azimut.rest.entities.teacomputers.AddAccountRequest;
import innovitics.azimut.rest.entities.teacomputers.AddAccountResponse;
import innovitics.azimut.rest.entities.teacomputers.TeaComputerResponse;
import innovitics.azimut.utilities.datautilities.StringUtility;

@Service
public class TeaComputersAddAccountApi
    extends RestTeaComputersApi<AddAccountRequest, AddAccountResponse> {

  public static final String PATH = "/AddCustomer";

  @Override
  protected String generateURL(String params) {
    return this.generateBaseURL() + PATH;
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.POST;
  }

  @Override
  protected String generateResponseSignature(AddAccountRequest request, TeaComputerResponse response) {
    return this.teaComputersSignatureGenerator.generateSignature(true,
        request.getUserName() != null ? request.getUserName() : this.configProperties.getTeaComputersKey(),
        "", response.getMessage());
  }

  protected String generateResponseSignature(TeaComputerResponse response) {
    // Not used
    return this.teaComputersSignatureGenerator.generateSignature("", response.getMessage());
  }

  @Override
  public String generateSignature(AddAccountRequest request) {
    if (request != null && StringUtility.isStringPopulated(request.getIdNumber()) && request.getIdTypeId() != null
        && StringUtility.isStringPopulated(request.getIdTypeId().toString())) {
      return this.teaComputersSignatureGenerator.generateSignature(true,
          request.getUserName() != null ? request.getUserName() : this.configProperties.getTeaComputersKey(),
          request.getIdTypeId() != null ? request.getIdTypeId().toString() : null, request.getIdNumber(),
          request.getMobile());
    }
    return null;
  }
}
