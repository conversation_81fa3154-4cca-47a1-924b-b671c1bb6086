package innovitics.azimut.controllers.kyc;

import java.io.IOException;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import innovitics.azimut.businessmodels.kyc.BusinessKYCPage;
import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.controllers.GenericResponseHandler;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.utilities.datautilities.StringUtility;

@RestController
@RequestMapping("/api/kyc/reviewed/pages")
public class KYCReviewController extends KYCController {
  @Autowired
  GenericResponseHandler<BusinessKYCPage> businessKYCPageResponseHandler;

  @PostMapping(value = "/submitAnswers", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE }, produces = {
          MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessKYCPage>> submitAnswer(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody SubmitAnswersDto businessUserAnswerSubmission)
      throws BusinessException, IOException, IntegrationException {
    return businessKYCPageResponseHandler.generateBaseGenericResponse(BusinessKYCPage.class,
        this.businessUserAnswerSubmissionService.submitAnswersWhenUnderReview(this.getCurrentRequestHolder(token),
            businessUserAnswerSubmission, businessKYCPageService, language),
        null, null);
  }

}
