package innovitics.azimut.rest;

import java.lang.reflect.ParameterizedType;
import java.util.Arrays;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import innovitics.azimut.configproperties.ConfigProperties;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.exceptionhandling.ExceptionHandler;
import innovitics.azimut.utilities.logging.MyLogger;

@Component
public abstract class RestBaseApi<I, O> {

  @Autowired
  protected ExceptionHandler exceptionHandler;
  @Autowired
  protected ConfigProperties configProperties;

  public O getData(I inputData) throws IntegrationException {
    return this.getData(inputData, null);
  }

  public O getData(I inputData, String param) throws IntegrationException {
    MyLogger.info("API Class Type:::" + this.getClass().getName());
    // MyLogger.info("Input Type:::" + this.getInputClass().getName());
    MyLogger.info("Response Type:::" + this.getResponseClass().getName());
    return invoke(inputData, getResponseClass(), param);
  }

  public void loopConsumption(List<I> dataEntries) throws IntegrationException {
    for (I entry : dataEntries) {
      this.getData(entry, null);
    }
  }

  protected Class<O> getResponseClass() {
    return (Class<O>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[1];
  };

  // public abstract Class<I> getInputClass();

  protected HttpEntity<?> generateRequestFromInput(I input) {
    // override if needed
    return this.stringify(input);
  };

  protected abstract String generateURL(String params);

  protected abstract HttpMethod chooseHttpMethod();

  protected Boolean showOutput() {
    return true;
  }

  protected void validateResponse(I request, ResponseEntity<O> responseEntity) throws IntegrationException {
    validateResponse(responseEntity);
  }

  protected void validateResponse(ResponseEntity<O> responseEntity) throws IntegrationException {
    // can be overridden if needed
    if (this.validateResponseStatus(responseEntity)) {
      if (responseEntity.getBody() == null) {
        throw new IntegrationException(ErrorCode.NO_DATA_FOUND);
      }
    }
  };

  private ResponseEntity<O> consumeRestAPI(HttpEntity<?> httpEntity, HttpMethod httpMethod, Class<O> clazz,
      String params) throws Exception, HttpClientErrorException, IntegrationException {
    MyLogger.info("Request right before invocation::::" + httpEntity.toString());
    MyLogger.info("Method:::" + httpMethod);
    MyLogger.info("Class:::" + clazz.getName());

    ResponseEntity<O> responseEntity = this.restTemplate().exchange(this.generateURL(params), httpMethod,
        httpEntity,
        clazz);
    return responseEntity;

  }

  protected RestTemplate restTemplate() {
    // can be overridden if needed
    return new RestTemplate();
  }

  protected boolean validateResponseStatus(ResponseEntity<O> responseEntity) {
    boolean responseResult = responseEntity != null && responseEntity.getStatusCode() != null
        && responseEntity.getStatusCode().is2xxSuccessful() && responseEntity.getBody() != null;

    return responseResult;
  }

  protected O generateOutPutFromResponse(ResponseEntity<O> responseEntity) {
    return responseEntity.getBody();
  }

  protected IntegrationException handleError(HttpClientErrorException httpClientErrorException) {
    // can be overridden if needed
    return new IntegrationException(ErrorCode.FAILED_TO_INTEGRATE);
  };

  protected void populateResponse(String url, ResponseEntity<O> responseEntity) {
    // empty unless specific implementation is overridden
  };

  protected void transferFromInputToOutput(I input, O output) {
    // empty unless specific implementation is overridden
  }

  protected IntegrationException handleException(Exception exception) {
    this.exceptionHandler.logException(exception);

    if (exception instanceof IntegrationException) {
      return (IntegrationException) exception;
    }

    if (exception instanceof HttpClientErrorException) {
      IntegrationException integrationException = this.handleError((HttpClientErrorException) exception);
      return integrationException;
    }

    return new IntegrationException(ErrorCode.FAILED_TO_INTEGRATE);
  }

  public O invoke(I input, Class<O> clazz, String params) throws IntegrationException {
    MyLogger.info("Input::" + input);
    ResponseEntity<O> responseEntity = null;
    try {

      HttpEntity<?> httpEntity = this.generateRequestFromInput(input);

      if (httpEntity != null) {
        MyLogger.info("Request::" + httpEntity.toString());
      }

      String url = this.generateURL(params);

      MyLogger.info("URL:::" + url);
      responseEntity = this.consumeRestAPI(httpEntity, this.chooseHttpMethod(), clazz, params);

      this.populateResponse(url, responseEntity);

      if (showOutput())
        MyLogger.info("Response::::" + (responseEntity != null ? responseEntity.toString() : null));

      this.validateResponse(input, responseEntity);

      O output = this.generateOutPutFromResponse(responseEntity);

      this.transferFromInputToOutput(input, output);
      if (showOutput() && output != null) {
        if (output.getClass().isArray()) {
          MyLogger.info("Output:::" + Arrays.toString(((Object[]) output)));
        } else {
          MyLogger.info("Output:::" + output.toString());
        }
      }
      return output;
    }

    catch (Exception exception) {
      if (this.exceptionHandler.isConnectionTimeOutException(exception)) {
        throw new IntegrationException(ErrorCode.CONNECTION_TIMEOUT);
      } else {
        throw this.handleException(exception);
      }
    }
  }

  public HttpHeaders generateHeaders() {
    HttpHeaders headers = new HttpHeaders();
    headers.setAccept(Arrays.asList(new MediaType[] { MediaType.APPLICATION_JSON }));
    headers.setContentType(MediaType.APPLICATION_JSON);
    return headers;
  }

  protected HttpEntity<String> stringify(I request) {
    return stringify(request, this.generateHeaders());
  }

  protected HttpEntity<String> stringify(I request, HttpHeaders headers) {
    ObjectMapper objectMapper = new ObjectMapper();
    String json = "";
    try {
      objectMapper.registerModule(new JavaTimeModule());
      objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
      objectMapper.setSerializationInclusion(Include.NON_NULL);
      objectMapper.setSerializationInclusion(Include.NON_ABSENT);
      objectMapper.setSerializationInclusion(Include.NON_EMPTY);

      json = objectMapper.writeValueAsString(request);
      // MyLogger.info("Json:::::"+json);
    } catch (JsonProcessingException e) {
      MyLogger.info("Could not stringify to json object");
      MyLogger.logStackTrace(e);
    }

    HttpEntity<String> httpEntity = (headers == null) ? new HttpEntity<String>(json)
        : new HttpEntity<String>(json, headers);
    return httpEntity;
  }

}
