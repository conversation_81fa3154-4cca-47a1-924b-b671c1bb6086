package innovitics.azimut.services;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import innovitics.azimut.models.Nav;
import innovitics.azimut.repositories.fund.NavRepository;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class NavService extends AbstractService<Nav, String> {
  @Autowired
  NavRepository navRepository;

  public List<Nav> batchInsert(List<Nav> navs) {
    return this.navRepository.saveAll(navs);
  }

  public List<Nav> getByJoinedTeacomputerIds() {
    return this.navRepository.getByJoinedTeacomputerIds();
  }

  public Nav updateNav(Nav nav) {
    MyLogger.info("Updating the Nav:::" + nav.getId());
    nav.setUpdatedAt(new Date());
    return this.navRepository.save(nav);
  }

  public List<Nav> getByDatesAndTeacomputerIds(List<Long> teacomputerIds, List<String> dates) {
    return this.navRepository.getByDatesAndTeacomputerIds(this.generateConditionString(teacomputerIds, dates));
  }

  public Nav getFirstByFundId(Long fundId) {
    return navRepository.findFirstByFundIdOrderByDateAsc(fundId).get();
  }

  public Nav getLastByFundId(Long fundId) {
    return navRepository.findFirstByFundIdOrderByDateDesc(fundId).get();
  }

  public Nav getNavOneYearAgo(Long fundId) {
    // Calculate today - 1 year
    LocalDate oneYearAgo = LocalDate.now().minusYears(1);
    Date oneYearAgoDate = Date.from(oneYearAgo.atStartOfDay(ZoneId.systemDefault()).toInstant());
    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
    var dateStr = simpleDateFormat.format(oneYearAgoDate);
    var navs = navRepository.getFirstBeforeDate(dateStr, fundId);
    return navs.size() == 0 ? getFirstByFundId(fundId) : navs.get(0);
  }

  public Nav getNavAtBeginningOfYear(Long fundId) {
    // Calculate January 1st of the current year
    LocalDate beginningOfYear = LocalDate.now().withDayOfYear(1).minus(1, ChronoUnit.DAYS);
    Date beginningOfYearDate = Date.from(beginningOfYear.atStartOfDay(ZoneId.systemDefault()).toInstant());
    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
    var dateStr = simpleDateFormat.format(beginningOfYearDate);
    var navs = navRepository.getFirstBeforeDate(dateStr, fundId);
    return navs.size() == 0 ? getFirstByFundId(fundId) : navs.get(0);
  }

  public List<Nav> getNavsWithDividendsDates(List<String> dates, Long fundId) {
    return this.navRepository.getByDatesAndDatesBefore(dates, fundId);
  }

  String generateConditionString(List<Long> teacomputerIds, List<String> dates) {
    StringBuffer condition = new StringBuffer();
    if (NumberUtility.areIntegerValuesMatching(teacomputerIds.size(), dates.size())) {
      int size = teacomputerIds.size();
      if (size > 1) {
        for (int i = 0; i < size; i++) {
          condition.append(this.generateConditionString(teacomputerIds.get(i), dates.get(i)));
          if (i != size - 1) {
            condition.append("or");
          }
        }
      } else {
        condition.append(this.generateConditionString(teacomputerIds.get(0), dates.get(0)));
      }
    } else {
      condition.append("1=1");
    }

    return condition.toString();
  }

  String generateConditionString(Long teacomputerId, String date) {
    return "(teacomputer_id=" + teacomputerId + " and date='" + date + "')";
  }

}
