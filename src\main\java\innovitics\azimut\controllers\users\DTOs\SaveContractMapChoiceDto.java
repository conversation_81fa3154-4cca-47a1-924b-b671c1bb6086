package innovitics.azimut.controllers.users.DTOs;

import innovitics.azimut.businessmodels.user.BusinessUser;
import lombok.Data;

@Data
public class SaveContractMapChoiceDto {
  Integer contractMap;
  Integer userStep;

  public BusinessUser toBusinessUser() {
    BusinessUser businessUser = new BusinessUser();
    businessUser.setContractMap(contractMap);
    businessUser.setUserStep(userStep);
    return businessUser;
  }
}
