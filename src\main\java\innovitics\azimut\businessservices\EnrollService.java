package innovitics.azimut.businessservices;

import java.io.IOException;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.xml.bind.DatatypeConverter;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import innovitics.azimut.businessmodels.enroll.BusinessEnroll;
import innovitics.azimut.businessmodels.user.AzimutAccount;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.controllers.enroll.DTOs.EnrollKycDto;
import innovitics.azimut.controllers.enroll.DTOs.EnrollRequestIdDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.models.kyc.ReasonType;
import innovitics.azimut.models.kyc.Review;
import innovitics.azimut.models.user.UserDevice;
import innovitics.azimut.models.user.UserImage;
import innovitics.azimut.rest.apis.enroll.EnrollDeleteApplicant;
import innovitics.azimut.rest.apis.enroll.EnrollGetApplicationsByDate;
import innovitics.azimut.rest.apis.enroll.EnrollGetApplicationsById;
import innovitics.azimut.rest.apis.enroll.EnrollGetApplicationsIdByRequestId;
import innovitics.azimut.rest.apis.enroll.EnrollGetImage;
import innovitics.azimut.rest.apis.enroll.EnrollOnboardingSession;
import innovitics.azimut.rest.entities.enroll.EnrollApplication;
import innovitics.azimut.rest.entities.enroll.EnrollAuthOutput;
import innovitics.azimut.rest.entities.enroll.EnrollGetApplicationsByDateInput;
import innovitics.azimut.rest.entities.enroll.EnrollGetApplicationsByIdInput;
import innovitics.azimut.rest.entities.enroll.EnrollGetApplicationsOutput;
import innovitics.azimut.services.kyc.UserImageService;
import innovitics.azimut.services.user.GenderService;
import innovitics.azimut.services.user.UserDeviceService;
import innovitics.azimut.utilities.businessutilities.UserUtility;
import innovitics.azimut.utilities.crosslayerenums.UserIdType;
import innovitics.azimut.utilities.crosslayerenums.UserImageType;
import innovitics.azimut.utilities.crosslayerenums.UserStep;
import innovitics.azimut.utilities.datautilities.BooleanUtility;
import innovitics.azimut.utilities.datautilities.CustomMultipartFile;
import innovitics.azimut.utilities.datautilities.DateUtility;
import innovitics.azimut.utilities.datautilities.JsonUtility;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;
import innovitics.azimut.validations.Validation;

@Service
public class EnrollService extends BusinessUserSigningService {

  private @Autowired Validation validation;
  private @Autowired EnrollGetApplicationsByDate enrollGetApplications;
  private @Autowired EnrollGetApplicationsById enrollGetApplicationsById;
  private @Autowired EnrollGetApplicationsIdByRequestId enrollGetApplicationsIdByRequestId;
  private @Autowired EnrollDeleteApplicant enrollDeleteApplicant;
  private @Autowired EnrollOnboardingSession enrollOnboardingSession;
  private @Autowired EnrollGetImage enrollGetImage;
  private @Autowired UserUtility userUtility;
  private @Autowired GenderService genderService;
  private @Autowired UserImageService userImageService;
  private @Autowired UserDeviceService userDeviceService;
  private @Autowired FraService fraService;

  public void setRequestId(BusinessUser user, EnrollRequestIdDto request) throws BusinessException {
    user.setEnrollRequestId(request.getRequestId());
    this.editUser(user);
    if (StringUtility.isStringPopulated(request.getDeviceId())) {
      user.setDeviceId(request.getDeviceId());
      if (StringUtility.isStringPopulated(request.getDeviceName()))
        user.setDeviceName(request.getDeviceName());
      this.editUser(user);
      this.userUtility.logDevice(user.getId(), request.getDeviceId(), request.getDeviceName());
    }
  }

  public void checkRequestId(BusinessUser user) {
    try {
      var result = enrollGetApplicationsIdByRequestId.getData(null, user.getEnrollRequestId());
      if (result != null && StringUtility.isStringPopulated(result.getValue())) {
        EnrollKycDto kycData = new EnrollKycDto();
        kycData.setApplicantId(result.getValue());
        verify(user, kycData, "en", false);
      }
    } catch (Exception e) {
      MyLogger.info("Request id not found " + user.getEnrollRequestId());
    }
  }

  public void enrollCallback(EnrollApplication applicant) throws BusinessException, IntegrationException, IOException {
    BusinessUser user = this.getUserByDeviceId(applicant.getDeviceId());
    if (user == null || (user.getUserStep() > UserStep.ID_IMAGES.getStepId()))
      return;
    EnrollKycDto kycData = new EnrollKycDto();
    if (applicant.getNationalIdInfo() != null) {
      kycData.setIdNumber(applicant.getNationalIdInfo().getIdNumber());
    } else {
      kycData.setDocumentNumber(applicant.getPassportInfo().getDocumentNumber());
    }
    kycData.setApplicantId(applicant.getApplicantId());
    verify(user, kycData, "en", false);
  }

  public BusinessEnroll checkAndVerify(BusinessUser user, String language)
      throws IntegrationException, BusinessException, IOException {
    var result = enrollGetApplicationsIdByRequestId.getData(null, user.getEnrollRequestId());
    if (result != null && StringUtility.isStringPopulated(result.getValue())) {
      EnrollKycDto kycData = new EnrollKycDto();
      kycData.setApplicantId(result.getValue());
      return verify(user, kycData, language, false);
    } else
      throw new IntegrationException(ErrorCode.NO_DATA_FOUND);
  }

  public BusinessEnroll checkAndUpdate(BusinessUser user, String language)
      throws IntegrationException, BusinessException, IOException {
    EnrollKycDto kycData = new EnrollKycDto();
    kycData.setApplicantId(user.getEnrollApplicantId());
    return verify(user, kycData, language, true);
  }

  public void syncUserImages(Long userId) throws BusinessException, IntegrationException, IOException {
    var businessUser = this.getByUserId(userId);
    EnrollKycDto kycData = new EnrollKycDto();
    kycData.setApplicantId(businessUser.getEnrollApplicantId());
    EnrollApplication application = getApplication(kycData);
    boolean isEgyptian = application.getNationalIdInfo() != null;
    List<UserImage> userImages = new ArrayList<UserImage>();
    if (isEgyptian) {
      MyLogger.info("Enroll national ID documents");
      MultipartFile frontImage = getImage(application.getNationalIdInfo().getIdFrontScanPath());
      UserImage frontUserImage = this.userUtility.createUserImageRecord(businessUser, frontImage,
          UserImageType.FRONT_IMAGE);
      userImages.add(frontUserImage);

      MultipartFile backImage = getImage(application.getNationalIdInfo().getIdBackScanPath());
      UserImage backUserImage = this.userUtility.createUserImageRecord(businessUser, backImage,
          UserImageType.BACK_IMAGE);
      userImages.add(backUserImage);
    } else {
      MyLogger.info("Enroll passport documents");
      MultipartFile passportImage = getImage(application.getPassportInfo().getPassportScanPath());
      UserImage passportUserImage = this.userUtility.createUserImageRecord(businessUser, passportImage,
          UserImageType.PASSPORT_IMAGE);
      userImages.add(passportUserImage);
    }

    MultipartFile selfieImage = getImage(application.getLivePhotoPath());
    UserImage straightImage = this.userUtility.createUserImageRecord(businessUser, selfieImage, UserImageType.STRAIGHT);
    userImages.add(straightImage);
    this.userUtility.uploadUserImages(userImages, businessUser);
    userImageService.saveImages(userImages);
  }

  public BusinessEnroll verify(BusinessUser businessUser, EnrollKycDto kycData, String language, Boolean isUpdate)
      throws IntegrationException, BusinessException, IOException {
    if (BooleanUtility.isFalse(isUpdate))
      this.validation.validateUserKYCCompletion(businessUser);
    List<UserImage> userImages = new ArrayList<UserImage>();
    BusinessEnroll response = new BusinessEnroll();
    if (kycData.getApplicantId() == null)
      validateKycData(kycData.getIdNumber(), kycData.getDocumentNumber());
    EnrollApplication application = getApplication(kycData);
    boolean isEgyptian = application.getNationalIdInfo() != null;
    response.setFullName(
        (application.getFullNameAr() == null || application.getFullNameAr().length() == 0) ? application.getFullNameEn()
            : application.getFullNameAr());
    response.setGender(application.getGender());
    response.setAmlBlackList(application.getAmlBlackList());
    response.setDateOfBirth(DateUtility.getDayMonthYear(application.getBirthdate()));
    Map<String, Object> idData = new HashMap<>();
    List<Double> percentages = new ArrayList<>();
    if (isEgyptian) {
      MyLogger.info("Enroll national ID documents");
      MultipartFile frontImage = getImage(application.getNationalIdInfo().getIdFrontScanPath());
      UserImage frontUserImage = this.userUtility.createUserImageRecord(businessUser, frontImage,
          UserImageType.FRONT_IMAGE);
      userImages.add(frontUserImage);

      MultipartFile backImage = getImage(application.getNationalIdInfo().getIdBackScanPath());
      UserImage backUserImage = this.userUtility.createUserImageRecord(businessUser, backImage,
          UserImageType.BACK_IMAGE);
      userImages.add(backUserImage);

      String state = application.getNationalIdInfo().getState();
      var splitState = state.split("-");
      response.setFirstName(application.getNationalIdInfo().getFirstName());
      response.setLastName(application.getNationalIdInfo().getFamilyName());
      response.setStreet(application.getNationalIdInfo().getAddressNormalized());
      response.setArea(splitState[0].trim());
      response.setFrontNid(application.getNationalIdInfo().getIdNumber());
      response.setSerialNumber(application.getNationalIdInfo().getDocumentNumber());
      response.setReleaseDate(DateUtility.getDayMonthYear(application.getNationalIdInfo().getIssueDate()));
      response.setMaritalStatus(application.getNationalIdInfo().getMaritalStatus());
      response.setProfession(application.getNationalIdInfo().getProfession());
      response.setReligion(application.getNationalIdInfo().getReligion());
      // response.setHusbandName(valifyOCROutput.getHusbandName());
      response.setExpirationDate(DateUtility.getDayMonthYear(application.getNationalIdInfo().getExpirationDate()));
      response.setCountry(StringUtility.EGYPT);
      response.setCity(splitState[splitState.length - 1].trim());
      response.setUserId(application.getNationalIdInfo().getIdNumber());
      response.setAzIdType(UserIdType.NATIONAL_ID.getTypeId());

      if (StringUtility.isStringPopulated(response.getReligion()))
        idData.put("religion", response.getReligion());
      if (StringUtility.isStringPopulated(response.getMaritalStatus()))
        idData.put("maritalStatus", response.getMaritalStatus());
      if (StringUtility.isStringPopulated(response.getSerialNumber()))
        idData.put("fcn", response.getSerialNumber());

      percentages.add(application.getNationalIdInfo().getColorProfileChangeDetectedScore());
      percentages.add(application.getNationalIdInfo().getLooksLikeScreenshotScore());
      percentages.add(application.getNationalIdInfo().getColorProfileChangeDetectedScoreBack());
      percentages.add(application.getNationalIdInfo().getLooksLikeScreenshotScoreBack());
      percentages.add(application.getNationalIdInfo().getDocumentPortraitGenuineScore());
      percentages.add(application.getNationalIdInfo().getFullNameFraudDetection());
      percentages.add(application.getNationalIdInfo().getAddressFraudDetection());
      percentages.add(application.getNationalIdInfo().getStateFraudDetection());
      percentages.add(application.getNationalIdInfo().getBirthDateFraudDetection());
      percentages.add(application.getNationalIdInfo().getPersonalNumberFraudDetection());
      percentages.add(application.getNationalIdInfo().getDocumentNumberFraudDetection());
      percentages.add(application.getNationalIdInfo().getProfessionFraudDetection());
      percentages.add(application.getNationalIdInfo().getCivilStatusFraudDetection());
      percentages.add(application.getNationalIdInfo().getReligionFraudDetection());
      percentages.add(application.getNationalIdInfo().getGenderFraudDetection());
      percentages.add(application.getNationalIdInfo().getExpiryDateFraudDetection());
      percentages.add(application.getNationalIdInfo().getIdFrontScanAccuracy());
      percentages.add(application.getNationalIdInfo().getIdBackScanAccuracy());

    } else {
      MyLogger.info("Enroll passport documents");
      MultipartFile passportImage = getImage(application.getPassportInfo().getPassportScanPath());
      UserImage passportUserImage = this.userUtility.createUserImageRecord(businessUser, passportImage,
          UserImageType.PASSPORT_IMAGE);
      userImages.add(passportUserImage);

      response.setFirstName(
          (application.getPassportInfo().getFirstNameAr() == null
              || application.getPassportInfo().getFirstNameAr().length() == 0)
                  ? application.getPassportInfo().getFirstNameEn()
                  : application.getPassportInfo().getFirstNameAr());
      response.setLastName((application.getPassportInfo().getFamilyNameAr() == null
          || application.getPassportInfo().getFamilyNameAr().length() == 0)
              ? application.getPassportInfo().getFamilyNameEn()
              : application.getPassportInfo().getFamilyNameAr());
      response.setPassportNumber(application.getPassportInfo().getDocumentNumber());
      response.setExpirationDate(DateUtility.getDayMonthYear(application.getPassportInfo().getExpirationDate()));
      response.setCountry(application.getPassportInfo().getNationality());
      response.setUserId(application.getPassportInfo().getDocumentNumber());
      response.setAzIdType(UserIdType.PASSPORT.getTypeId());
      percentages.add(application.getPassportInfo().getPassportScanAccuracy());
    }
    if (application.getBiometricInfo() != null) {
      idData.put("similarity", application.getBiometricInfo().getPhotoMatchPercentage());
      idData.put("liveness", application.getBiometricInfo().getLivenessScore());
      percentages.add(application.getBiometricInfo().getNaturalImageScore());
      percentages.add(application.getBiometricInfo().getSmileImageScore());
      percentages.add(application.getBiometricInfo().getMagnifeyeLiveness());
    }
    idData.put("percentages", percentages);
    response.setIdData(idData);
    // result.setImagesSimilar(valifyFacialImageOutput.isSimilar());
    // result.setConfidence(valifyFacialImageOutput.getConfidence());
    MultipartFile selfieImage = getImage(application.getLivePhotoPath());
    UserImage straightImage = this.userUtility.createUserImageRecord(businessUser, selfieImage, UserImageType.STRAIGHT);
    userImages.add(straightImage);
    businessUser.setEnrollApplicantId(application.getApplicantId());
    businessUser.setLivenessChecked(true);
    if (BooleanUtility.isFalse(isUpdate)) {
      this.userUtility.removeImagesFromBlobAndDb(businessUser, true);
      this.userUtility.removeImagesFromBlobAndDb(businessUser, false);
    }
    updateUserDetailsAndSaveUserImages(businessUser, response, userImages, isEgyptian);

    MyLogger.info("First Page ID::::" + businessUser.getFirstPageId());
    response.setFirstPageId(businessUser.getFirstPageId());
    if (BooleanUtility.isFalse(isUpdate))
      this.handleReviews(businessUser, null, language);
    response.setNextUserStep(this.reviewUtility.calculateUserStepUnderReview(businessUser));

    return response;
  }

  public void updateSingleIdData(String applicantId) throws IntegrationException, BusinessException {
    EnrollGetApplicationsByIdInput byIdInput = new EnrollGetApplicationsByIdInput();
    List<String> applicants = Arrays.asList(applicantId);
    byIdInput.setApplicationIds(applicants);
    var result = enrollGetApplicationsById.getData(byIdInput);
    for (EnrollApplication application : result.getEntities()) {
      updateApplicant(application);
    }
  }

  public void updateIdData() throws IntegrationException, BusinessException {
    Instant hourAgo = Instant.now().minus(365, ChronoUnit.DAYS);
    EnrollGetApplicationsByDateInput input = new EnrollGetApplicationsByDateInput();
    input.setFrom(hourAgo);
    input.setTo(Instant.now());
    Integer pageNumber = 1;
    input.setPageNumber(pageNumber);
    var result = enrollGetApplications.getData(input);
    MyLogger.info("Enroll page: " + pageNumber);
    for (EnrollApplication application : result.getEntities()) {
      updateApplicant(application);
    }
    MyLogger.info(result.getTotalCount() + " > " + (result.getPageNumber() * result.getPageSize()));
    while (result.getTotalCount() > (result.getPageNumber() * result.getPageSize())) {
      pageNumber += 1;
      MyLogger.info("Enroll page: " + pageNumber);
      input.setPageNumber(pageNumber);
      try {
        result = enrollGetApplications.getData(input);
      } catch (Exception e) {
        try {
          Thread.sleep(10000);
        } catch (InterruptedException e1) {
          System.out.println("Thread was interrupted");
        }
        MyLogger.info("Retry Enroll page: " + pageNumber);
        result = enrollGetApplications.getData(input);
      }
      for (EnrollApplication application : result.getEntities()) {
        updateApplicant(application);
      }
      try {
        Thread.sleep(10000);
      } catch (InterruptedException e) {
        // Handle the exception if needed
        System.out.println("Thread was interrupted");
      }
    }
  }

  private void updateApplicant(EnrollApplication application) throws BusinessException {
    String userId = application.getNationalIdInfo() != null ? application.getNationalIdInfo().getIdNumber()
        : application.getPassportInfo().getDocumentNumber();
    MyLogger.info("Updating Enroll userId: " + userId);
    var users = userService.findUsersByUserId(userId);
    for (var user : users) {
      var businessUser = userMapper.convertBasicUnitToBusinessUnit(user);
      Map<String, Object> idData = new HashMap<>();
      List<Double> percentages = new ArrayList<>();
      if (application.getNationalIdInfo() != null) {
        AzimutAccount azimutAccount = businessUser.getAzimutAccount();
        azimutAccount.setOccupation(application.getNationalIdInfo().getProfession());
        idData.put("religion", application.getNationalIdInfo().getReligion());
        idData.put("maritalStatus", application.getNationalIdInfo().getMaritalStatus());
        idData.put("fcn", application.getNationalIdInfo().getDocumentNumber());
        percentages.add(application.getNationalIdInfo().getColorProfileChangeDetectedScore());
        percentages.add(application.getNationalIdInfo().getLooksLikeScreenshotScore());
        percentages.add(application.getNationalIdInfo().getColorProfileChangeDetectedScoreBack());
        percentages.add(application.getNationalIdInfo().getLooksLikeScreenshotScoreBack());
        percentages.add(application.getNationalIdInfo().getDocumentPortraitGenuineScore());
        percentages.add(application.getNationalIdInfo().getFullNameFraudDetection());
        percentages.add(application.getNationalIdInfo().getAddressFraudDetection());
        percentages.add(application.getNationalIdInfo().getStateFraudDetection());
        percentages.add(application.getNationalIdInfo().getBirthDateFraudDetection());
        percentages.add(application.getNationalIdInfo().getPersonalNumberFraudDetection());
        percentages.add(application.getNationalIdInfo().getDocumentNumberFraudDetection());
        percentages.add(application.getNationalIdInfo().getProfessionFraudDetection());
        percentages.add(application.getNationalIdInfo().getCivilStatusFraudDetection());
        percentages.add(application.getNationalIdInfo().getReligionFraudDetection());
        percentages.add(application.getNationalIdInfo().getGenderFraudDetection());
        percentages.add(application.getNationalIdInfo().getExpiryDateFraudDetection());
        percentages.add(application.getNationalIdInfo().getIdFrontScanAccuracy());
        percentages.add(application.getNationalIdInfo().getIdBackScanAccuracy());
      } else {
        // for passport
        percentages.add(application.getPassportInfo().getPassportScanAccuracy());
      }
      if (application.getBiometricInfo() != null) {
        idData.put("similarity", application.getBiometricInfo().getPhotoMatchPercentage());
        idData.put("liveness", application.getBiometricInfo().getLivenessScore());
        percentages.add(application.getBiometricInfo().getNaturalImageScore());
        percentages.add(application.getBiometricInfo().getSmileImageScore());
        percentages.add(application.getBiometricInfo().getMagnifeyeLiveness());
      }
      idData.put("percentages", percentages);
      businessUser.setIdData(JsonUtility.toJson(idData)); // Convert Map to JSON

      this.editUser(businessUser);
    }
  }

  public EnrollAuthOutput getSessionToken() throws IntegrationException {
    return enrollOnboardingSession.getData(null);
  }

  public Boolean deleteParticipant(BusinessUser user, boolean isUpdate) throws BusinessException, IntegrationException {
    if (!StringUtility.isStringPopulated(user.getEnrollApplicantId())) {
      throw new BusinessException(ErrorCode.NO_DATA_FOUND);
    }
    if (isUpdate)
      this.userUtility.softDeleteUserImages(user);
    enrollDeleteApplicant.getData(null, user.getEnrollApplicantId());
    user.setEnrollApplicantId(null);
    user.setUserStep(UserStep.ID_IMAGES.getStepId());
    this.editUser(user);
    return true;
  }

  private Long determineUserGender(String genderType) {
    if (StringUtility.isStringPopulated(genderType)) {
      return this.genderService.determineGender(genderType);
    } else
      return null;
  }

  private void updateUserDetailsAndSaveUserImages(BusinessUser businessUser, BusinessEnroll result,
      List<UserImage> userImages, Boolean isEgyptian) throws BusinessException, IOException {
    businessUser = this.userUtility.isOldUserStepGreaterThanNewUserStep(businessUser, UserStep.CLIENT_DATA.getStepId());
    businessUser.setFirstName(result.getFirstName());
    businessUser.setLastName(result.getLastName());
    businessUser.setCountry(result.getCountry());
    businessUser.setCity(result.getCity());
    businessUser.setDateOfBirth(result.getDateOfBirth());
    businessUser.setDateOfIdExpiry(result.getExpirationDate());
    businessUser.setUserId(result.getUserId());
    businessUser.setIdType(result.getAzIdType());
    businessUser.setGenderId(this.determineUserGender(result.getGender()));
    businessUser.setDateOfRelease(result.getReleaseDate());
    var oldIdData = StringUtility.isStringPopulated(businessUser.getIdData())
        ? JsonUtility.fromJson(businessUser.getIdData())
        : null;
    businessUser.setIdData(JsonUtility.toJson(result.getIdData())); // Convert Map to JSON

    AzimutAccount azimutAccount = businessUser.getAzimutAccount();
    if (StringUtility.isStringPopulated(result.getArea()) && StringUtility.isStringPopulated(result.getStreet())) {
      azimutAccount.setAddressAr(result.getStreet() + "," + result.getArea());
      azimutAccount.setAddressEn(result.getStreet() + "," + result.getArea());
    }
    azimutAccount.setClientAML(BooleanUtility.isTrue(result.getAmlBlackList()) ? StringUtility.CLIENT_AML_BLACKLIST
        : StringUtility.CLIENT_AML);
    if (StringUtility.isStringPopulated(result.getProfession()))
      azimutAccount.setOccupation(result.getProfession());
    this.editUser(businessUser);

    this.userUtility.uploadUserImages(userImages, businessUser);
    userImageService.saveImages(userImages);
    result.setVerificationPercentage(businessUser.getVerificationPercentage());
    if (NumberUtility.areIntegerValuesMatching(businessUser.getUserStep(), UserStep.FINISHED.getStepId())
        && !BooleanUtility.isTrue(businessUser.getIsVerified()) && oldIdData != null) {
      // if the user is finished and not verified, we need to check the CSO
      var oldFcn = (String) oldIdData.get("fcn");
      if (StringUtility.isStringPopulated(oldFcn) && !StringUtility.stringsMatch(oldFcn, result.getSerialNumber())) {
        var fraReviews = this.reviewUtility.getFraReviewsByUserId(businessUser.getId());
        for (Review review : fraReviews) {
          if (NumberUtility.areIntegerValuesMatching(review.getReason().getReasonType().getTypeId(),
              ReasonType.CSO.getTypeId())) {
            fraService.checkCso(businessUser);
            postFraActions(businessUser);
            autoAcceptActions(businessUser, false);
          }
        }
      }
    } else if (BooleanUtility.isTrue(businessUser.getIsVerified()) && oldIdData != null) {
      var oldFcn = (String) oldIdData.get("fcn");
      if (StringUtility.isStringPopulated(oldFcn) && !StringUtility.stringsMatch(oldFcn, result.getSerialNumber())) {
        AzimutAccount azimutAccountForFits = new AzimutAccount();
        azimutAccountForFits.setId(businessUser.getId());
        try {
          businessClientDetailsService.addAccountAtTeaComputers(azimutAccountForFits.toAddAccountDto(), businessUser);
        } catch (Exception exception) {
          throw this.exceptionHandler.handleException(exception);
        }
      }
    }
  }

  private MultipartFile getImage(String imagePath) throws IntegrationException {
    String imageBase64 = this.enrollGetImage.getData(null, imagePath);
    byte[] base64 = DatatypeConverter.parseBase64Binary(imageBase64.split(",")[1]);
    return new CustomMultipartFile(base64, imagePath);

  }

  private void validateKycData(String idNumber, String documentNumber) throws BusinessException {
    // id number for national id, document number for passport id
    if (idNumber == null && documentNumber == null)
      throw new BusinessException(ErrorCode.INVALID_FIELD_VALUE);
  }

  private EnrollApplication getApplication(EnrollKycDto kycData) throws IntegrationException, BusinessException {
    EnrollGetApplicationsOutput result;
    if (kycData.getApplicantId() != null) {
      EnrollGetApplicationsByIdInput byIdInput = new EnrollGetApplicationsByIdInput();
      List<String> applicants = Arrays.asList(kycData.getApplicantId());
      byIdInput.setApplicationIds(applicants);
      result = enrollGetApplicationsById.getData(byIdInput);
      if (result.getEntities().size() > 0)
        return result.getEntities().get(0);
    } else {
      Instant hourAgo = Instant.now().minus(10, ChronoUnit.HOURS);
      EnrollGetApplicationsByDateInput input = new EnrollGetApplicationsByDateInput();
      input.setFrom(hourAgo);
      input.setTo(Instant.now());
      result = enrollGetApplications.getData(input);
    }
    for (EnrollApplication r : result.getEntities()) {
      if (r.getNationalIdInfo() != null &&
          StringUtility.stringsMatch(kycData.getIdNumber(), r.getNationalIdInfo().getIdNumber())) {
        return r;
      }
      if (r.getPassportInfo() != null &&
          StringUtility.stringsMatch(kycData.getDocumentNumber(), r.getPassportInfo().getDocumentNumber())) {
        return r;
      }
    }
    throw new BusinessException(ErrorCode.NO_DATA_FOUND, HttpStatus.NOT_FOUND);

  }

  private BusinessUser getUserByDeviceId(String deviceId) throws BusinessException {
    UserDevice userDevice = this.userDeviceService.getUserDeviceByDeviceId(deviceId);
    if (userDevice == null) {
      MyLogger.info("Device Not found from Enroll callback: " + deviceId);
      return null;
    }
    return this.getByUserId(userDevice.getUser().getId());
  }

}
