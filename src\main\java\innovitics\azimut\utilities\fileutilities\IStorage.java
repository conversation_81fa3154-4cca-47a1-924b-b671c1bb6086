package innovitics.azimut.utilities.fileutilities;

import java.io.IOException;
import java.io.InputStream;
import java.util.Map.Entry;

import org.springframework.http.MediaType;
import org.springframework.web.multipart.MultipartFile;

import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.exceptions.BusinessException;

public interface IStorage {
	
	public BlobData uploadFile(MultipartFile file,boolean generateSasToken,String containerName,String subDirectory,boolean useOriginalFileName) throws BusinessException, IOException;

	public BlobData uploadFile(MultipartFile file,boolean generateSasToken,String containerName,String subDirectory,boolean useOriginalFileName,Object... options) throws BusinessException, IOException;
	
	public String generateFileRetrievalUrl(String path,String fileName,String subDirectory,boolean generateWithToken,Object... options) throws IOException;
	
	public String generateFileRetrievalUrl(String path,String fileName,String subDirectory,boolean generateWithToken,Long tokenValidityMinutes,Object... options) throws IOException;
	
	public void deleteFile(String path,String fileName,String subDirectory,boolean generateWithToken,Long tokenValidityInMinutes) throws IOException;

	public void copyFile(String sourceContainerName,String destinationContainerName,String subDirectory,String fileName,boolean generateSasToken) throws IOException;
	
	public void copyWithAbsolutePath(String sourcePath,String destinationPath) throws IOException;
		
	public String generateLocalPath(String containerName,String subDirectory,String fileName,boolean generateSasToken) throws IOException;

	public Entry<MediaType,byte[]> getFileWithAbsolutePath(String path) throws IOException;
	
	public IFileRetrievalService setFileRetrievalService();
	
	public Entry<MediaType,byte[]> getFileUsingIdAndType(BusinessUser businessUser, String validityToken, String documentId,String documentType) throws ClassNotFoundException, BusinessException, IOException;

	public BlobData uploadFileToBlob(InputStream inputStream, boolean generateSasToken, String containerName,String subDirectory, String extension) throws IOException, BusinessException;
	
	public void emptySubDirectory(String container,String subDiectory) throws IOException, BusinessException;
	
	public BlobData uploadFileToBlob(String fileName,InputStream inputStream, boolean generateSasToken, String containerName,String subDirectory, String extension) throws IOException, BusinessException;
	
}
