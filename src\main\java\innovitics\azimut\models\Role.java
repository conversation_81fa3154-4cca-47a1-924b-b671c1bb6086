package innovitics.azimut.models;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import innovitics.azimut.utilities.CustomJsonRootName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Entity
@Table(name = "app_roles")
@Setter
@Getter
@ToString
@CustomJsonRootName(plural = "roles", singular = "role")
public class Role extends DbBaseEntity {
  private String name;
  private String displayName;
  private String permissionsApp;

}
