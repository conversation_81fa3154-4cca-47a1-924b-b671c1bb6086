package innovitics.azimut.businessmodels.funds;

import java.math.BigDecimal;
import java.util.List;

import lombok.Data;

@Data
public class BusinessClientFund {

  private Double quantity;
  private Double avgcost;
  private String tradePrice;
  private Double availableToBuy;
  private Double availableToSell;
  private Long fundId;
  private Long teacomputerId;
  private String fundName;
  private String fundType;
  private Long currencyId;
  private String currencyName;
  private Double currencyRate;
  private BigDecimal totalAmount;
  private String lastPriceUpdateDate;
  private String logo;
  private Double currentRevenue;
  private Double currentRevenuePercent;
  private Boolean buyEnabled;
  private Boolean sellEnabled;
  private List<BusinessFundTransaction> fundTransactions;
  private Long azIdType;
  private String azId;
}
