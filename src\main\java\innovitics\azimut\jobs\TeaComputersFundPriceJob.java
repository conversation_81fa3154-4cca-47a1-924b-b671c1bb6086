package innovitics.azimut.jobs;

import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import innovitics.azimut.businessservices.BusinessFundsChildService;
import innovitics.azimut.businessservices.BusinessFundsService;
import innovitics.azimut.repositories.DBConfigurationRepository;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
@Scope(value = ConfigurableBeanFactory.SCOPE_SINGLETON, proxyMode = ScopedProxyMode.TARGET_CLASS)
public class TeaComputersFundPriceJob extends ParentJob {
  @Autowired
  BusinessFundsService businessFundsService;
  @Autowired
  BusinessFundsChildService businessFundsChildService;
  @Autowired
  DBConfigurationRepository dbConfigurationRepository;

  @Override
  @Scheduled(fixedDelayString = "${tea.computers.job.delay.seconds}", timeUnit = TimeUnit.SECONDS)
  public void scheduleFixedDelayTask() {
    super.scheduleFixedDelayTask();
    try {
      this.getService().updateFundPrices();
      MyLogger.info("Action Finished::::::");
    } catch (Exception exception) {
      MyLogger.info("Could not update the fund prices");
      exception.printStackTrace();
    }
  }

  @Override
  public String getName() {
    return this.getClass().getName();
  }

  BusinessFundsService getService() {
    String value = this.dbConfigurationRepository.findValue("fundPriceUpdate");
    if (StringUtility.stringsMatch(value, BusinessFundsService.class.getSimpleName())) {
      return businessFundsService;
    } else if (StringUtility.stringsMatch(value, BusinessFundsChildService.class.getSimpleName())) {
      return businessFundsChildService;
    }

    return businessFundsService;

  }

}
