package innovitics.azimut.businessmodels.funds;

import innovitics.azimut.utilities.CustomJsonRootName;
import lombok.Data;

@Data
@CustomJsonRootName(plural = "fundPrices", singular = "fundPrice")
public class BusinessFundPrice {
  private Long fundId;
  private Long id;
  private Double nav;
  private Double tradePrice;
  private String priceDate;
  private Long teacomputerId;
  private String logo;
  private Long azIdType;
  private String azId;
  private String searchFromDate;
  private String searchToDate;
  private String sorting;
  private Long bankId;
}
