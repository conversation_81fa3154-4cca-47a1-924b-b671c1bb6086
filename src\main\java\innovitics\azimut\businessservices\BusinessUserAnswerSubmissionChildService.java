package innovitics.azimut.businessservices;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import innovitics.azimut.businessmodels.kyc.BusinessKYCPage;
import innovitics.azimut.businessmodels.kyc.BusinessRelatedAnswer;
import innovitics.azimut.businessmodels.kyc.BusinessReview;
import innovitics.azimut.businessmodels.kyc.BusinessSubmittedAnswer;
import innovitics.azimut.businessmodels.kyc.BusinessUserSubmittedAnswer;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.controllers.kyc.SubmitAnswersDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.models.kyc.UserAnswer;
import innovitics.azimut.utilities.crosslayerenums.AnswerType;
import innovitics.azimut.utilities.crosslayerenums.KycStatus;
import innovitics.azimut.utilities.crosslayerenums.ReviewResult;
import innovitics.azimut.utilities.crosslayerenums.UserStep;
import innovitics.azimut.utilities.datautilities.BooleanUtility;
import innovitics.azimut.utilities.datautilities.ListUtility;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;
import innovitics.azimut.utilities.mapping.kyc.UserAnswersIntermediary;

@Service
public class BusinessUserAnswerSubmissionChildService extends BusinessUserAnswerSubmissionService {

  @Autowired
  ListUtility<BusinessUserSubmittedAnswer> businessUserSubmittedAnswerListUtility;
  @Autowired
  ListUtility<BusinessSubmittedAnswer> businessSubmittedAnswerListUtility;
  @Autowired
  ListUtility<BusinessRelatedAnswer> businessRelatedAnswerListUtility;
  @Autowired
  ListUtility<Double> doubleListUtility;

  @Transactional(rollbackFor = BusinessException.class)
  public BusinessKYCPage submitAnswersWhenUnderReview(BusinessUser businessUser,
      SubmitAnswersDto businessUserAnswerSubmission, BusinessKYCPageService businessKYCPageService,
      String language) throws BusinessException {
    // this.validation.validateWithCustomError(businessUser, KycStatus.PENDING,
    // ErrorCode.USER_UNDER_REVIEW );
    this.validation.validateUserKYCCompletion(businessUser);
    this.validation.checkUserAnswersValidity(businessUserAnswerSubmission);
    List<UserAnswer> oldUserAnswers = this.userAnswerSubmissionService
        .getOldUserAnswers(businessUserAnswerSubmission.getPageId(), businessUser.getId());
    this.populateDocumentName(oldUserAnswers);
    this.processAnswersAndReviewsThenSave(businessUser.getId(), businessUserAnswerSubmission, oldUserAnswers);
    businessUser.setUserStep(UserStep.UNDER_REVIEW.getStepId());

    BusinessKYCPage businessKYCPage = this.setNextPageForMobile(businessUser, businessUserAnswerSubmission,
        businessKYCPageService, language);

    /*
     * if(NumberUtility.areLongValuesMatching(businessUserAnswerSubmission.
     * getNextPageId(), businessUserAnswerSubmission.getPageId()))
     * {
     * if(!this.checkForRejectedReviews(businessUser))
     * {
     * businessUser.setKycStatus(KycStatus.PENDING_CLIENT.getStatusId());
     * }
     *
     * }
     */
    // else
    // {

    businessUser.setKycStatus(KycStatus.PENDING_CLIENT.getStatusId());
    // }
    this.editUser(businessUser);
    // this.userAnswerSubmissionService.removeOldUserAnswers(oldUserAnswers);
    // this.userAnswerSubmissionService.deleteOldUserAnswers(businessUserAnswerSubmission.getPageId(),businessUser.getId());
    businessKYCPage.setVerificationPercentage(100);
    businessKYCPage.setNextUserStep(this.reviewUtility.calculateUserStepUnderReview(businessUser));

    return businessKYCPage;
  }

  public void processAnswersAndReviewsThenSave(Long userId, SubmitAnswersDto businessUserAnswerSubmission,
      List<UserAnswer> oldUserAnswers) throws BusinessException {
    List<String> textAnswerTypes = AnswerType.getAnswerTypes(true);
    List<String> choiceAnswerTypes = AnswerType.getAnswerTypes(false);
    List<BusinessReview> reviewsToBeRemoved = new ArrayList<BusinessReview>();
    boolean relatedAnswersExist = false;

    List<UserAnswersIntermediary> userAnswersIntermediaries = userAnswerMapper
        .convertBusinessUserAnswerSubmissionToUserAnswerList(userId, businessUserAnswerSubmission);
    List<UserAnswer> userAnswers = new ArrayList<>();
    List<UserAnswer> relatedAnswers = new ArrayList<>();
    List<UserAnswer> oldParentUserAnswers = new ArrayList<>();
    List<UserAnswer> oldRelatedUserAnswers = new ArrayList<>();
    relatedAnswersExist = this.populateChildAndParentAnswers(userAnswersIntermediaries, userAnswers, relatedAnswers);
    this.populateDocumentName(businessUserAnswerSubmission.getUserAnswers());
    this.populateOldChildAndParentAnswers(oldUserAnswers, oldParentUserAnswers, oldRelatedUserAnswers);
    if (userAnswerListUtility.isListPopulated(oldUserAnswers)) {
      for (BusinessUserSubmittedAnswer businessUserSubmittedAnswer : businessUserAnswerSubmission.getUserAnswers()) {
        if (this.detectIfAnswersAreDifferent(relatedAnswersExist, businessUserSubmittedAnswer, oldParentUserAnswers,
            oldRelatedUserAnswers, textAnswerTypes, choiceAnswerTypes)) {
          reviewsToBeRemoved.add(businessUserSubmittedAnswer.getUserReview());
        }

      }
    }
    // this.reviewUtility.softDeleteReviews(reviewsToBeRemoved);
    if (this.reviewListUtility.isListPopulated(reviewsToBeRemoved)) {
      this.reviewUtility.softDeleteReviewsUsingIds(
          reviewsToBeRemoved.stream().map(BusinessReview::getId).collect(Collectors.toList()));
    }
    this.userAnswerSubmissionService.deleteOldUserAnswers(businessUserAnswerSubmission.getPageId(), userId);
    this.processAndSaveAnswers(relatedAnswersExist, userAnswersIntermediaries);

  }

  boolean detectIfAnswersAreDifferent(boolean relatedAnswersExit,
      BusinessUserSubmittedAnswer businessUserSubmittedAnswer, List<UserAnswer> oldParentUserAnswers,
      List<UserAnswer> oldRelatedUserAnswers, List<String> textAnswerTypes, List<String> choiceAnswerTypes)
      throws BusinessException {
    boolean areAnswersIdentical = false;
    boolean areChoicesIdentical = false;
    boolean areTextsIdentical = true;
    String currentAnswerType = businessUserSubmittedAnswer.getAnswers()[0].getAnswerType();

    MyLogger.info("Detecing answer Differences for Question:::" + businessUserSubmittedAnswer.toString());
    List<UserAnswer> oldUserAnswersPerQuestion = new ArrayList<UserAnswer>();
    oldUserAnswersPerQuestion = oldParentUserAnswers.stream()
        .filter(
            o -> NumberUtility.areLongValuesMatching(o.getQuestionId(), businessUserSubmittedAnswer.getQuestionId()))
        .collect(Collectors.toList());

    areChoicesIdentical = this.detectIfChoiceAnswerIsDifferent(businessUserSubmittedAnswer, oldUserAnswersPerQuestion);
    if (textAnswerTypes.contains(currentAnswerType)) {
      areTextsIdentical = this.detectIfTextAnswerIsDifferent(businessUserSubmittedAnswer, oldUserAnswersPerQuestion);
    }
    areAnswersIdentical = areChoicesIdentical && areTextsIdentical;

    if (isReviewStatus(businessUserSubmittedAnswer, ReviewResult.REJECTED)) {
      if (areAnswersIdentical) {
        if (relatedAnswersExit) {
          boolean areRelatedAnswersDifferent = this.checkRelatedAnswerDifference(businessUserSubmittedAnswer,
              oldRelatedUserAnswers, textAnswerTypes, choiceAnswerTypes);
          if (!areRelatedAnswersDifferent) {
            throw new BusinessException(ErrorCode.REJECTED_ANSWER_NOT_CHANGED);
          } else if (areRelatedAnswersDifferent) {
            return true;
          }

        } else {
          throw new BusinessException(ErrorCode.REJECTED_ANSWER_NOT_CHANGED);
        }
      } else {
        return true;
      }
    } else if (isReviewStatus(businessUserSubmittedAnswer, ReviewResult.APPROVED)) {
      if (areAnswersIdentical) {
        if (relatedAnswersExit) {
          boolean areRelatedAnswersDifferent = this.checkRelatedAnswerDifference(businessUserSubmittedAnswer,
              oldRelatedUserAnswers, textAnswerTypes, choiceAnswerTypes);
          if (areRelatedAnswersDifferent) {
            return true;
          }
        }
      } else if (!areAnswersIdentical) {
        return true;
      }
    }
    return false;
  }

  boolean checkRelatedAnswerDifference(BusinessUserSubmittedAnswer businessUserSubmittedAnswer,
      List<UserAnswer> oldRelatedUserAnswers, List<String> textAnswerTypes, List<String> choiceAnswerTypes)
      throws BusinessException {
    boolean areRelatedAnswersDifferent = false;
    for (BusinessSubmittedAnswer businessSubmittedAnswer : businessUserSubmittedAnswer.getAnswers()) {
      areRelatedAnswersDifferent = this.detectIfRelatedAnswersAreDifferent(businessSubmittedAnswer,
          oldRelatedUserAnswers, businessUserSubmittedAnswer.getUserReview(), textAnswerTypes, choiceAnswerTypes);
      if (areRelatedAnswersDifferent) {
        MyLogger.info("Related answer was different, returning true.");
        break;
      }
    }
    return areRelatedAnswersDifferent;
  }

  boolean detectIfRelatedAnswersAreDifferent(BusinessSubmittedAnswer businessSubmittedAnswer,
      List<UserAnswer> oldAnswers, BusinessReview businessReview, List<String> textAnswerTypes,
      List<String> choiceAnswerTypes) throws BusinessException {
    boolean areAnswersDifferent = false;

    if (this.arrayUtility.isArrayPopulated(businessSubmittedAnswer.getRelatedAnswers())) {
      String currentRelatedAnswerType = "";
      currentRelatedAnswerType = businessSubmittedAnswer.getRelatedAnswers()[0].getRelatedAnswerType();
      MyLogger.info("Detecing differences for Answer:::" + businessSubmittedAnswer.toString());
      List<UserAnswer> oldRelatedUserAnswersPerAnswer = new ArrayList<UserAnswer>();
      oldRelatedUserAnswersPerAnswer = oldAnswers.stream()
          .filter(
              o -> NumberUtility.areLongValuesMatching(o.getRelatedAnswerId(), businessSubmittedAnswer.getAnswerId()))
          .collect(Collectors.toList());

      boolean areChoicesIdentical = this.detectIfRelatedChoiceAnswerIsDifferent(businessSubmittedAnswer,
          oldRelatedUserAnswersPerAnswer);

      if (textAnswerTypes.contains(currentRelatedAnswerType)) {
        boolean areTextsIdentical = this.detectIfRelatedTextAnswerIsDifferent(businessSubmittedAnswer,
            oldRelatedUserAnswersPerAnswer);
        areAnswersDifferent = !areChoicesIdentical || !areTextsIdentical;
      } else {
        areAnswersDifferent = !areChoicesIdentical;
      }
    }
    return areAnswersDifferent;
  }

  boolean populateChildAndParentAnswers(List<UserAnswersIntermediary> userAnswersIntermediaries,
      List<UserAnswer> userAnswers, List<UserAnswer> relatedAnswers) {
    boolean relatedAnswersExist = false;
    for (UserAnswersIntermediary userAnswersIntermediary : userAnswersIntermediaries) {
      MyLogger.info("User Answer Int:::" + userAnswersIntermediary.toString());
      this.copyAnswerIfDocument(userAnswersIntermediary.getParentAnswer());
      userAnswers.add(userAnswersIntermediary.getParentAnswer());

      if (userAnswerListUtility.isListPopulated(userAnswersIntermediary.getRelatedAnswers())) {
        relatedAnswersExist = true;
        for (UserAnswer userAnswer : userAnswersIntermediary.getRelatedAnswers()) {
          this.copyAnswerIfDocument(userAnswer);
        }
        relatedAnswers.addAll(userAnswersIntermediary.getRelatedAnswers());
      }
    }
    return relatedAnswersExist;
  }

  void populateOldChildAndParentAnswers(List<UserAnswer> oldUserAnswers, List<UserAnswer> oldParentUserAnswers,
      List<UserAnswer> oldRelatedUserAnswers) {
    if (userAnswerListUtility.isListPopulated(oldUserAnswers)) {
      for (UserAnswer userAnswer : oldUserAnswers) {
        if (userAnswer != null) {
          if (userAnswer.getQuestionId() != null) {
            oldParentUserAnswers.add(userAnswer);
          } else if (userAnswer.getRelatedAnswerId() != null) {
            oldRelatedUserAnswers.add(userAnswer);
          }

        }

      }
    }
  }

  private boolean detectIfTextAnswerIsDifferent(BusinessUserSubmittedAnswer businessUserSubmittedAnswer,
      List<UserAnswer> oldUserAnswersPerQuestion) {
    List<String> oldUserAnswerValues = oldUserAnswersPerQuestion.stream().map(UserAnswer::getAnswerValue)
        .collect(Collectors.toList());
    List<String> newUserAnswerValues = (Arrays.asList(businessUserSubmittedAnswer.getAnswers())).stream()
        .map(BusinessSubmittedAnswer::getAnswerValue).collect(Collectors.toList());
    MyLogger.info("oldUserAnswerValues:::" + oldUserAnswerValues.toString());
    MyLogger.info("newUserAnswerValues:::" + newUserAnswerValues.toString());

    boolean areTheTwoListsIdentical = StringUtility.areTheTwoListsIdentical(oldUserAnswerValues, newUserAnswerValues);
    return areTheTwoListsIdentical
        && this.areDocumentSizesIdentical(businessUserSubmittedAnswer, oldUserAnswersPerQuestion);
  }

  private boolean detectIfRelatedTextAnswerIsDifferent(BusinessSubmittedAnswer businessSubmittedAnswer,
      List<UserAnswer> oldRelatedUserAnswersPerAnswer) {
    List<String> oldUserAnswerValues = oldRelatedUserAnswersPerAnswer.stream().map(UserAnswer::getAnswerValue)
        .collect(Collectors.toList());
    List<String> newRelatedUserAnswerValues = (Arrays.asList(businessSubmittedAnswer.getRelatedAnswers())).stream()
        .map(BusinessRelatedAnswer::getRelatedAnswerValue).collect(Collectors.toList());
    boolean areTheTwoListsIdentical = StringUtility.areTheTwoListsIdentical(oldUserAnswerValues,
        newRelatedUserAnswerValues);
    return areTheTwoListsIdentical
        && this.areRelatedDocumentSizesIdentical(businessSubmittedAnswer, oldRelatedUserAnswersPerAnswer);
  }

  private boolean detectIfChoiceAnswerIsDifferent(BusinessUserSubmittedAnswer businessUserSubmittedAnswer,
      List<UserAnswer> oldUserAnswersPerQuestion) {
    List<Long> oldUserAnswerIds = oldUserAnswersPerQuestion.stream().map(UserAnswer::getAnswerId)
        .collect(Collectors.toList());
    List<Long> newUserAnswerIds = (Arrays.asList(businessUserSubmittedAnswer.getAnswers())).stream()
        .map(BusinessSubmittedAnswer::getAnswerId).collect(Collectors.toList());
    boolean areTheTwoListsIdentical = NumberUtility.areTheTwoListsIdentical(oldUserAnswerIds, newUserAnswerIds);
    return areTheTwoListsIdentical;
  }

  private boolean detectIfRelatedChoiceAnswerIsDifferent(BusinessSubmittedAnswer businessSubmittedAnswer,
      List<UserAnswer> oldRelatedUserAnswersPerAnswer) {
    List<Long> oldUserAnswerIds = oldRelatedUserAnswersPerAnswer.stream().map(UserAnswer::getAnswerId)
        .collect(Collectors.toList());
    List<Long> newRelatedUserAnswerIds = (Arrays.asList(businessSubmittedAnswer.getRelatedAnswers())).stream()
        .map(BusinessRelatedAnswer::getRelatedAnswerId).collect(Collectors.toList());
    boolean areTheTwoListsIdentical = NumberUtility.areTheTwoListsIdentical(oldUserAnswerIds, newRelatedUserAnswerIds);
    return areTheTwoListsIdentical;
  }

  boolean isReviewStatus(BusinessUserSubmittedAnswer businessUserSubmittedAnswer, ReviewResult reviewResult) {
    return businessUserSubmittedAnswer.getUserReview() != null && NumberUtility
        .areLongValuesMatching(businessUserSubmittedAnswer.getUserReview().getStatus(), reviewResult.getResultId());
  }

  boolean isReviewStatus(BusinessReview businessReview, ReviewResult reviewResult) {
    return NumberUtility.areLongValuesMatching(businessReview.getStatus(), reviewResult.getResultId());
  }

  public void processAndSaveAnswers(boolean relatedAnswersExist,
      List<UserAnswersIntermediary> userAnswersIntermediaries) throws BusinessException {

    LinkedList<UserAnswer> userAnswers = new LinkedList<>();
    LinkedList<UserAnswer> relatedAnswers = new LinkedList<>();

    for (UserAnswersIntermediary userAnswersIntermediary : userAnswersIntermediaries) {
      MyLogger.info("User Answer Int:::" + userAnswersIntermediary.toString());
      userAnswers.add(userAnswersIntermediary.getParentAnswer());

      if (userAnswerListUtility.isListPopulated(userAnswersIntermediary.getRelatedAnswers())) {
        relatedAnswersExist = true;
        relatedAnswers.addAll(userAnswersIntermediary.getRelatedAnswers());
      }

    }
    this.submitAnswers(userAnswers);

    if (relatedAnswersExist) {
      this.submitAnswers(relatedAnswers);
    }

  }

  public BusinessKYCPage setNextPageForMobile(BusinessUser businessUser,
      SubmitAnswersDto businessUserAnswerSubmission, BusinessKYCPageService businessKYCPageService,
      String language) throws BusinessException {
    BusinessKYCPage businessKYCPage = new BusinessKYCPage();
    if (BooleanUtility.isTrue(businessUserAnswerSubmission.getIsMobile())) {
      MyLogger.info("Mobile true:::");
      if (!NumberUtility.areLongValuesMatching(businessUserAnswerSubmission.getNextPageId(),
          businessUserAnswerSubmission.getPageId())) {
        MyLogger.info("Page Ids not similar:::");
        businessKYCPage = businessKYCPageService.getKycPagebyId(businessUser,
            businessUserAnswerSubmission.getNextPageId(), false, language);
      } else {
        businessKYCPage.setNextId(businessUserAnswerSubmission.getNextPageId());
        businessKYCPage.setId(businessUserAnswerSubmission.getNextPageId());
      }
    }
    MyLogger.info("Page::::" + businessKYCPage.toString());
    return businessKYCPage;
  }

  boolean areDocumentSizesIdentical(BusinessUserSubmittedAnswer businessUserSubmittedAnswer,
      List<UserAnswer> oldUserAnswersPerQuestion) {
    MyLogger.info("are documents identical???");
    if (StringUtility.stringsMatch(oldUserAnswersPerQuestion.get(0).getAnswerType(), AnswerType.DOCUMENT.getType())) {
      List<String> oldUserAnswerDocumentSizes = oldUserAnswersPerQuestion.stream().map(UserAnswer::getDocumentSize)
          .collect(Collectors.toList());

      List<Double> newUserAnswerDocumentSizes = (Arrays.asList(businessUserSubmittedAnswer.getAnswers())).stream()
          .map(BusinessSubmittedAnswer::getDocumentSize).collect(Collectors.toList());

      List<String> newUserAnswerDocumentStringSizes = new ArrayList<String>();

      if (doubleListUtility.isListPopulated(newUserAnswerDocumentSizes)) {
        for (Double value : newUserAnswerDocumentSizes) {
          newUserAnswerDocumentStringSizes.add(value.toString());
        }
      }

      boolean areTheTwoListsIdentical = StringUtility.areTheTwoListsIdentical(oldUserAnswerDocumentSizes,
          newUserAnswerDocumentStringSizes);
      MyLogger.info("result:::" + areTheTwoListsIdentical);

      return areTheTwoListsIdentical;
    } else
      return true;
  }

  boolean areRelatedDocumentSizesIdentical(BusinessSubmittedAnswer businessSubmittedAnswer,
      List<UserAnswer> oldRelatedUserAnswersPerAnswer) {
    MyLogger.info("are documents identical???");
    if (StringUtility.stringsMatch(oldRelatedUserAnswersPerAnswer.get(0).getAnswerType(),
        AnswerType.DOCUMENT.getType())) {
      List<String> oldRelatedDocumentSizes = oldRelatedUserAnswersPerAnswer.stream().map(UserAnswer::getDocumentSize)
          .collect(Collectors.toList());
      List<Double> newRelatedUserAnswerValues = (Arrays.asList(businessSubmittedAnswer.getRelatedAnswers())).stream()
          .map(BusinessRelatedAnswer::getDocumentSize).collect(Collectors.toList());
      List<String> newRelatedUserAnswerDocumentStringSizes = new ArrayList<String>();

      if (doubleListUtility.isListPopulated(newRelatedUserAnswerValues)) {
        for (Double value : newRelatedUserAnswerValues) {
          newRelatedUserAnswerDocumentStringSizes.add(value.toString());
        }
      }
      boolean areTheTwoListsIdentical = StringUtility.areTheTwoListsIdentical(oldRelatedDocumentSizes,
          newRelatedUserAnswerDocumentStringSizes);
      MyLogger.info("result:::" + areTheTwoListsIdentical);

      return areTheTwoListsIdentical;
    } else
      return true;
  }

  void populateDocumentName(List<UserAnswer> userAnswers) {
    if (userAnswerListUtility.isListPopulated(userAnswers)) {
      MyLogger.info("List populated::::");
      for (UserAnswer userAnswer : userAnswers) {
        MyLogger.info("UserAnswer::::" + userAnswer.toString());
        if (answerTypeUtility.isAnswerTypeMatching(userAnswer, AnswerType.DOCUMENT)) {
          MyLogger.info("type::::" + userAnswer.getAnswerType());
          String documentName = userAnswer.getDocumentName();
          userAnswer.setAnswerValue(documentName);
          MyLogger.info("UserAnswer::::" + userAnswer.toString());
        }
      }
    }

  }

  void populateDocumentName(BusinessUserSubmittedAnswer[] businessUserSubmittedAnswers) {
    if (this.arrayUtility.isArrayPopulated(businessUserSubmittedAnswers)) {
      for (BusinessUserSubmittedAnswer businessUserSubmittedAnswer : businessUserSubmittedAnswers) {
        if (this.arrayUtility.isArrayPopulated(businessUserSubmittedAnswer.getAnswers())) {
          for (BusinessSubmittedAnswer businessSubmittedAnswer : businessUserSubmittedAnswer.getAnswers()) {
            if (StringUtility.stringsMatch(businessSubmittedAnswer.getAnswerType(), AnswerType.DOCUMENT.getType())) {
              String documentName = businessSubmittedAnswer.getDocumentName();
              businessSubmittedAnswer.setAnswerValue(documentName);
            }
            if (this.arrayUtility.isArrayPopulated(businessSubmittedAnswer.getRelatedAnswers())) {
              for (BusinessRelatedAnswer businessRelatedAnswer : businessSubmittedAnswer.getRelatedAnswers()) {
                if (StringUtility.stringsMatch(businessRelatedAnswer.getRelatedAnswerType(),
                    AnswerType.DOCUMENT.getType())) {
                  String documentName = businessRelatedAnswer.getDocumentName();
                  businessRelatedAnswer.setRelatedAnswerValue(documentName);
                }
              }
            }

          }
        }
      }
    }
  }

  protected void submitAnswers(List<UserAnswer> userAnswers) {
    this.userAnswerSubmissionService.submitAnswers(userAnswers);
  }

}
