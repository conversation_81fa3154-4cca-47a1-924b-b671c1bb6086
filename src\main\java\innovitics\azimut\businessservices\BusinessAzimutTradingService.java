package innovitics.azimut.businessservices;

import java.io.IOException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import innovitics.azimut.businessmodels.trading.BaseAzimutTrading;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.controllers.users.DTOs.CancelOrderDto;
import innovitics.azimut.controllers.users.DTOs.PlaceOrderDto;
import innovitics.azimut.controllers.users.DTOs.WithdrawDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.models.user.UserBlockage;
import innovitics.azimut.repositories.partners.PartnerRepository;
import innovitics.azimut.rest.apis.teacomputers.TeaComputersCancelOrderApi;
import innovitics.azimut.rest.apis.teacomputers.TeaComputersInjectApi;
import innovitics.azimut.rest.apis.teacomputers.TeaComputersPlaceOrderApi;
import innovitics.azimut.rest.apis.teacomputers.TeaComputersWithdrawApi;
import innovitics.azimut.services.FundService;
import innovitics.azimut.services.NavService;
import innovitics.azimut.utilities.crosslayerenums.ModuleType;
import innovitics.azimut.utilities.crosslayerenums.OrderType;
import innovitics.azimut.utilities.datautilities.BooleanUtility;
import innovitics.azimut.utilities.datautilities.DateUtility;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class BusinessAzimutTradingService extends AbstractBusinessService<BaseAzimutTrading> {
  @Autowired
  private TeaComputersPlaceOrderApi placeOrderApi;
  @Autowired
  private TeaComputersCancelOrderApi cancelOrderApi;
  @Autowired
  private FundService fundService;
  @Autowired
  private TeaComputersInjectApi injectApi;
  @Autowired
  private TeaComputersWithdrawApi withdrawApi;
  @Autowired
  private PartnerRepository partnerRepository;
  @Autowired
  protected NavService navService;

  public BaseAzimutTrading placeOrder(BusinessUser tokenizedBusinessUser, BaseAzimutTrading baseAzimutTrading)
      throws IntegrationException, BusinessException {
    BaseAzimutTrading responseBaseAzimutTrading = (BaseAzimutTrading) (this.userBlockageUtility.checkUserBlockage(
        this.configProperties.getBlockageNumberOfTrialsInt(),
        this.configProperties.getBlockageDurationInMinutes(), tokenizedBusinessUser, userMapper,
        this, "placeOrderRest",
        new Object[] { tokenizedBusinessUser, baseAzimutTrading },
        new Class<?>[] { BusinessUser.class, PlaceOrderDto.class },
        ErrorCode.OPERATION_FAILURE));
    return responseBaseAzimutTrading;

  }

  public BaseAzimutTrading inject(BusinessUser tokenizedBusinessUser, BaseAzimutTrading baseAzimutTrading)
      throws IntegrationException, BusinessException, IOException {
    BaseAzimutTrading responseBaseAzimutTrading = (BaseAzimutTrading) (this.userBlockageUtility.checkUserBlockage(
        this.configProperties.getBlockageNumberOfTrialsInt(),
        this.configProperties.getBlockageDurationInMinutes(), tokenizedBusinessUser, userMapper,
        this, "injectRest",
        new Object[] { tokenizedBusinessUser, baseAzimutTrading },
        new Class<?>[] { BusinessUser.class, BaseAzimutTrading.class },
        ErrorCode.OPERATION_FAILURE));
    return responseBaseAzimutTrading;

  }

  public boolean isOtpRequired(Double amount, Integer currency) {
    return false;
    // Todo: enable in next app release when it is fixed
    // if (NumberUtility.areLongValuesMatching(
    // currency.longValue(), CurrencyType.US_DOLLAR.getTypeId()))
    // return amount > 10000;
    // else
    // return amount > 500000;
  }

  public BaseAzimutTrading placeOrderRest(BusinessUser tokenizedBusinessUser, PlaceOrderDto baseAzimutTrading)
      throws IntegrationException, Exception {
    String otp = baseAzimutTrading.getOtp();
    if (NumberUtility.areLongValuesMatching(baseAzimutTrading.getOrderTypeId(),
        Long.valueOf(OrderType.BUY.getTypeId()))) {
      var fund = fundService.getFundsByTeacomputerId(baseAzimutTrading.getTeacomputerId());
      if (!fund.isPresent()) {
        throw new IntegrationException(ErrorCode.OPERATION_NOT_PERFORMED);
      } else if (BooleanUtility.isFalse(fund.get().isBuyEnabled())) {
        if (isOtpRequired(baseAzimutTrading.getOrderValue(), fund.get().getCurrencyId())
            && !this.userUtility.validateOTP(otp, tokenizedBusinessUser.getUserPhone())) {
          throw new BusinessException(ErrorCode.INVALID_OTP);
        }
        throw new IntegrationException(ErrorCode.OPERATION_NOT_PERFORMED);
      }
    } else if (NumberUtility.areLongValuesMatching(baseAzimutTrading.getOrderTypeId(),
        Long.valueOf(OrderType.SELL.getTypeId()))) {
      var fund = fundService.getFundsByTeacomputerId(baseAzimutTrading.getTeacomputerId());
      if (!StringUtility.isStringPopulated(tokenizedBusinessUser.getSignedPdf())
          && BooleanUtility.isFalse(tokenizedBusinessUser.getIsOld())) {
        throw new BusinessException(ErrorCode.CONTRACT_NOT_SIGNED);
      } else if (BooleanUtility.isFalse(fund.get().isSellEnabled())) {
        var nav = navService.getLastByFundId(fund.get().getId());
        Double amount = nav.getNav() * baseAzimutTrading.getQuantity();
        if (isOtpRequired(amount, fund.get().getCurrencyId())
            && !this.userUtility.validateOTP(otp, tokenizedBusinessUser.getUserPhone())) {
          throw new BusinessException(ErrorCode.INVALID_OTP);
        }
        throw new IntegrationException(ErrorCode.OPERATION_NOT_PERFORMED);
      }
    }
    BaseAzimutTrading responseBaseAzimutTrading = new BaseAzimutTrading();
    try {
      var azimutTrading = this.prepareOrderPlacingRequest(tokenizedBusinessUser, baseAzimutTrading);
      if (baseAzimutTrading.getPartnerId() != null) {
        var partner = partnerRepository.getById(baseAzimutTrading.getPartnerId());
        azimutTrading.setPartnerUserName(partner.getUserName());
      }
      var request = this.placeOrderApi.createPlaceOrderRequest(azimutTrading);
      var response = this.placeOrderApi.getData(request);
      responseBaseAzimutTrading.setTransactionId(response.getTransactionID());
    } catch (Exception exception) {
      throw this.exceptionHandler.handleException(exception);
    }

    return responseBaseAzimutTrading;
  }

  public BaseAzimutTrading injectRest(BusinessUser tokenizedBusinessUser, BaseAzimutTrading baseAzimutTrading)
      throws Exception {
    BaseAzimutTrading responseBaseAzimutTrading = new BaseAzimutTrading();
    responseBaseAzimutTrading.setOrderValue(baseAzimutTrading.getOrderValue());
    responseBaseAzimutTrading.setCurrencyId(baseAzimutTrading.getCurrencyId());
    responseBaseAzimutTrading.setAccountId(baseAzimutTrading.getAccountId());
    try {
      var businessAzimutTrading = this.prepareInjectInputs(tokenizedBusinessUser, baseAzimutTrading);
      var injectRequest = this.injectApi.generateInjectRequest(businessAzimutTrading);
      var injectResponse = this.injectApi.getData(injectRequest, StringUtility.INFORM_DEPOSIT);
      responseBaseAzimutTrading.setOrderId(Long.valueOf(injectResponse.getOrderId()));
    } catch (Exception exception) {
      throw this.exceptionHandler.handleException(exception);
    }

    return responseBaseAzimutTrading;

  }

  public BaseAzimutTrading withdrawRest(BusinessUser tokenizedBusinessUser, WithdrawDto baseAzimutTrading)
      throws IntegrationException, BusinessException, Exception {
    BaseAzimutTrading responseBaseAzimutTrading = new BaseAzimutTrading();
    responseBaseAzimutTrading.setOrderValue(baseAzimutTrading.getOrderValue());
    responseBaseAzimutTrading.setCurrencyId(baseAzimutTrading.getCurrencyId());
    responseBaseAzimutTrading.setAccountId(baseAzimutTrading.getAccountId());
    try {
      var withdrawRequest = this.withdrawApi.generateWithdrawRequest(tokenizedBusinessUser, baseAzimutTrading);
      var withdrawResponse = this.withdrawApi.getData(withdrawRequest, StringUtility.INFORM_WITHDRAW);
      responseBaseAzimutTrading.setOrderId(Long.valueOf(withdrawResponse.getOrderId()));
    } catch (Exception exception) {
      throw this.exceptionHandler.handleException(exception);
    }

    return responseBaseAzimutTrading;
  }

  public BaseAzimutTrading getUserBlockage(BusinessUser tokenizedBusinessUser, String userPhone)
      throws BusinessException {
    BaseAzimutTrading baseAzimutTrading = new BaseAzimutTrading();

    try {
      if (!StringUtility.isStringPopulated(userPhone))
        baseAzimutTrading
            .setUserBlockage(this.userBlockageUtility.getUserBlockage(tokenizedBusinessUser.getId(), true));
      else
        baseAzimutTrading.setUserBlockage(this.phoneNumberBlockageUtility.getUserBlockage(userPhone, true));
    } catch (Exception exception) {
      this.exceptionHandler.getNullIfNonExistent(exception);
    }
    this.populateThreshold(baseAzimutTrading);
    return baseAzimutTrading;
  }

  public BaseAzimutTrading incrementUserBlockage(BusinessUser tokenizedBusinessUser) throws BusinessException {
    return null;
  }

  public BaseAzimutTrading incrementUserBlockage(BusinessUser tokenizedBusinessUser, String userPhone)
      throws BusinessException {
    boolean hasToken = tokenizedBusinessUser != null && !StringUtility.isStringPopulated(userPhone);

    BaseAzimutTrading baseAzimutTrading = new BaseAzimutTrading();
    UserBlockage userBlockage = new UserBlockage();
    try {
      if (hasToken) {
        userBlockage = this.userBlockageUtility.getUserBlockage(tokenizedBusinessUser.getId(), false);
      } else {
        userBlockage = this.phoneNumberBlockageUtility.getUserBlockage(userPhone, false);
      }

      if (userBlockage == null) {

        MyLogger.info("User Blockage none existent::");
        UserBlockage addedUserBlockage = new UserBlockage();

        if (hasToken) {
          addedUserBlockage = this.userBlockageUtility
              .addUserBlockage(this.userMapper.convertBusinessUnitToBasicUnit(tokenizedBusinessUser, false));
        } else {
          addedUserBlockage = this.phoneNumberBlockageUtility.addUserBlockage(userPhone);
        }

        addedUserBlockage.setUser(null);
        baseAzimutTrading.setUserBlockage(addedUserBlockage);
      } else {
        MyLogger.info("User Blockage::" + userBlockage.toString());
        if (DateUtility.getMinutesBefore(this.configProperties.getBlockageDurationInMinutes())
            .before(userBlockage.getUpdatedAt())) {

          if (userBlockage.getErrorCount() != null
              && (userBlockage.getErrorCount() < this.configProperties.getBlockageNumberOfTrialsInt())) {
            int oldUserCount = userBlockage.getErrorCount().intValue();
            userBlockage.setErrorCount(oldUserCount + 1);
          } else if (NumberUtility.areIntegerValuesMatching(
              this.configProperties.getBlockageNumberOfTrialsInt(),
              userBlockage.getErrorCount())) {
            userBlockage.setErrorCount(this.configProperties.getBlockageNumberOfTrialsInt());
          }
        } else if (!DateUtility.getMinutesBefore(this.configProperties.getBlockageDurationInMinutes())
            .before(userBlockage.getUpdatedAt())) {
          userBlockage.setErrorCount(1);
        }

        if (hasToken) {
          userBlockage.setUser(this.userMapper.convertBusinessUnitToBasicUnit(tokenizedBusinessUser, false));
        }

        this.userBlockageUtility.updateUserBlockage(userBlockage);
        userBlockage.setUser(null);

        baseAzimutTrading.setUserBlockage(userBlockage);
      }

      this.populateThreshold(baseAzimutTrading);
      return baseAzimutTrading;
    } catch (Exception exception) {
      throw this.handleBusinessException(exception, ErrorCode.OPERATION_NOT_PERFORMED);
    }

  }

  public BaseAzimutTrading incrementUserBlockageUsingPhoneNumber(String userPhone) throws BusinessException {
    BaseAzimutTrading baseAzimutTrading = new BaseAzimutTrading();
    try {
      UserBlockage userBlockage = this.phoneNumberBlockageUtility.getUserBlockage(userPhone, false);
      if (userBlockage == null) {
        MyLogger.info("User Blockage none existent::");
        UserBlockage addedUserBlockage = this.phoneNumberBlockageUtility.addUserBlockage(userPhone);
        addedUserBlockage.setUser(null);
        baseAzimutTrading.setUserBlockage(addedUserBlockage);
      } else {
        MyLogger.info("User Blockage::" + userBlockage.toString());
        if (DateUtility.getMinutesBefore(this.configProperties.getBlockageDurationInMinutes())
            .before(userBlockage.getUpdatedAt())) {

          if (userBlockage.getErrorCount() != null
              && (userBlockage.getErrorCount() < this.configProperties.getBlockageNumberOfTrialsInt())) {
            int oldUserCount = userBlockage.getErrorCount().intValue();
            userBlockage.setErrorCount(oldUserCount + 1);
          } else if (NumberUtility.areIntegerValuesMatching(
              this.configProperties.getBlockageNumberOfTrialsInt(),
              userBlockage.getErrorCount())) {
            userBlockage.setErrorCount(this.configProperties.getBlockageNumberOfTrialsInt());
          }
        } else if (!DateUtility.getMinutesBefore(this.configProperties.getBlockageDurationInMinutes())
            .before(userBlockage.getUpdatedAt())) {
          userBlockage.setErrorCount(1);
        }
        this.phoneNumberBlockageUtility.updateUserBlockage(userBlockage);
        baseAzimutTrading.setUserBlockage(userBlockage);
      }
      this.populateThreshold(baseAzimutTrading);
      return baseAzimutTrading;
    } catch (Exception exception) {
      throw this.handleBusinessException(exception, ErrorCode.OPERATION_NOT_PERFORMED);
    }

  }

  private BaseAzimutTrading prepareOrderPlacingRequest(BusinessUser tokenizedBusinessUser,
      PlaceOrderDto baseAzimutTrading) throws Exception {

    BaseAzimutTrading addBaseAzimutTrading = new BaseAzimutTrading();
    addBaseAzimutTrading.setAzId(tokenizedBusinessUser.getUserId());
    addBaseAzimutTrading.setAzIdType(this.getAzimutUserTypeId(tokenizedBusinessUser));
    addBaseAzimutTrading.setOrderTypeId(baseAzimutTrading.getOrderTypeId());
    addBaseAzimutTrading.setOrderValue(baseAzimutTrading.getOrderValue());
    addBaseAzimutTrading.setQuantity(baseAzimutTrading.getQuantity());
    addBaseAzimutTrading.setFundId(baseAzimutTrading.getTeacomputerId());
    return addBaseAzimutTrading;
  }

  private BaseAzimutTrading prepareOrderPlacingRequest(BusinessUser tokenizedBusinessUser,
      CancelOrderDto baseAzimutTrading) throws Exception {

    BaseAzimutTrading addBaseAzimutTrading = new BaseAzimutTrading();
    addBaseAzimutTrading.setAzId(tokenizedBusinessUser.getUserId());
    addBaseAzimutTrading.setAzIdType(this.getAzimutUserTypeId(tokenizedBusinessUser));
    addBaseAzimutTrading.setTransactionId(baseAzimutTrading.getTransactionId());
    return addBaseAzimutTrading;
  }

  private BaseAzimutTrading prepareInjectInputs(BusinessUser tokenizedBusinessUser,
      BaseAzimutTrading baseAzimutTrading) throws Exception {

    BaseAzimutTrading addBaseAzimutTrading = new BaseAzimutTrading();

    if (baseAzimutTrading != null) {
      addBaseAzimutTrading.setAzId(tokenizedBusinessUser.getUserId());
      addBaseAzimutTrading.setAzIdType(this.getAzimutUserTypeId(tokenizedBusinessUser));
      addBaseAzimutTrading.setOrderValue(baseAzimutTrading.getOrderValue());
      addBaseAzimutTrading.setAccountId(baseAzimutTrading.getAccountId());
      addBaseAzimutTrading.setBankId(baseAzimutTrading.getBankId());
      addBaseAzimutTrading.setCurrencyId(baseAzimutTrading.getCurrencyId());
      addBaseAzimutTrading.setUserId(tokenizedBusinessUser.getUserId());
      addBaseAzimutTrading.setModuleTypeId(ModuleType.CASH.getTypeId());
      addBaseAzimutTrading.setReferenceNo(baseAzimutTrading.getReferenceNo());
      if (baseAzimutTrading.getInjectionDocument() != null) {
        MyLogger.info("Document available");
        try {
          addBaseAzimutTrading.setFileBytes(baseAzimutTrading.getInjectionDocument().getBytes().toString());
          addBaseAzimutTrading.setInjectionDocument(baseAzimutTrading.getInjectionDocument());
        } catch (IOException ioException) {
          MyLogger.info("Could not extract the file bytes");
          MyLogger.logStackTrace(ioException);
        }
      } else {
        MyLogger.info("Document empty");
      }
    }

    return addBaseAzimutTrading;
  }

  void populateThreshold(BaseAzimutTrading baseAzimutTrading) {
    baseAzimutTrading.setThreshold(this.configProperties.getBlockageNumberOfTrialsInt());
  }

  public String getConcatenatedValue(String countryPhoneCode, String phoneNumber) {
    String userPhone = StringUtility.isStringPopulated(countryPhoneCode) && StringUtility.isStringPopulated(phoneNumber)
        ? countryPhoneCode + phoneNumber
        : null;
    if (StringUtility.isStringPopulated(userPhone)) {
      String withPlus = "+" + userPhone;
      return withPlus;
    }
    return userPhone;
  }

  public BaseAzimutTrading cancelOrderRest(BusinessUser tokenizedBusinessUser, CancelOrderDto baseAzimutTrading)
      throws Exception {
    BaseAzimutTrading responseBaseAzimutTrading = new BaseAzimutTrading();
    try {
      // Note: responseBaseAzimutTrading is not used in the code, it is just returned
      // empty
      var azimutTrading = this.prepareOrderPlacingRequest(tokenizedBusinessUser, baseAzimutTrading);
      if (baseAzimutTrading.getPartnerId() != null) {
        var partner = partnerRepository.getById(baseAzimutTrading.getPartnerId());
        azimutTrading.setPartnerUserName(partner.getUserName());
      }
      var request = this.cancelOrderApi.createCancelOrderRequest(azimutTrading);
      this.cancelOrderApi.getData(request);
    } catch (Exception exception) {
      this.exceptionHandler.handleException(exception);
    }
    return responseBaseAzimutTrading;
  }

}
