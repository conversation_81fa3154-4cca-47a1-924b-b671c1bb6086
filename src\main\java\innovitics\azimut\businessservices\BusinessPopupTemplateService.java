package innovitics.azimut.businessservices;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import innovitics.azimut.businessutilities.BusinessSearchCriteria;
import innovitics.azimut.controllers.admin.DTOs.PopupDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.models.PopupTemplate;
import innovitics.azimut.models.user.User;
import innovitics.azimut.services.PopupService;
import innovitics.azimut.services.PopupTemplateService;
import innovitics.azimut.utilities.datautilities.PaginatedEntity;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class BusinessPopupTemplateService extends AbstractBusinessService<PopupTemplate> {

  private @Autowired PopupTemplateService popupTemplateService;
  private @Autowired PopupService popupService;

  public PaginatedEntity<PopupTemplate> listPopupTemplates(
      BusinessSearchCriteria businessSearchCriteria) {
    var templates = this.popupTemplateService
        .getAllPopups(generateDatabaseConditions(businessSearchCriteria));
    PaginatedEntity<PopupTemplate> paginatedEntity = new PaginatedEntity<PopupTemplate>();

    paginatedEntity.setCurrentPage(businessSearchCriteria.getPageNumber());
    paginatedEntity.setPageSize(businessSearchCriteria.getPageSize());
    paginatedEntity.setNumberOfPages(templates.getTotalPages());
    paginatedEntity.setNumberOfItems(templates.getTotalElements());
    paginatedEntity.setHasNext(!templates.isLast());
    paginatedEntity.setHasPrevious(!templates.isFirst());
    List<PopupTemplate> list = new ArrayList<>();
    if (!templates.isEmpty()) {
      for (var template : templates.getContent()) {
        list.add(template);
      }
    } else {
      list.add(null);
    }
    paginatedEntity.setDataList(list);
    return paginatedEntity;
  }

  public void sendTemplate(Long templateId, String target)
      throws IntegrationException, BusinessException, IOException, Exception {
    var template = popupTemplateService.getTemplate(templateId);
    if ((template.getSentGroups().contains(target) || template.getSentGroups().contains("allUsers"))
        && StringUtility.stringsDontMatch(target, "testUsers"))
      return;
    this.sendTemplateToFirebase(template, target);

    if (!template.getSentGroups().contains(target))
      template.getSentGroups().add(target);
    template.setSent(true);
    popupTemplateService.updateTemplate(template);
  }

  public void sendTemplateToList(Long templateId, List<Long> target)
      throws IntegrationException, BusinessException, IOException, Exception {
    var template = popupTemplateService.getTemplate(templateId);
    this.sendTemplateToFirebaseList(template, target);

    template.setSentCount(template.getSentCount() + target.size());
    template.setSent(true);
    popupTemplateService.updateTemplate(template);
  }

  public void sendTemplateToUserIdList(Long templateId, List<String> target)
      throws IntegrationException, BusinessException, IOException, Exception {
    var template = popupTemplateService.getTemplate(templateId);
    this.sendTemplateToFirebaseUserIdsList(template, target);

    template.setSentCount(template.getSentCount() + target.size());
    template.setSent(true);
    popupTemplateService.updateTemplate(template);
  }

  public PopupTemplate addTemplate(PopupDto businessReview)
      throws BusinessException {
    this.popupTemplateService.addTemplate(businessReview);
    return new PopupTemplate();
  }

  public PopupTemplate editTemplate(PopupDto businessReview)
      throws BusinessException {
    this.popupTemplateService.editTemplate(businessReview);
    return new PopupTemplate();
  }

  public PopupTemplate deleteTemplate(PopupDto businessReview) throws BusinessException {
    this.popupTemplateService.deleteTemplate(businessReview);
    return new PopupTemplate();
  }

  public void sendTemplateToFirebase(PopupTemplate template, String target) throws BusinessException {
    MyLogger.info("PopupExecuterService: Executing task on thread: " + Thread.currentThread().getName());
    var users = StringUtility.stringsMatch(target, "testUsers") ? userService.findTestUsers()
        : (StringUtility.stringsMatch(target, "allUsers") ? userService.findAll()
            : userService.findByUserStep(target));
    for (User user : users) {
      if (user.getDeletedAt() == null) {
        this.popupService.addPopup(user.getId(), template);
      }
    }
    MyLogger.info("PopupExecuterService: Task execution complete");
  }

  public void sendTemplateToFirebaseList(PopupTemplate template, List<Long> target) throws BusinessException {
    MyLogger.info("PopupExecuterService: Executing task on thread: " + Thread.currentThread().getName());
    var users = userService.findByIds(target);
    for (User user : users) {
      if (user.getDeletedAt() == null) {
        this.popupService.addPopup(user.getId(), template);
      }
    }
    MyLogger.info("PopupExecuterService: Task execution complete");
  }

  public void sendTemplateToFirebaseUserIdsList(PopupTemplate template, List<String> target)
      throws BusinessException {
    MyLogger.info("PopupExecuterService: Executing task on thread: " + Thread.currentThread().getName());
    var users = userService.findByUserIds(target);
    for (User user : users) {
      if (user.getDeletedAt() == null) {
        this.popupService.addPopup(user.getId(), template);
      }
    }
    MyLogger.info("PopupExecuterService: Task execution complete");
  }

}
