package innovitics.azimut.filters;

//@Component
//@Order(value = Integer.MAX_VALUE)
public class SimpleCORSFilter
// implements Filter
{

  /*
   * @Override
   * public void doFilter(ServletRequest servletRequest, ServletResponse
   * servletResponse, FilterChain filterChain) throws IOException,
   * ServletException
   * {
   * 
   * MyLogger.info("Inside the CORs Filter::::");
   * 
   * HttpServletRequest request = (HttpServletRequest) servletRequest;
   * HttpServletResponse response = (HttpServletResponse) servletResponse;
   * response.setHeader("Access-Control-Allow-Origin",request.getHeader("Origin"))
   * ;
   * response.setHeader("Access-Control-Allow-Methods",
   * "GET,POST,DELETE,PUT,OPTIONS");
   * response.setHeader("Access-Control-Allow-Headers", "*");
   * response.setHeader("Access-Control-Allow-Headers", "lang");
   * response.setHeader("Access-Control-Allow-Credentials", "true");
   * response.setHeader("Access-Control-Allow-Headers",
   * "Content-Type, Accept, X-Requested-With, remember-me");
   * response.setHeader("Access-Control-Max-Age","3600");
   * filterChain.doFilter(servletRequest, servletResponse);
   * 
   * MyLogger.info("After the CORs Filter::::");
   * 
   * }
   * 
   */

}
