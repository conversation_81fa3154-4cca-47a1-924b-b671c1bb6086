package innovitics.azimut.rest.apis.teacomputers;

import java.util.List;

import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import innovitics.azimut.businessmodels.user.AzimutAccount;
import innovitics.azimut.rest.entities.teacomputers.ClientAccountResponse;
import innovitics.azimut.rest.entities.teacomputers.GetClientAccountsRequest;
import innovitics.azimut.rest.entities.teacomputers.TeaComputerResponse;
import innovitics.azimut.utilities.crosslayerenums.UserIdType;
import innovitics.azimut.utilities.datautilities.StringUtility;

@Component
public class TeaComputersCheckAccountApi
    extends RestTeaComputersApi<GetClientAccountsRequest, ClientAccountResponse[]> {
  public static final String PATH = "/CheckAccount";
  public static final String MOBILE_NUMBER_NOT_EXISTING_CODE = "79";

  @Override
  protected String generateSignature(GetClientAccountsRequest request) {
    if (request != null && StringUtility.isStringPopulated(request.getIdNumber()) && request.getIdTypeId() != null
        && StringUtility.isStringPopulated(request.getIdTypeId().toString())) {
      return this.teaComputersSignatureGenerator.generateSignature(
          request.getIdTypeId() != null ? request.getIdTypeId().toString() : null, request.getIdNumber(),
          request.getMobile());
    } else if (request != null) {
      return this.teaComputersSignatureGenerator.generateSignature(request.getMobile());
    }
    return null;
  }

  @Override
  protected String generateResponseSignature(TeaComputerResponse response) {
    ClientAccountResponse clientAccountResponse = (ClientAccountResponse) response;
    return this.teaComputersSignatureGenerator.generateSignature("", clientAccountResponse.getClientName(),
        clientAccountResponse.getMobile());
  }

  @Override
  protected String generateURL(String params) {
    return super.generateBaseURL() + PATH;
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.GET;
  }

  public GetClientAccountsRequest createRequest(AzimutAccount azimutAccount) {
    var request = new GetClientAccountsRequest();
    request.setMobile(azimutAccount.getPhoneNumber());
    request.setIdNumber(azimutAccount.getUserId());
    request.setIdTypeId(azimutAccount.getIdType());
    request.setSignature(this.generateSignature(request));

    return request;
  }

  public List<AzimutAccount> createAzimutAccountList(ClientAccountResponse[] clientAccountResponses) {
    List<AzimutAccount> azimutAccounts = new java.util.ArrayList<>();
    for (ClientAccountResponse clientAccountResponse : clientAccountResponses) {
      azimutAccounts.add(convertResponseToAzimutAccount(clientAccountResponse));
    }
    return azimutAccounts;
  }

  private AzimutAccount convertResponseToAzimutAccount(ClientAccountResponse clientAccountResponse) {

    AzimutAccount azimutAccount = new AzimutAccount();
    var responseTypeId = Long.parseLong(clientAccountResponse.getIdTypeId());

    azimutAccount.setFullName(clientAccountResponse.getClientName());
    azimutAccount.setPhoneNumber(clientAccountResponse.getMobile());
    azimutAccount.setAzId(clientAccountResponse.getIdNumber());
    azimutAccount.setAzIdType(responseTypeId);

    azimutAccount.setUserIdType(UserIdType.getById(responseTypeId).getType());
    azimutAccount.setUserIdTypeAr(UserIdType.getById(responseTypeId).getTypeAr());

    return azimutAccount;
  }
}
