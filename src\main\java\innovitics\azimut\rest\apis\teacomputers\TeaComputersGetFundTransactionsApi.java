package innovitics.azimut.rest.apis.teacomputers;

import java.util.ArrayList;
import java.util.List;

import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import innovitics.azimut.businessmodels.funds.BusinessFundTransaction;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.controllers.users.DTOs.GetBalanceAndFundOwnershipDto;
import innovitics.azimut.rest.entities.teacomputers.FundTransactionResponse;
import innovitics.azimut.rest.entities.teacomputers.GetFundTransactionsRequest;
import innovitics.azimut.rest.entities.teacomputers.TeaComputerResponse;
import innovitics.azimut.utilities.crosslayerenums.OrderStatus;

@Component
public class TeaComputersGetFundTransactionsApi
    extends RestTeaComputersApi<GetFundTransactionsRequest, FundTransactionResponse[]> {
  @Override
  protected String generateURL(String params) {
    return super.generateBaseURL() + "/" + params;
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.GET;
  }

  @Override
  protected String generateSignature(GetFundTransactionsRequest getFundTransactionsRequest) {
    return this.teaComputersSignatureGenerator.generateSignature(true,
        getFundTransactionsRequest.getUserName() != null ? getFundTransactionsRequest.getUserName()
            : this.configProperties.getTeaComputersKey(),
        "",
        getFundTransactionsRequest.getIdTypeId().toString(), getFundTransactionsRequest.getIdNumber());
  }

  @Override
  protected String generateResponseSignature(GetFundTransactionsRequest request,
      TeaComputerResponse teaComputerResponse) {
    FundTransactionResponse fundTransactionResponse = (FundTransactionResponse) teaComputerResponse;
    return this.teaComputersSignatureGenerator.generateSignature(true,
        request.getUserName() != null ? request.getUserName() : this.configProperties.getTeaComputersKey(),
        fundTransactionResponse.getTransactionID() != null ? fundTransactionResponse.getTransactionID().toString()
            : null,
        fundTransactionResponse.getFundId() != null ? fundTransactionResponse.getFundId().toString() : null);
  }

  @Override
  protected String generateResponseSignature(TeaComputerResponse teaComputerResponse) {
    FundTransactionResponse fundTransactionResponse = (FundTransactionResponse) teaComputerResponse;
    // not used
    return this.teaComputersSignatureGenerator.generateSignature(
        fundTransactionResponse.getTransactionID() != null ? fundTransactionResponse.getTransactionID().toString()
            : null,
        fundTransactionResponse.getFundId() != null ? fundTransactionResponse.getFundId().toString() : null);
  }

  public GetFundTransactionsRequest prepareFundTransactionRequest(BusinessUser tokenizedBusinessUser,
      GetBalanceAndFundOwnershipDto businessAzimutClient, String partnerUsername) {
    GetFundTransactionsRequest request = new GetFundTransactionsRequest();
    request.setIdTypeId(tokenizedBusinessUser.getAzimutIdTypeId());
    request.setIdNumber(tokenizedBusinessUser.getUserId());
    request.setFundID(businessAzimutClient.getTeacomputerId());
    if (partnerUsername != null)
      request.setUserName(partnerUsername);
    request.setSignature(this.generateSignature(request));

    return request;
  }

  public List<BusinessFundTransaction> createListBusinessTransactionsFromResponses(FundTransactionResponse[] responses,
      OrderStatus orderStatus) {

    List<BusinessFundTransaction> businessFundTransactions = new ArrayList<BusinessFundTransaction>();

    for (FundTransactionResponse response : responses) {
      businessFundTransactions.add(createBusinessFundTransaction(response, orderStatus));
    }
    return businessFundTransactions;
  }

  private static BusinessFundTransaction createBusinessFundTransaction(FundTransactionResponse response,
      OrderStatus orderStatus) {
    BusinessFundTransaction businessFundTransaction = new BusinessFundTransaction();
    businessFundTransaction.setTransactionId(response.getTransactionID());
    businessFundTransaction.setFundId(response.getFundId());
    businessFundTransaction.setOrderDate(response.getOrderDate());
    businessFundTransaction.setOrderValue(response.getOrderValue());
    businessFundTransaction.setExecPrice(response.getExecPrice());
    businessFundTransaction.setQuantity(response.getQuantity());
    businessFundTransaction.setOrderTypeId(response.getOrderTypeId());
    businessFundTransaction.setOrderStatusId(orderStatus.getTypeId());
    businessFundTransaction.setOrderStatus(orderStatus.getType());
    return businessFundTransaction;
  }
}
