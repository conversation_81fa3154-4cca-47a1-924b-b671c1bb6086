package innovitics.azimut.businessmodels.kyc;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import innovitics.azimut.businessmodels.BaseBusinessEntity;
import innovitics.azimut.utilities.CustomJsonRootName;
import lombok.Data;

@Data
@CustomJsonRootName(plural = "answers", singular = "answer")
public class BusinessAnswer extends BaseBusinessEntity {

  private List<BusinessAnswer> relatedAnswers;
  private String answerType;
  private String answerOrder;
  private String answerOption;
  private String answerPlaceHolder;
  private Boolean isRelatedAnswerMandatory;
  private String relatedQuestionText;
  private Boolean isAnswerMandatory;
  private List<BusinessSubmittedAnswer> businessSubmittedAnswers;
  private List<BusinessSubmittedAnswer> childBusinessSubmittedAnswers;
  private String answerOptionAr;
  private String relatedQuestionTextAr;
  private String answerPlaceHolderAr;
  private String pdFieldName;
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  protected Date deletedAt;
}
