package innovitics.azimut.rest.apis.fra;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.rest.RestBaseApi;
import innovitics.azimut.rest.entities.fra.FraNtraInput;
import innovitics.azimut.rest.entities.fra.FraNtraOutput;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class FraNtraApi extends RestBaseApi<FraNtraInput, FraNtraOutput> {

  @Override
  protected String generateURL(String params) {
    return this.configProperties.getFraUrl() + "/PhoneNumberOwner/check";
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.POST;
  }

  @Override
  public HttpHeaders generateHeaders() {
    HttpHeaders headers = super.generateHeaders();
    headers.set("ApiKey", this.configProperties.getFraApiKey());
    return headers;
  }

  @Override
  protected HttpEntity<?> generateRequestFromInput(FraNtraInput input) {
    input.setCompanyName(this.configProperties.getFraCompanyName());
    return this.stringify(input);
  };

  @Override
  protected void validateResponse(ResponseEntity<FraNtraOutput> responseEntity) throws IntegrationException {
    super.validateResponse(responseEntity);
    if (!responseEntity.getStatusCode().is2xxSuccessful()) {
      MyLogger.info("FraNtraApi: Error in response from FRA API" + responseEntity.getStatusCode() + " "
          + responseEntity.getBody());
      throw new IntegrationException(ErrorCode.FAILED_TO_INTEGRATE);
    }
  }

}
