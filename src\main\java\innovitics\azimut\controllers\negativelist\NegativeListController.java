package innovitics.azimut.controllers.negativelist;

import java.io.IOException;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import innovitics.azimut.businessmodels.negativelist.BusinessNegativeList;
import innovitics.azimut.businessmodels.negativelist.BusinessNegativeListScreeningResult;
import innovitics.azimut.businessservices.BusinessNegativeListService;
import innovitics.azimut.controllers.BaseController;
import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.controllers.GenericResponseHandler;
import innovitics.azimut.controllers.negativelist.DTOs.ScreenUsersDto;
import innovitics.azimut.controllers.negativelist.DTOs.SearchNegativeListDto;
import innovitics.azimut.controllers.negativelist.DTOs.UploadSanctionsListDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.utilities.datautilities.StringUtility;

@RestController
@RequestMapping(value = "/api/negativelist", produces = {
    MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE })
public class NegativeListController extends BaseController {

  @Autowired
  private BusinessNegativeListService businessNegativeListService;

  @Autowired
  private GenericResponseHandler<BusinessNegativeList> negativeListResponseHandler;

  @Autowired
  private GenericResponseHandler<BusinessNegativeListScreeningResult> screeningResultResponseHandler;

  @PostMapping(value = "/uploadSanctionsList", consumes = {
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessNegativeList>> uploadSanctionsList(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid UploadSanctionsListDto uploadDto)
      throws BusinessException, IOException, IntegrationException {

    return negativeListResponseHandler.generateBaseGenericResponse(
        BusinessNegativeList.class,
        this.businessNegativeListService.uploadSanctionsList(uploadDto),
        null, null);
  }

  @PostMapping(value = "/screenUsers", consumes = {
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessNegativeListScreeningResult>> screenUsers(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid ScreenUsersDto screenDto)
      throws BusinessException, IOException, IntegrationException {

    return screeningResultResponseHandler.generateBaseGenericResponse(
        BusinessNegativeListScreeningResult.class,
        null,
        this.businessNegativeListService.screenUsers(screenDto),
        null);
  }

  @PostMapping(value = "/search", consumes = {
      MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessNegativeList>> searchNegativeList(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody SearchNegativeListDto searchDto)
      throws BusinessException, IOException, IntegrationException {

    return negativeListResponseHandler.generateBaseGenericResponse(
        BusinessNegativeList.class,
        null,
        this.businessNegativeListService.searchNegativeList(searchDto),
        null);
  }

  @PostMapping(value = "/flagExistingUsers")
  protected ResponseEntity<BaseGenericResponse<BusinessNegativeListScreeningResult>> flagExistingUsers(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language)
      throws BusinessException, IOException, IntegrationException {

    return screeningResultResponseHandler.generateBaseGenericResponse(
        BusinessNegativeListScreeningResult.class,
        null,
        this.businessNegativeListService.flagExistingUsers(),
        null);
  }

  @GetMapping(value = "/statistics")
  protected ResponseEntity<BaseGenericResponse<Object>> getStatistics(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language)
      throws BusinessException, IOException, IntegrationException {

    // This would be implemented to return statistics about the negative lists
    // For now, returning a placeholder response
    return new GenericResponseHandler<Object>().generateBaseGenericResponse(
        Object.class,
        "Statistics endpoint - to be implemented",
        null, null);
  }
}
