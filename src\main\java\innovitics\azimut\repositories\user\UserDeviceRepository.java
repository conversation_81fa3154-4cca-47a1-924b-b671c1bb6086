package innovitics.azimut.repositories.user;

import javax.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import innovitics.azimut.models.user.UserDevice;
@Repository
public interface UserDeviceRepository extends JpaRepository<UserDevice, Long>, UserDeviceRepositoryCustom,JpaSpecificationExecutor<UserDevice>{

	 @Query(value="update user_devices set updated_at=sysdate() where app_user_id=? and device_id=?",nativeQuery = true)
     @Modifying
     @Transactional	 
     public void updateDeviceLastUpdateDateUsingUserIdAndDeviceId(Long userId,String deviceId);
	
}
