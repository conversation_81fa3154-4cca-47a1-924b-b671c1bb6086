package innovitics.azimut.utilities.exceptionhandling;

public enum ErrorCode {
  NO_MATCHED_CLIENT_NUMBER_EXIST(79, "Invalid Username or password.", ""),
  INVALID_CLIENT(185, "", ""),
  INVALID_CLIENT_STATUS(79, "", ""),
  INVALID_NOTIFICATION_TYPE(410, "Invalid notification type", ""),
  INVALID_HEADER_SIGNATURE(993, "Invalid signature, corrupted request.", ""),
  CONNECTION_TIMEOUT(994, "Connection Timeout (FRA), try again later.", ""),
  OPERATION_FAILURE(995, "Operation failed.", ""),
  PAYMENT_FAILURE(996, "Operation Could not be performed, please check with the vendor.", ""),
  INVALID_SIGNATURE(997, "Invalid signature, corrupted response.", ""),
  INTEGRATION_TEST_FAILED(998, "Integration Test Failed.", ""),
  FAILED_TO_INTEGRATE(999, "Operation Could not be performed.", ""),
  OPERATION_NOT_PERFORMED(1000, "Operation Could not be performed.", ""),
  NO_DATA_FOUND(1001, "No data found.", ""),
  NO_USER_ACCOUNT_FOUND(1002, "User Account not found.", ""),
  MULTIPLE_LOGINS(1003, "You're logged in using another device, please try again later.",
      "أنت مسجل على جهاز آخر برجاء المحاولة في وقت لاحق"),
  USER_BLOCKED(1004, "User Blocked, please contact +*********** for help.",
      "هذا الحساب محظور، فضلا اتصل ب +***********"),
  FAILED_TO_SEND_EMAIL(1005, "Could not send the email.", "لم نتمكن من إرسال الإيميل"),
  USER_DELETED(1006, "This user has been deleted", "هذا العميل تم وقفه"),
  UPDATE_APP_REQUIRED(1007, "Outdated app version. Please update the app to continue.",
      "نسخة التطبيق قديمة. يرجى تحديث التطبيق للاستمرار."),
  INVALID_EXTENSION(2101, "Could not upload the file.", ""),
  COPY_FAILURE(2102, "Could not upload the file.", ""),
  UPLOAD_FAILURE(2103, "Could not upload the file.", ""),
  FILE_TOO_BIG(2104, "File size too big.", "حجم الملف كبير"),
  REQUESTS_FOUND(2105, "Another request was found for this user.", ""),
  MISSING_FILE(2106, "File missing.", ""),
  UNAUTHORIZED_USER(2107, "Unauthorized user.", "غير مصرح لهذا المستخدم"),
  WEAK_PASSWORD(2108,
      "The password is weak, it must be at least 8 characters long, consist of at least 1 capital letter 1 small letter and 1 number.",
      ""),
  PASSWORDS_NOT_MATHCING(2109, "The two passwords are not matching.", ""),
  TOO_MANY_USERS(2110, "More than one user was found having this phone.", ""),
  NO_USERS_FOUND(2111, "No users were found.", ""),
  USER_NOT_FOUND(2112, "Invalid Username or password.", ""),
  USER_NOT_SAVED(2113, "User was not saved.", ""),
  USER_NOT_UPDATED(2114, "User was not updated.", ""),
  USER_EXISTS(2115, "User already exists.", ""),
  INCORRECT_PASSWORD(2116, "Incorrect password.", ""),
  BAD_CREDENTIALS(2117, "Bad credentials.", ""),
  FAILED_TO_VALIDATE_TOKEN(2118, "Session expired.", "لا يمكن الدخول الأن"),
  INVALID_FIELD_VALUE(2119, "Invalid value", ""),
  IMAGES_NOT_SIMIILAR(2120, "The front and back images are of different Ids, please retake them.", ""),
  IMAGES_NOT_ClEAR(2121, "The images are not clear.", ""),
  USE_MOBILE(2122, "Please use the mobile app to take the pictures for a better experience.", ""),
  TOO_MANY_USER_LOCATIONS(2123, "More than one user was found having this phone.", ""),
  USER_LOCATION_NOT_SAVED(2124, "Could not save the user location.", ""),
  INVALID_USER_STEP(2125, "Invalid userStep", ""),
  USER_ID_NOT_MATCHING(2126, "The User Id does not match the extracted client data.", ""),
  USER_INTERACTION_NOT_ADDED(2127, "We could not submit your request, please try again later", ""),
  OTP_NOT_SAVED(2128, "Could not issue your OTP. Please try again later.", ""),
  OTP_NOT_VERIFIED(2129, "OTP already verified. Cannot be used again", ""),
  OTP_NONE_EXISTING(2130, "Could not issue your OTP. Please try again later.", ""),
  IMAGES_GREYSCALE(2131, "The front or back images is a copy, please retake them using original Id.", ""),
  ID_EXPIRED(2132, "The national Id is expired, please use a valid Id.", ""),
  ID_FACE_FRAUD(2133, "The national Id image is not valid, please retake it.", ""),
  FACE_NOT_MATCHING(2134, "The photo does not match the id image, please retake it.", ""),
  SECURITY_QUESTIONS_EXISTS(2140, "Security questions already submitted.", ""),
  CLIENT_SOLVING(31107, "The client is currently correcting their answers.", ""),
  USER_UNDER_REVIEW(31109, "Your answers are currently being reviewed.", ""),
  REJECTED_ANSWER_NOT_CHANGED(31110, "A rejected answer was not changed.", ""),
  PAGES_NOT_FOUND(31111, "No pages are found for this user type.", ""),
  PAGE_NOT_FOUND(31112, "Page not found.", ""),
  INVALID_ANSWER_TYPE(31113, "Invalid answer type.", ""),
  ANSWER_SUBMISSION_FAILED(31114, "Could not submit the answers.", ""),
  CONTRACT_DOWNLOAD_FAILED(31115, "Could not download the contract.", ""),
  PDF_GENERATION_FAILED(31117, "Could not download the contract.", ""),
  KYC_INCOMPLETE(31118, "KYC Form incomplete.", ""),
  KYC_SUBMITTED(31119, "KYC Form submitted.", ""),
  INVALID_OTP(31120, "Invalid OTP.", "رمز التحقق غير صحيح"),
  CONTRACT_NOT_SIGNED(31121, "Contract not signed, please wait for the contract to be signed", ""),
  REVIEW_SUBMISSION_FAILED(31122, "A review is missing.", ""),
  KYC_STATUS_COMPLETE(31123, "The review for this user has been completed.", ""),
  REASON_MISSING(31124, "Please select a rejection reason.", ""),
  REASON_IN_USE(31125, "The reason you are trying to delete is currently in use.", ""),
  LOCATION_NOT_FOUND(31126, "You need to enable your GPS before signing the contract.", ""),
  INVALID_NTRA(31127, "The phone number is not registered with your national id.", ""),
  PHONE_CHANGE_LIMIT(31128, "You have reached change phone number limit", ""),
  INVALID_DIVIDEND_DATE(35001, "Invalid Dividend date.", ""),
  PAYMENT_TRANSACTION_NOT_FOUND(4000, "Transaction not found", ""),
  PAYMENT_TRANSACTION_CORRUPTED(4001, "Transaction corrupted", ""),
  PAYMENT_TRANSACTION_NOT_SAVED(4002, "Transaction not saved", "");

  private final int code;
  private final String message;
  private final String messageAr;

  private ErrorCode(int code, String message, String messageAr) {
    this.code = code;
    this.message = message;
    this.messageAr = messageAr;
  }

  public String getMessage() {
    return message;
  }

  public int getCode() {
    return code;
  }

  public String getMessageAr() {
    return messageAr.isBlank() ? message : messageAr;
  }

  @Override
  public String toString() {
    return code + ": " + message;
  }

}
