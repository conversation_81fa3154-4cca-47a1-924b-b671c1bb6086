package innovitics.azimut.rest.entities.fra;

import lombok.Data;

@Data
public class FraNtraOutput {

  private Boolean isMatched;
  private String errorKey;
  private String errorCode;
  private String errorMessage;

  private FraNtraOutputData data;
  private String error_code;
  private String error_message;
}

/*
 * Error Codes List
 * Code Message
 * 400 Bad Request
 * 401 Unauthorized – invalid API Key
 * 6000 Phone OwnerShip is not valid
 * 6001 Phone OwnerShip service error
 * 6002 NationalId is not valid egypt format
 * 6003 invalid uuid format
 * 6004 Contract already saved before with the same status
 */
