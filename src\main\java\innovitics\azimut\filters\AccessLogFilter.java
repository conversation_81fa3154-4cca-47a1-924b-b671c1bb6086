package innovitics.azimut.filters;

import java.io.IOException;
import java.util.Arrays;

import javax.mail.BodyPart;
import javax.mail.internet.MimeMultipart;
import javax.mail.util.ByteArrayDataSource;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.tomcat.util.json.JSONParser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;
import org.springframework.web.util.ContentCachingResponseWrapper;

import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.logging.FileUtility;
import innovitics.azimut.utilities.logging.MyLogger;

@Component
public class AccessLogFilter implements Filter {

  protected static final Logger logger = LoggerFactory.getLogger(AccessLogFilter.class);

  @Autowired
  FileUtility fileUtility;

  @Override
  public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
      throws IOException, ServletException {

    // ContentCachingRequestWrapper requestWrapper = new
    // ContentCachingRequestWrapper((HttpServletRequest)request);
    ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper((HttpServletResponse) response);

    String transaction = // CurrentRequestHolder.get()!=null&&StringUtility.isStringPopulated(CurrentRequestHolder.get().getSystemTrx())?CurrentRequestHolder.get().getSystemTrx():""
        Thread.currentThread().getName();

    RequestWrapper wrapper = new RequestWrapper((HttpServletRequest) request);
    byte[] body = StreamUtils.copyToByteArray(wrapper.getInputStream());
    String requestBody = new String(body, 0, body.length, wrapper.getCharacterEncoding());
    MyLogger.info("IP Address::" + wrapper.getHeader(StringUtility.IP));
    this.fileUtility.write("IP Address::" + wrapper.getHeader(StringUtility.IP));

    try {
      if (StringUtility.stringsMatch(request.getContentType(), MediaType.APPLICATION_JSON_VALUE)) {
        var jsonRequestBody = new JSONParser(requestBody).parseObject();
        if (jsonRequestBody.containsKey("password")) {
          jsonRequestBody.replace("password", "********");
        }
        MyLogger.info("REQUEST::" + transaction + "::" + wrapper.getRequestURI() + "::" + jsonRequestBody.toString());
        this.fileUtility
            .write("REQUEST::" + transaction + "::" + wrapper.getRequestURI() + "::" + jsonRequestBody.toString());
      } else if (request.getContentType().startsWith(MediaType.MULTIPART_FORM_DATA_VALUE)) {
        ByteArrayDataSource datasource = new ByteArrayDataSource(body, "multipart/form-data");
        MimeMultipart multipart = new MimeMultipart(datasource);
        int count = multipart.getCount();
        for (int i = 0; i < count; i++) {
          BodyPart bodyPart = multipart.getBodyPart(i);
          MyLogger.info("REQUEST:: multipart header: " + Arrays.toString(bodyPart.getHeader("Content-Disposition")));
          this.fileUtility
              .write("REQUEST:: multipart header: " + Arrays.toString(bodyPart.getHeader("Content-Disposition")));
          if (bodyPart.getContentType() != null
              && (bodyPart.getContentType().contains("text") || bodyPart.getContentType().contains("json"))) {

            Object content = bodyPart.getContent();
            MyLogger.info("REQUEST:: multipart value: " + content);
            this.fileUtility.write("REQUEST:: multipart value: " + content);
          }
        }
      } else {
        MyLogger.info("REQUEST::" + transaction + "::" + wrapper.getRequestURI() + "::" + requestBody);
        this.fileUtility.write("REQUEST::" + transaction + "::" + wrapper.getRequestURI() + "::" + requestBody);
      }
    } catch (Exception e) {
      MyLogger.info("REQUEST::" + transaction + "::" + wrapper.getRequestURI() + "::" + requestBody);
      this.fileUtility.write("REQUEST::" + transaction + "::" + wrapper.getRequestURI() + "::" + requestBody);
    }

    chain.doFilter(wrapper, responseWrapper);

    // Get Cache

    /*
     * String requestBody =
     * StringUtility.getStringValue(requestWrapper.getContentAsByteArray(),
     * requestWrapper.getCharacterEncoding());
     * MyLogger.info("REQUEST:::"+requestBody);
     */

    String responseBody = isContentTypeLoggable(responseWrapper.getContentType())
        ? StringUtility.getStringValue(responseWrapper.getContentAsByteArray(), response.getCharacterEncoding())
        : responseWrapper.getContentType();
    final String adminHeader = ((HttpServletRequest) request).getHeader(StringUtility.ADMIN_HEADER);
    final boolean isAdmin = StringUtility.isStringPopulated(adminHeader) && StringUtility.TRUE.contains(adminHeader);
    if (isAdmin || wrapper.getRequestURI().contains("digital-registry")
        || wrapper.getRequestURI().contains("gateId/status"))
      responseBody = responseWrapper.getContentType();
    MyLogger.info("RESPONSE::" + transaction + ":status=" + responseWrapper.getStatus() + "::" + responseBody);
    responseWrapper.copyBodyToResponse();

    this.fileUtility.write("RESPONSE::" + transaction + ":status=" + responseWrapper.getStatus() + "::" + responseBody);

  }

  private Boolean isContentTypeLoggable(String contentType) {
    return contentType == null || contentType.toLowerCase().contains("json")
        || contentType.toLowerCase().contains("txt") ||
        contentType.toLowerCase().contains("xml");
  }

}
