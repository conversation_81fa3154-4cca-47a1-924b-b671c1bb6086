package innovitics.azimut.services.user;

import java.util.List;
import java.util.NoSuchElementException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import innovitics.azimut.models.Role;
import innovitics.azimut.repositories.user.RoleRepository;

@Service
public class RoleService {
  @Autowired
  RoleRepository roleRepository;

  public Role findById(Long id) throws NoSuchElementException {
    return roleRepository.findById(id).orElseThrow();
  }

  public List<Role> getAllRoles() {
    return this.roleRepository.findAll();
  }

  public Role saveRole(Role role) {
    return this.roleRepository.save(role);
  }

  public void deleteRole(Long id) {
    this.roleRepository.deleteById(id);
  }
}