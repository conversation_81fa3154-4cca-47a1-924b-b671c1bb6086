package innovitics.azimut.models;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Entity
@Table(name = "navs")
@Setter
@Getter
@ToString
public class Nav extends DbBaseEntity {

  private Long fundId;
  private Double nav;
  private Date deletedAt;
  private Long teacomputerId;
  private Date date;

  public Nav(Long id, Long fundId, Double nav, Date createdAt, Date updatedAt, Date deletedAt, Long teacomputerId,
      Date date) {
    super();
    this.setId(id);
    this.fundId = fundId;
    this.nav = nav;
    this.setCreatedAt(createdAt);
    this.setUpdatedAt(updatedAt);
    this.deletedAt = deletedAt;
    this.teacomputerId = teacomputerId;
    this.date = date;
  }

  public Nav() {

  }

}
