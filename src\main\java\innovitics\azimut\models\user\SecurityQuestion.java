package innovitics.azimut.models.user;

import javax.persistence.Entity;
import javax.persistence.Table;

import innovitics.azimut.models.DbBaseEntity;
import innovitics.azimut.utilities.CustomJsonRootName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "security_questions")
@CustomJsonRootName(plural = "questions", singular = "question")
@Setter
@Getter
@ToString
public class SecurityQuestion extends DbBaseEntity {

  private String questionText;
  private String questionTextAr;
}
