package innovitics.azimut.models.user;

import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import innovitics.azimut.models.DbBaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "user_security_questions")
@Setter
@Getter
@ToString
public class UserSecurityQuestion extends DbBaseEntity {

  @ManyToOne
  @JoinColumn(name = "security_question_id", nullable = false)
  private SecurityQuestion securityQuestion;

  @ManyToOne
  @JoinColumn(name = "user_id", nullable = false)
  private User user;

  private String answer;
}
