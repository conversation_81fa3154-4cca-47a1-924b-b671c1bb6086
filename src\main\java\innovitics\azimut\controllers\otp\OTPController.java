package innovitics.azimut.controllers.otp;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.businessmodels.user.BusinessUserOTP;
import innovitics.azimut.businessservices.BusinessOTPService;
import innovitics.azimut.businessservices.BusinessUserService;
import innovitics.azimut.controllers.BaseController;
import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.controllers.GenericResponseHandler;
import innovitics.azimut.controllers.otp.Dtos.SendOtpDto;
import innovitics.azimut.controllers.otp.Dtos.VerifyDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.security.RecaptchaUtility;
import innovitics.azimut.services.digitalregistry.DigitalRegistryService;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;

@RestController
@RequestMapping("api/otp")
public class OTPController extends BaseController {

  @Autowired
  BusinessOTPService businessOTPService;

  @Autowired
  DigitalRegistryService digitalRegistryService;

  @Autowired
  GenericResponseHandler<BusinessUserOTP> businessUserOTPResponseHandler;

  @Autowired
  GenericResponseHandler<Boolean> booleanHandler;

  @Autowired
  BusinessUserService businessUserService;

  @Autowired
  RecaptchaUtility recaptchaUtility;

  @PostMapping(value = "/sendOtp")
  public ResponseEntity<BaseGenericResponse<Boolean>> sendOtp(@Valid @RequestBody SendOtpDto otpDto,
      @RequestHeader(name = StringUtility.AUTHORIZATION_HEADER, required = false) String token)
      throws Exception, BusinessException {
    BusinessUser user = StringUtility.isStringPopulated(token) ? this.getCurrentRequestHolder(token) : null;
    String phone = user != null ? user.getUserPhone() : otpDto.getPhone();
    if (user == null) {
      boolean isEgypt = otpDto.getCountryCode() == null ? phone.startsWith("+20")
          : (phone.startsWith("+20") && StringUtility.stringsMatch(otpDto.getCountryCode(), "EG"));
      if (otpDto.getToken() == null)
        throw new IntegrationException(ErrorCode.UPDATE_APP_REQUIRED);
      var res = recaptchaUtility.createAssessment(otpDto.getToken(), otpDto.getPlatform(), phone,
          "TRIGGER_MFA", true, phone.startsWith("+20"));
      if (!res.getResponse())
        throw new IntegrationException(ErrorCode.OPERATION_NOT_PERFORMED);
      this.businessOTPService.sendOnboardingOTP(phone, res.getAssessmentId(), isEgypt);
    } else {
      var otp = this.businessOTPService.sendOtp(phone, null);
      if (user.getEmailAddress() != null)
        this.businessUserService.sendOtpEmail(user, otp);
    }
    return booleanHandler.generateBaseGenericResponse(Boolean.class, true, null, null);
  }

  @PostMapping(value = "/verifyOtp")
  public ResponseEntity<BaseGenericResponse<Boolean>> verifyOtp(@Valid @RequestBody VerifyDto otpDto,
      @RequestHeader(name = StringUtility.IP, required = false) String ip,
      @RequestHeader(name = StringUtility.AUTHORIZATION_HEADER, required = false) String token)
      throws Exception, BusinessException {
    BusinessUser user = StringUtility.isStringPopulated(token) ? this.getCurrentRequestHolder(token) : null;
    String phone = user != null ? user.getUserPhone() : otpDto.getPhone();
    if (user != null) {
      BusinessUserOTP businessUserOTP = this.businessOTPService.getPhoneOTP(phone);
      this.businessOTPService.verifyOtp(businessUserOTP, otpDto.getOtp(), user);
      // this.digitalRegistryService.recordVerifyPhone(ip, user.getId(),
      // otpDto.getOtp(), token);
      // ToDo: uncomment the above line when the otp is above 500K
    } else
      this.businessOTPService.verifyOnboardingOtp(phone, otpDto.getOtp());
    return booleanHandler.generateBaseGenericResponse(Boolean.class,
        true, null, null);
  }

  @PostMapping(value = "/verifyContractOtp")
  public ResponseEntity<BaseGenericResponse<BusinessUserOTP>> verifyContractOtp(
      @Valid @RequestBody VerifyDto businessUserOTP,
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.IP, required = false) String ip)
      throws Exception, BusinessException {
    var user = this.getCurrentRequestHolder(token);
    var userOtp = this.businessOTPService.verifyContractOtp(user, businessUserOTP);
    this.businessUserService.autoAccept(user);
    this.digitalRegistryService.recordSignContract(ip, user.getId(), businessUserOTP.getOtp(), token);
    return businessUserOTPResponseHandler.generateBaseGenericResponse(BusinessUserOTP.class,
        userOtp, null,
        null);
  }

}
