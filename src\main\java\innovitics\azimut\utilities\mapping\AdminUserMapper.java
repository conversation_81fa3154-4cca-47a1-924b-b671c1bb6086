package innovitics.azimut.utilities.mapping;

import java.util.Date;

import org.springframework.stereotype.Component;

import innovitics.azimut.businessmodels.admin.BusinessAdminUser;
import innovitics.azimut.models.admin.AdminUser;

@Component
public class AdminUserMapper extends Mapper<AdminUser, BusinessAdminUser> {

  @Override
  public AdminUser convertBusinessUnitToBasicUnit(BusinessAdminUser businessAdminUser, boolean save) {
    AdminUser adminUser = new AdminUser();
    if (save) {
      adminUser.setCreatedAt(new Date());
      adminUser.setUpdatedAt(new Date());
    } else {
      adminUser.setCreatedAt(businessAdminUser.getCreatedAt());
      adminUser.setUpdatedAt(new Date());
    }
    adminUser.setId(businessAdminUser.getId());
    adminUser.setEmail(businessAdminUser.getEmailAddress());
    adminUser.setPassword(this.aes.encrypt(businessAdminUser.getPassword()));
    adminUser.setName(businessAdminUser.getFullName());
    adminUser.setRoleId(businessAdminUser.getRoleId());
    return adminUser;
  }

  @Override
  public BusinessAdminUser convertBasicUnitToBusinessUnit(AdminUser adminUser) {

    BusinessAdminUser businessAdminUser = new BusinessAdminUser();

    businessAdminUser.setCreatedAt(adminUser.getCreatedAt());
    businessAdminUser.setUpdatedAt(adminUser.getUpdatedAt());
    businessAdminUser.setId(adminUser.getId());
    businessAdminUser.setEmailAddress(adminUser.getEmail());
    businessAdminUser.setFullName(adminUser.getName());
    businessAdminUser.setPassword(adminUser.getPassword());
    businessAdminUser.setRoleId(adminUser.getRoleId());
    return businessAdminUser;
  }

  @Override
  public BusinessAdminUser convertBasicUnitToBusinessUnit(AdminUser s, String language) {

    return null;
  }

}
