package innovitics.azimut.utilities.messaging.email;

import java.io.ByteArrayOutputStream;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.mail.MessagingException;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.util.ByteArrayDataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.MailException;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import innovitics.azimut.businessmodels.admin.BusinessAdminUser;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;
import innovitics.azimut.utilities.messaging.MessagingUtility;

@Component
public class EmailUtility extends MessagingUtility {

  @Autowired
  protected JavaMailSender emailSender;

  public void sendSimpleMessage(String to, String subject, String text) throws BusinessException {
    SimpleMailMessage message = new SimpleMailMessage();
    message.setFrom(this.configProperties.getMailFrom());
    message.setTo(to);
    message.setSubject(subject);
    message.setText(text);

    try {
      emailSender.send(message);
      MyLogger.info("Email sent to: " + to);
    } catch (MailException ex) {
      MyLogger.error("Email could not be sent::::::::::");
      MyLogger.logStackTrace(ex);
      throw new BusinessException(ErrorCode.FAILED_TO_SEND_EMAIL);
    }

  }

  public void sendMailWithAttachment(String to, String subject, String body,
      ByteArrayOutputStream byteArrayOutputStream, boolean sendCopyToAzimut) throws BusinessException {
    MimeMessage mimeMessage = emailSender.createMimeMessage();
    try {
      MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true);
      helper.setTo(new InternetAddress(to));
      if (sendCopyToAzimut)
        helper.setBcc(this.configProperties.getAzimutBccMail().split(","));

      helper.setFrom(this.configProperties.getMailFrom());
      helper.setText(body);

      /*
       * DataSource attachment = new
       * ByteArrayDataSource(byteArrayOutputStream.toByteArray(),"application/"+
       * StringUtility.PDF_EXTENSION);
       * MimeBodyPart attachmentPart = new MimeBodyPart();
       * attachmentPart.setDataHandler(new DataHandler(attachment));
       * helper.addAttachment(StringUtility.CONTRACT_DOCUMENT_NAME+"."+StringUtility.
       * PDF_EXTENSION, attachment);
       */
      this.attach(helper, byteArrayOutputStream);

    } catch (MessagingException e) {
      MyLogger.logStackTrace(e);
    }

    try {
      this.emailSender.send(mimeMessage);
    } catch (MailException ex) {
      MyLogger.logStackTrace(ex);
      throw new BusinessException(ErrorCode.FAILED_TO_SEND_EMAIL);
    }

  }

  @Override
  protected void send(BusinessUser to, String message, String title, String messageAr, String titleAr,
      String language,
      Object... options) throws BusinessException {
    if (options != null && options.length > 0 && options[0] != null) {
      try {
        sendMailWithAttachment(to.getEmailAddress(), title, message, (ByteArrayOutputStream) options[0],
            (Boolean) options[1]);
      } catch (MailException ex) {
        MyLogger.error("Email could not be sent::::::::::");
        MyLogger.logStackTrace(ex);
        throw new BusinessException(ErrorCode.FAILED_TO_SEND_EMAIL);
      }
    } else {
      sendSimpleMessage(to.getEmailAddress(), title, message);
    }
  }

  @Override
  protected void send(BusinessAdminUser to, String message, String title, String messageAr, String titleAr,
      String language,
      Object... options) throws BusinessException {
    if (options != null && options.length > 0 && options[0] != null) {
      try {
        sendMailWithAttachment(to.getEmailAddress(), title, message, (ByteArrayOutputStream) options[0],
            (Boolean) options[1]);
      } catch (MailException ex) {
        MyLogger.error("Email could not be sent::::::::::");
        MyLogger.logStackTrace(ex);
        throw new BusinessException(ErrorCode.FAILED_TO_SEND_EMAIL);
      }
    } else {
      sendSimpleMessage(to.getEmailAddress(), title, message);
    }
  }

  void attach(MimeMessageHelper helper, ByteArrayOutputStream byteArrayOutputStream) throws MessagingException {
    DataSource attachment = new ByteArrayDataSource(byteArrayOutputStream.toByteArray(),
        "application/" + StringUtility.PDF_EXTENSION);
    MimeBodyPart attachmentPart = new MimeBodyPart();
    attachmentPart.setDataHandler(new DataHandler(attachment));
    helper.addAttachment(StringUtility.CONTRACT_DOCUMENT_NAME + "." + StringUtility.PDF_EXTENSION, attachment);
  }
}
