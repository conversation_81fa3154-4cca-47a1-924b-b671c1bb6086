package innovitics.azimut.models.teacomputers;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import innovitics.azimut.models.DbBaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Entity
@Table(name = "client_bank_accounts")
@Setter
@Getter
@ToString
public class ClientBankAccount extends DbBaseEntity {
  private Long userId;
  private Long idTypeId;
  private String idNumber;

  private String englishBankName;
  private String arabicBankName;

  private Long bankId;

  private String englishBranchName;
  private String arabicBranchName;

  private Long branchId;

  private String englishCurrencyName;
  private String arabicCurrencyName;
  private String bankType;

  private Long currencyId;

  private String accountNo;
  private String swiftCode;
  private String iban;

  private Date createdAt;
  private Date updatedAt;
  private Date deletedAt;

  private Boolean kycOnly;

}
