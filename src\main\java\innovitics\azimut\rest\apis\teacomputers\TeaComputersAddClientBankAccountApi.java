package innovitics.azimut.rest.apis.teacomputers;

import innovitics.azimut.businessmodels.user.BusinessClientBankAccountDetails;
import innovitics.azimut.rest.entities.teacomputers.AddClientBankAccountRequest;
import innovitics.azimut.rest.entities.teacomputers.AddClientBankAccountResponse;
import innovitics.azimut.rest.entities.teacomputers.TeaComputerResponse;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.logging.MyLogger;
import lombok.Data;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

@Data
@Service
public class TeaComputersAddClientBankAccountApi extends RestTeaComputersApi<AddClientBankAccountRequest, AddClientBankAccountResponse> {

    public static final String PATH="/AddClientBankAcc";
    private String locale;

    @Override
    protected String generateURL(String params) {
        return super.generateBaseURL()+PATH;
    }

    @Override
    protected HttpMethod chooseHttpMethod() {
        return HttpMethod.POST;
    }

    @Override
    protected String generateSignature(AddClientBankAccountRequest teaComputerRequest) {
        return this.teaComputersSignatureGenerator.generateSignature("",teaComputerRequest.getIdTypeId()!=null?teaComputerRequest.getIdTypeId().toString():null,teaComputerRequest.getIdNumber());
    }

    @Override
    protected String generateResponseSignature(TeaComputerResponse teaComputerResponse) {
        return this.teaComputersSignatureGenerator.generateSignature("",teaComputerResponse.getMessage());
    }

    @Override
    public HttpHeaders generateHeaders() {
        HttpHeaders headers = super.generateHeaders();
        if(StringUtility.isStringPopulated(locale)){
            if(StringUtility.stringsMatch(locale,StringUtility.ARABIC)){
                locale=ARABIC;
            }else if(StringUtility.stringsMatch(locale,StringUtility.ENGLISH)){
                locale=ENGLISH;
            }
        }
        headers.add("lang",locale);
        MyLogger.info("Generated Headers:::"+headers.toString());
        return headers;
    }

    public AddClientBankAccountRequest prepareRequest(BusinessClientBankAccountDetails businessClientBankAccountDetails){
        AddClientBankAccountRequest request = new AddClientBankAccountRequest();
        request.setIdTypeId(businessClientBankAccountDetails.getAzIdType());
        request.setIdNumber(businessClientBankAccountDetails.getAzId());

        request.setBankId(businessClientBankAccountDetails.getBankId());
        request.setBranchId(businessClientBankAccountDetails.getBranchId());
        request.setCurrencyId(businessClientBankAccountDetails.getCurrencyId());
        request.setAccountNo(businessClientBankAccountDetails.getAccountNumber());
        request.setSwiftCode(businessClientBankAccountDetails.getSwiftCode());
        request.setIBAN(businessClientBankAccountDetails.getIban());
        request.setSignature(this.generateSignature(request));

        return request;
    }
}
