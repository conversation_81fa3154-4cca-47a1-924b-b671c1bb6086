package innovitics.azimut.services;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import innovitics.azimut.models.Notification;
import innovitics.azimut.repositories.notification.NotificationRepository;
import innovitics.azimut.utilities.dbutilities.DatabaseConditions;
import innovitics.azimut.utilities.dbutilities.SearchCriteria;
import innovitics.azimut.utilities.dbutilities.SearchOperation;
import innovitics.azimut.utilities.dbutilities.specifications.child.NotificationSpecification;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class NotificationCenterService extends AbstractService<Notification, String> {

  @Autowired
  NotificationRepository notificationRepository;
  @Autowired
  NotificationSpecification notificationSpecification;

  public List<Notification> findAll() {
    List<Notification> notifications = new ArrayList<Notification>();
    notifications = this.notificationRepository.findAll();
    MyLogger.info("notifications::" + notifications.toString());

    return notifications;
  }

  public Page<Notification> findAllByUserId(Long userId, DatabaseConditions databaseConditions) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    if (this.searchCriteriaListUtility.isListPopulated(databaseConditions.getSearchCriteria())) {
      searchCriteriaList = databaseConditions.getSearchCriteria();
    }
    searchCriteriaList.add(new SearchCriteria("userId", userId, SearchOperation.EQUAL, null));
    return this.notificationRepository.findAll(notificationSpecification.findByCriteria(searchCriteriaList),
        databaseConditions.getPageRequest());
  }

  public Notification addNotification(Long userId, String title, String body, String titleAr, String bodyAr,
      String language, Integer navigation, Integer liveNavigation, Long fundId, String link, String notificationType) {
    Notification notification = new Notification();
    notification.setCreatedAt(new Date());
    notification.setNotificationText(body);
    notification.setNotificationHeader(title);
    notification.setNotificationTextAr(bodyAr);
    notification.setNotificationHeaderAr(titleAr);
    notification.setUserId(userId);
    notification.setIsRead(false);
    notification.setNavigation(navigation);
    notification.setLiveNavigation(liveNavigation);
    notification.setFundId(fundId);
    notification.setLink(link);
    notification.setNotificationType(notificationType);
    return this.notificationRepository.save(notification);
  }

  public void readNotification(Long id) {
    this.notificationRepository.readNotification(id);
  }
}
