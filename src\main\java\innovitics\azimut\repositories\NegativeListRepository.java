package innovitics.azimut.repositories;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;

import innovitics.azimut.models.NegativeList;

public interface NegativeListRepository extends JpaRepository<NegativeList, Long>,
    EntityGraphJpaSpecificationExecutor<NegativeList> {

  // Basic search methods
  boolean existsByFullNameIgnoreCase(String fullName);

  List<NegativeList> findByFullNameContainingIgnoreCase(String fullName);

  List<NegativeList> findByListSourceAndIsActive(String listSource, Boolean isActive);

  List<NegativeList> findByIsActive(Boolean isActive);

  // Advanced fuzzy search methods
  @Query("SELECT n FROM NegativeList n WHERE " +
      "(LOWER(n.fullName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
      "LOWER(n.firstName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
      "LOWER(n.lastName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
      "LOWER(n.alias) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) AND " +
      "n.isActive = true")
  List<NegativeList> findByNameFuzzySearch(@Param("searchTerm") String searchTerm);

  @Query("SELECT n FROM NegativeList n WHERE " +
      "n.passportNumber = :passportNumber OR " +
      "n.nationalId = :nationalId AND " +
      "n.isActive = true")
  List<NegativeList> findByIdentificationDocuments(@Param("passportNumber") String passportNumber,
      @Param("nationalId") String nationalId);

  @Query("SELECT n FROM NegativeList n WHERE " +
      "n.nationality = :nationality AND " +
      "n.dateOfBirth = :dateOfBirth AND " +
      "n.isActive = true")
  List<NegativeList> findByNationalityAndDateOfBirth(@Param("nationality") String nationality,
      @Param("dateOfBirth") String dateOfBirth);

  // Count methods for statistics
  @Query("SELECT COUNT(n) FROM NegativeList n WHERE n.listSource = :listSource AND n.isActive = true")
  Long countByListSourceAndActive(@Param("listSource") String listSource);

  @Query("SELECT n.listSource, COUNT(n) FROM NegativeList n WHERE n.isActive = true GROUP BY n.listSource")
  List<Object[]> countByListSource();
}
