package innovitics.azimut.controllers.users;

import java.io.IOException;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.multipart.MultipartFile;

import innovitics.azimut.businessmodels.trading.BaseAzimutTrading;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.businessservices.BusinessAzimutTradingService;
import innovitics.azimut.controllers.BaseController;
import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.controllers.GenericResponseHandler;
import innovitics.azimut.controllers.users.DTOs.CancelOrderDto;
import innovitics.azimut.controllers.users.DTOs.PlaceOrderDto;
import innovitics.azimut.controllers.users.DTOs.WithdrawDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.utilities.businessutilities.UserUtility;
import innovitics.azimut.utilities.datautilities.BooleanUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;

@RestController
@RequestMapping(value = "/api/azimut/trading", produces = {
    MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE })
public class AzimutTradingController extends BaseController {

  @Autowired
  BusinessAzimutTradingService businessAzimutTradingService;

  @Autowired
  GenericResponseHandler<BaseAzimutTrading> businessAzimutTradingResponseHandler;

  @Autowired
  UserUtility userUtility;

  @PostMapping(value = "/placeOrder", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BaseAzimutTrading>> placeOrder(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody PlaceOrderDto baseAzimutTrading) throws BusinessException, Exception {
    BusinessUser currentUser = this.getCurrentRequestHolder(token);
    return businessAzimutTradingResponseHandler.generateBaseGenericResponse(BaseAzimutTrading.class,
        this.businessAzimutTradingService.placeOrderRest(currentUser, baseAzimutTrading),
        null, null);
  }

  @PostMapping(value = "/cancelOrder", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BaseAzimutTrading>> cancelOrder(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody CancelOrderDto baseAzimutTrading) throws BusinessException, Exception {
    return businessAzimutTradingResponseHandler.generateBaseGenericResponse(BaseAzimutTrading.class,
        this.businessAzimutTradingService.cancelOrderRest(this.getCurrentRequestHolder(token), baseAzimutTrading),
        null, null);
  }

  @PostMapping(value = "/inject", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BaseAzimutTrading>> inject(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @RequestParam("orderValue") Double orderValue,
      @RequestParam("currencyId") Long currencyId,
      @RequestParam("accountId") Long accountId,
      @RequestParam(name = "referenceNumber", required = false) String referenceNumber,
      @RequestParam("bankId") Long bankId,
      @RequestParam(name = "file", required = false) MultipartFile file) throws BusinessException,
      MaxUploadSizeExceededException, IllegalStateException, IOException, IntegrationException, Exception {
    BaseAzimutTrading inputBaseAzimutTrading = new BaseAzimutTrading();
    inputBaseAzimutTrading.setOrderValue(orderValue);
    inputBaseAzimutTrading.setCurrencyId(currencyId);
    inputBaseAzimutTrading.setAccountId(accountId);
    inputBaseAzimutTrading.setReferenceNo(referenceNumber);
    inputBaseAzimutTrading.setBankId(bankId);
    if (file != null && !file.isEmpty()) {
      inputBaseAzimutTrading.setInjectionDocument(file);
    }

    return businessAzimutTradingResponseHandler.generateBaseGenericResponse(BaseAzimutTrading.class,
        this.businessAzimutTradingService.injectRest(this.getCurrentRequestHolder(token), inputBaseAzimutTrading),
        null, null);
  }

  @PostMapping(value = "/withdraw", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BaseAzimutTrading>> withdraw(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody WithdrawDto baseAzimutTrading)
      throws BusinessException, IOException, IntegrationException, Exception {
    BusinessUser currentUser = this.getCurrentRequestHolder(token);
    String otp = baseAzimutTrading.getOtp();
    if (businessAzimutTradingService.isOtpRequired(baseAzimutTrading.getOrderValue(),
        baseAzimutTrading.getCurrencyId().intValue())
        && !this.userUtility.validateOTP(otp, currentUser.getUserPhone()))
      throw new BusinessException(ErrorCode.INVALID_OTP);

    if (!StringUtility.isStringPopulated(currentUser.getSignedPdf()) && BooleanUtility.isFalse(currentUser.getIsOld()))
      throw new BusinessException(ErrorCode.CONTRACT_NOT_SIGNED);

    return businessAzimutTradingResponseHandler.generateBaseGenericResponse(BaseAzimutTrading.class,
        this.businessAzimutTradingService.withdrawRest(currentUser, baseAzimutTrading), null,
        null);
  }

  @GetMapping(value = "/incrementUserBlockage")
  protected ResponseEntity<BaseGenericResponse<BaseAzimutTrading>> incrementUserBlockage(

      @RequestHeader(name = StringUtility.AUTHORIZATION_HEADER, required = false) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @RequestParam(name = "countryPhoneCode", required = false) String countryPhoneCode,
      @RequestParam(name = "phoneNumber", required = false) String phoneNumber

  ) throws IOException, BusinessException {
    return businessAzimutTradingResponseHandler.generateBaseGenericResponse(BaseAzimutTrading.class,
        this.businessAzimutTradingService.incrementUserBlockage(
            StringUtility.isStringPopulated(token) ? this.getCurrentRequestHolder(token) : null,
            this.businessAzimutTradingService.getConcatenatedValue(countryPhoneCode, phoneNumber)),
        null, null);
  }

  @GetMapping(value = "/getUserBlockage")
  protected ResponseEntity<BaseGenericResponse<BaseAzimutTrading>> getUserBlockage(
      @RequestHeader(name = StringUtility.AUTHORIZATION_HEADER, required = false) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @RequestParam(name = "countryPhoneCode", required = false) String countryPhoneCode,
      @RequestParam(name = "phoneNumber", required = false) String phoneNumber) throws IOException, BusinessException {
    return businessAzimutTradingResponseHandler.generateBaseGenericResponse(BaseAzimutTrading.class,
        this.businessAzimutTradingService.getUserBlockage(
            StringUtility.isStringPopulated(token) ? this.getCurrentRequestHolder(token) : null,
            this.businessAzimutTradingService.getConcatenatedValue(countryPhoneCode, phoneNumber)),
        null, null);

  }
}
