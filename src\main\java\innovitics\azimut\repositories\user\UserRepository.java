package innovitics.azimut.repositories.user;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import innovitics.azimut.models.user.User;

@Repository
public interface UserRepository extends JpaRepository<User, Long>, UserRepositoryCustom {
  public User findByUserId(String userId);

  List<User> findByIdIn(List<Long> ids);

  List<User> findByUserIdIn(List<String> ids);

}
