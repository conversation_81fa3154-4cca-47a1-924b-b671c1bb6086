package innovitics.azimut.repositories.digitalregistry;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import innovitics.azimut.models.digitalregistry.ApiRegistry;

@Repository
public interface ApiRegistryRepository
    extends JpaRepository<ApiRegistry, Long>, JpaSpecificationExecutor<ApiRegistry> {

}
