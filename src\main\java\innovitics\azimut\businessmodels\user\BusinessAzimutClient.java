package innovitics.azimut.businessmodels.user;

import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import innovitics.azimut.businessmodels.BusinessTransaction;
import innovitics.azimut.businessmodels.funds.BusinessClientFund;
import innovitics.azimut.models.azimutdetails.AzimutDetails;
import innovitics.azimut.models.teacomputers.Currency;
import innovitics.azimut.utilities.CustomJsonRootName;
import innovitics.azimut.utilities.crosslayerenums.AzimutEntityType;
import innovitics.azimut.utilities.datautilities.PaginatedEntity;
import lombok.Data;

@Data
@CustomJsonRootName(plural = "azAccounts", singular = "azAccount")
public class BusinessAzimutClient {
  private Long id;
  private BusinessClientCashBalance businessClientCashBalance;
  private List<BusinessClientCashBalance> businessClientCashBalances;
  @JsonProperty("businessTransactions")
  private List<BusinessTransaction> transactions;
  private List<BusinessClientBankAccountDetails> bankList;
  private BusinessClientBankAccountDetails bankAccountDetails;
  private Double pendingAmount;
  private Double balance;
  private String lastTransactionDate;
  private String balanceCurrency;
  private Double totalPendingAmount;
  private String tPACurrency;
  private Double currentRevenue;
  private Double currentRevenuePercent;
  private List<AzimutAccount> azimutAccounts;
  private BusinessAzimutDataLookup lookupData;
  private BusinessClientBankAccountDetails[] clientBankAccounts;
  private Long entityTypeId;
  private String param;
  private AzimutDetails azimutDetails;
  private List<BusinessClientFund> businessClientFunds;
  private PaginatedEntity<BusinessClientFund> paginatedBusinessClientFunds;
  private BigDecimal totalPosition;
  private List<EportfolioDetail> eportfolioDetails;
  private String reportType;
  private Long currencyId;
  private List<Currency> currency;
  private Boolean owned;
  protected String sorting;
  protected Long accountId;
  protected Long azIdType;
  protected String azId;
  protected Integer verificationPercentage;
  protected Long firstPageId;
  protected String documentURL;
  protected String language;
  protected String searchFromDate;
  protected String searchToDate;
  protected Long transactionId;
  protected String firstName;
  protected String lastName;
  protected String city;
  protected String country;
  protected String emailAddress;
  protected String userPhone;

  public BusinessAzimutClient(BusinessClientBankAccountDetails[] clientBankAccounts) {
    this.clientBankAccounts = clientBankAccounts;
  }

  public BusinessAzimutClient(AzimutEntityType azimutEntityType) {
    this.entityTypeId = azimutEntityType.getTypeId();
    this.param = azimutEntityType.getParam();
  }

  public BusinessAzimutClient(AzimutDetails azimutDetails) {
    this.azimutDetails = azimutDetails;
  }

  public BusinessAzimutClient() {

  }

}
