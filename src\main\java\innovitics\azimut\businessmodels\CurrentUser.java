package innovitics.azimut.businessmodels;

import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.stereotype.Component;

import innovitics.azimut.businessmodels.user.BusinessUserInterface;

@Component
@Scope(value = "request", proxyMode = ScopedProxyMode.TARGET_CLASS)
public class CurrentUser {
    private BusinessUserInterface currentUser;

    public BusinessUserInterface getCurrentUser() {
        return currentUser;
    }

    public void setCurrentUser(BusinessUserInterface currentUser) {
        this.currentUser = currentUser;
    }
}