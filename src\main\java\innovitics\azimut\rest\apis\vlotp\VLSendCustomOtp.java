package innovitics.azimut.rest.apis.vlotp;

import java.io.IOException;
import java.io.StringReader;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.w3c.dom.Document;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;

import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.rest.RestBaseApi;
import innovitics.azimut.rest.entities.vl.VLOtpRequest;
import innovitics.azimut.utilities.crosslayerenums.Messages;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class VLSendCustomOtp extends RestBaseApi<VLOtpRequest, String> {

  @Override
  protected String generateURL(String params) {
    return this.configProperties.getVlCustomSMSUrl();
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.POST;
  }

  @Override
  public HttpHeaders generateHeaders() {
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
    return headers;
  }

  @Override
  protected HttpEntity<?> generateRequestFromInput(VLOtpRequest input) {
    LinkedMultiValueMap<String, Object> map = new LinkedMultiValueMap<>();

    map.add("UserName", configProperties.getVlUsername());
    map.add("Password", configProperties.getVlPassword());
    map.add("SMSText", String.format(Messages.OTP_PHONE.getMessage(), input.getOtp()));
    map.add("SMSLang", "E");
    map.add("SMSSender", configProperties.getVlSender());
    map.add("SMSReceiver", input.getPhone());

    return new HttpEntity<>(map, this.generateHeaders());
  }

  @Override
  protected void validateResponse(ResponseEntity<String> responseEntity) throws IntegrationException {
    super.validateResponse(responseEntity);
    DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
    DocumentBuilder builder;
    try {
      builder = factory.newDocumentBuilder();
      InputSource is = new InputSource(new StringReader(responseEntity.getBody()));
      Document res = builder.parse(is);

      if (Integer.parseInt(res.getChildNodes().item(0).getTextContent()) != 0) {
        IntegrationException integrationException = new IntegrationException(ErrorCode.OTP_NOT_SAVED);
        throw integrationException;
      }
    } catch (ParserConfigurationException e) {
      MyLogger.logStackTrace(e);
      IntegrationException integrationException = new IntegrationException(ErrorCode.OTP_NOT_SAVED);
      throw integrationException;
    } catch (SAXException e) {
      MyLogger.logStackTrace(e);
      IntegrationException integrationException = new IntegrationException(ErrorCode.OTP_NOT_SAVED);
      throw integrationException;
    } catch (IOException e) {
      MyLogger.logStackTrace(e);
      IntegrationException integrationException = new IntegrationException(ErrorCode.OTP_NOT_SAVED);
      throw integrationException;
    }

  }

}
