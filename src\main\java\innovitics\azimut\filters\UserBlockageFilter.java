package innovitics.azimut.filters;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import innovitics.azimut.businessmodels.CurrentUser;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.businessservices.BusinessUserService;
import innovitics.azimut.configproperties.ConfigProperties;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.models.user.UserBlockage;
import innovitics.azimut.security.JwtUtil;
import innovitics.azimut.utilities.businessutilities.PhoneNumberBlockageUtility;
import innovitics.azimut.utilities.businessutilities.UserBlockageUtility;
import innovitics.azimut.utilities.datautilities.DateUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;
import innovitics.azimut.utilities.mapping.UserMapper;

@Component
public class UserBlockageFilter extends GenericFilter implements Filter {
  @Autowired
  BusinessUserService businessUserService;
  @Autowired
  UserBlockageUtility userBlockageUtility;
  @Autowired
  PhoneNumberBlockageUtility phoneNumberBlockageUtility;
  @Autowired
  UserMapper userMapper;
  @Autowired
  JwtUtil jwtUtil;
  @Autowired
  CurrentUser currentUser;
  @Autowired
  ConfigProperties configProperties;

  @Override
  public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
      throws IOException, ServletException {
    HttpServletRequest wrapper = ((HttpServletRequest) request);
    MyLogger.info("URI:::" + wrapper.getRequestURI());
    // Boolean
    // hasToken=CurrentRequestHolder.get()!=null&&CurrentRequestHolder.get().getId()!=null;
    // Boolean
    // hasToken=this.currentUser.getCurrentUser()!=null&&this.currentUser.getCurrentUser().getId()!=null;
    try {
      final String authorizationHeader = wrapper.getHeader(StringUtility.AUTHORIZATION_HEADER);
      Boolean hasToken = false;
      BusinessUser businessUser = new BusinessUser();
      if (StringUtility.isStringPopulated(authorizationHeader)) {
        hasToken = true;
        final String adminHeader = wrapper.getHeader(StringUtility.ADMIN_HEADER);
        final boolean isAdmin = StringUtility.isStringPopulated(adminHeader)
            && StringUtility.TRUE.contains(adminHeader);
        if (isAdmin) {
          chain.doFilter(request, response);
          return;
        }
        businessUser = (BusinessUser) this.currentUser.getCurrentUser();
        /*
         * try
         * {
         * businessUser=this.businessUserService.getByUserPhone(this.jwtUtil.
         * extractUsername(StringUtility.generateSubStringStartingFromCertainIndex(
         * authorizationHeader,' ')));
         * }
         * catch (BusinessException businessException)
         * {
         * this.setErrorResponse(HttpStatus.BAD_REQUEST, (HttpServletResponse)response,
         * businessException.getError());
         * }
         */
      }

      if (this.applyFilterOnPath(this.getFilterablePaths(), wrapper.getRequestURI())) {
        MyLogger.info("User Blockage Filter::");
        int actualNumberOfTrials = this.configProperties.getBlockageNumberOfTrials() != null
            ? this.configProperties.getBlockageNumberOfTrialsInt().intValue()
            : 0;
        UserBlockage userBlockage = this.checkUserBlockageType(businessUser, wrapper, hasToken);

        if (userBlockage != null) {
          if ((userBlockage.getErrorCount() != null && userBlockage.getErrorCount() >= actualNumberOfTrials)
              && (DateUtility.getMinutesBefore(this.configProperties.getBlockageDurationInMinutes())
                  .before(userBlockage.getUpdatedAt()))) {
            throw new BusinessException(ErrorCode.USER_BLOCKED);
          } else {
            chain.doFilter(request, response);
            this.updateUserBlockage(businessUser, hasToken, userBlockage, wrapper);
            return;
          }
        } else {
          chain.doFilter(request, response);
          return;
        }
      } else {
        chain.doFilter(request, response);
        return;
      }
    } catch (IOException e) {
      MyLogger.logStackTrace(e);
      setErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR, (HttpServletResponse) response,
          ErrorCode.OPERATION_NOT_PERFORMED);
    } catch (ServletException e) {
      MyLogger.logStackTrace(e);
      setErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR, (HttpServletResponse) response,
          ErrorCode.OPERATION_NOT_PERFORMED);
    } catch (BusinessException exception) {
      MyLogger.logStackTrace(exception);
      setErrorResponse(HttpStatus.BAD_REQUEST, (HttpServletResponse) response, exception.getError());
    }

  }

  @Override
  List<String> getFilterablePaths() {
    final String tradingPath = this.configProperties.getTradingPath();
    final String otpPath = this.configProperties.getOtpPath();

    return Arrays.asList(tradingPath + "placeOrder", tradingPath + "inject", tradingPath + "withdraw", otpPath + "add",
        otpPath + "verify");
  }

  UserBlockage checkUserBlockageType(BusinessUser businessUser, HttpServletRequest wrapper, boolean hasToken)
      throws BusinessException {

    if (hasToken) {
      UserBlockage userBlockage = userBlockageUtility.getUserBlockage(businessUser.getId(), false);
      return userBlockage;
    } else {
      UserBlockage userBlockage = phoneNumberBlockageUtility.getUserBlockage(this.getUserPhone(wrapper), false);
      return userBlockage;
    }
  }

  String getUserPhone(HttpServletRequest wrapper) {
    MyLogger.info(
        "Parameters:::" + wrapper.getParameter("countryPhoneCode") + "::::::" + wrapper.getParameter("phoneNumber"));
    return "+" + wrapper.getParameter("countryPhoneCode") + wrapper.getParameter("phoneNumber");
  }

  void updateUserBlockage(BusinessUser businessUser, boolean hasToken, UserBlockage userBlockage,
      HttpServletRequest wrapper) {
    if (hasToken) {
      userBlockage.setUser(userMapper.convertBusinessUnitToBasicUnit((BusinessUser) businessUser, false));
    } else {
      userBlockage.setUserPhone(this.getUserPhone(wrapper));
    }
    userBlockage.setErrorCount(0);
    userBlockageUtility.updateUserBlockage(userBlockage);
  }

}
