package innovitics.azimut.rest.entities.gateid;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class GateIdResult extends GateIdOutput {

  private String address;
  private String area;

  @JsonProperty("birth_date")
  private String birthDate;

  @JsonProperty("birth_date_en")
  private String birthDateEn;

  @JsonProperty("birth_place")
  private String birthPlace;

  @JsonProperty("creation_date")
  private String creationDate;

  @JsonProperty("creation_date_en")
  private String creationDateEn;

  @JsonProperty("expiration_date")
  private String expirationDate;

  @JsonProperty("expiration_date_en")
  private String expirationDateEn;

  private String fcn;

  @JsonProperty("first_name")
  private String firstName;

  @JsonProperty("font_manipulation_percentage")
  private Double fontManipulationPercentage;

  private String gender;

  @JsonProperty("husband_name")
  private String husbandName;

  @JsonProperty("last_name")
  private String lastName;

  private String marital;

  private String nid;

  @JsonProperty("nid_en")
  private String nidEn;

  @JsonProperty("passport_number")
  private String passportNumber; // passport

  private String country; // passport

  private String nationality; // passport

  @JsonProperty("valid_score")
  private Double validScore; // passport

  private String front; // passport image

  @JsonProperty("potential_fraud")
  private Boolean potentialFraud;

  private String profession;

  private String religion;

  private String workplace;

  // New fields only from the task results
  @JsonProperty("front_rotation_angle")
  private Integer frontRotationAngle;

  @JsonProperty("front_color_score")
  private Double frontColorScore;

  @JsonProperty("photo_type")
  private String photoType;

  @JsonProperty("fraud_front")
  private Boolean fraudFront;

  @JsonProperty("back_rotation_angle")
  private Integer backRotationAngle;

  @JsonProperty("back_color_score")
  private Double backColorScore;

  private Boolean eagle;

  private Boolean pharaoh;

  private Integer similarity;

  private String decision;
}
