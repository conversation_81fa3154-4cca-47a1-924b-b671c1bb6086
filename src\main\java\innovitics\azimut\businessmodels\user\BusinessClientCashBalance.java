package innovitics.azimut.businessmodels.user;

import innovitics.azimut.utilities.crosslayerenums.CurrencyType;
import innovitics.azimut.utilities.datautilities.StringUtility;
import lombok.Data;

@Data
public class BusinessClientCashBalance {

  private Long idType;
  private String idNumber;
  private Long currencyID;
  private String currencyName;
  private Double balance;
  private String balanceFormatted;
  private Double pendingTransfer;
  private String pendingTransferFormatted;
  private Double inPendingTrans;
  private Double outPendingTrans;
  private Double currencyRate;
  private Double totalBuyValue;
  protected Long azIdType;
  protected String azId;

  public BusinessClientCashBalance() {
  }

  public BusinessClientCashBalance(Long currencyID, String currencyName, Double balance, String balanceFormatted,
      Double pendingTransfer, String pendingTransferFormatted, Double inPendingTrans, Double outPendingTrans,
      Double currencyRate) {
    this.currencyID = currencyID;
    this.currencyName = currencyName;
    this.balance = balance;
    this.balanceFormatted = balanceFormatted;
    this.pendingTransfer = pendingTransfer;
    this.pendingTransferFormatted = pendingTransferFormatted;
    this.inPendingTrans = inPendingTrans;
    this.outPendingTrans = outPendingTrans;
    this.currencyRate = currencyRate;
  }

  public BusinessClientCashBalance(Long currencyID, String language) {
    this.currencyID = currencyID;
    this.currencyName = StringUtility.stringsMatch(language, "ar") ? CurrencyType.getById(currencyID).getTypeAr()
        : CurrencyType.getById(currencyID).getType();
  }

  public BusinessClientCashBalance(CurrencyType currencyType, String language) {
    this.currencyID = currencyType.getTypeId();
    this.currencyName = StringUtility.stringsMatch(language, "ar") ? CurrencyType.getById(currencyID).getTypeAr()
        : CurrencyType.getById(currencyID).getType();
    this.balance = 0D;
    this.balanceFormatted = "0";
    this.pendingTransfer = 0D;
    this.pendingTransferFormatted = "0";
  }

}
