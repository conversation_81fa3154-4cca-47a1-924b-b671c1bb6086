package innovitics.azimut.businessmodels;

import com.fasterxml.jackson.annotation.JsonProperty;

import innovitics.azimut.utilities.CustomJsonRootName;
import lombok.Data;

@Data
@CustomJsonRootName(plural = "response", singular = "response")
public class BusinessPayment extends BaseBusinessEntity {

  @JsonProperty("orderValue")
  private Double amount;
  private String notes;
  private String referenceTransactionId;
  private Long id;

  private String transactionStatus;
  private String statusMessage;
  private String redirectUrl;
  private Integer action;
  private String returnUrl;
  private String cartId;
  private Boolean isMobile;
  private Long bankId;
  private Long transactionId;
  private String language;
  private String firstName;
  private String lastName;
  private Long currencyId;
  private String city;
  private String country;
  private String emailAddress;
  private String userPhone;
  private Long accountId;
  private String publicKey;
  private String secretKey;

  public BusinessPayment(String referenceTransactionId) {

    this.referenceTransactionId = referenceTransactionId;
  }

  public BusinessPayment() {

  }

}
