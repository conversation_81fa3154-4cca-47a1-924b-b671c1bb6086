package innovitics.azimut.businessmodels.user;

import lombok.Data;

@Data
public class AuthenticationRequest {

  private String userPhone;
  private String countryPhoneCode;
  private String phoneNumber;
  private String password;
  private String refreshToken;
  private String newPassword;
  private String email;
  private Boolean isAdmin;
  private String fullName;
  protected String deviceId;

  public AuthenticationRequest(String userPhone, String countryPhoneCode, String phoneNumber, String password) {
    this.userPhone = userPhone;
    this.countryPhoneCode = countryPhoneCode;
    this.phoneNumber = phoneNumber;
    this.password = password;
  }

  public AuthenticationRequest(String password, String email) {
    this.password = password;
    this.email = email;
  }

  public AuthenticationRequest() {
  }

}
