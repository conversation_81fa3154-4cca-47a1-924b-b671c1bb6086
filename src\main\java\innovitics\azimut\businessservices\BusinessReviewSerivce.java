package innovitics.azimut.businessservices;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import innovitics.azimut.businessmodels.admin.BusinessAdminUser;
import innovitics.azimut.businessmodels.kyc.BusinessReview;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.controllers.admin.DTOs.GetReasonsDto;
import innovitics.azimut.controllers.admin.DTOs.GetReviewsDto;
import innovitics.azimut.controllers.admin.DTOs.ReasonDto;
import innovitics.azimut.controllers.users.DTOs.EditUserAndSubmitReviewDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.models.kyc.Question;
import innovitics.azimut.models.kyc.Review;
import innovitics.azimut.services.kyc.QuestionService;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class BusinessReviewSerivce extends AbstractBusinessService<BusinessReview> {

  @Autowired
  QuestionService questionService;
  @Autowired
  BusinessClientDetailsService businessClientDetailsService;
  @Autowired
  BusinessKYCPageService businessKYCPageService;

  public BusinessReview submitReviews(BusinessAdminUser businessAdminUser,
      EditUserAndSubmitReviewDto baseBusinessEntity, String language)
      throws BusinessException, IntegrationException, IOException {

    BusinessUser businessUser = this.findUserById(baseBusinessEntity.getAppUserId(), true);
    // this.validation.validateWithCustomError(businessUser,
    // KycStatus.PENDING_CLIENT, ErrorCode.CLIENT_SOLVING);
    if (reviewListUtility.isListPopulated(baseBusinessEntity.getReviews())) {

      validation.validateReviewSubmission(baseBusinessEntity.getReviews(),
          this.checkQuestionMandatoriness(baseBusinessEntity.getPageId(), baseBusinessEntity.getReviews()),
          baseBusinessEntity.getPageId());
      reviewUtility.addReviews(businessAdminUser, baseBusinessEntity, language, businessUser);
    }
    this.editUser(businessUser);
    MyLogger.info("Kyc Status::" + businessUser.getKycStatus());
    this.addAtTCAndSendPushNotification(businessUser, businessClientDetailsService, businessKYCPageService, language);
    return new BusinessReview();
  }

  public BusinessReview getReviewPages(BusinessAdminUser businessAdminUser, GetReviewsDto baseBusinessEntity,
      String language) throws BusinessException {
    BusinessReview businessReview = new BusinessReview();
    List<Long> pageIds = new ArrayList<Long>();
    for (Review review : this.reviewUtility.getReviews(baseBusinessEntity)) {
      pageIds.add(review.getPageId());
    }

    businessReview.setPageIds(pageIds);

    return businessReview;
  }

  public List<BusinessReview> getAllReviews(Long userId,
      String language) throws BusinessException {
    return this.reviewUtility.getAllKycReviews(userId, language);
  }

  public BusinessReview getReasons(BusinessAdminUser businessAdminUser, GetReasonsDto baseBusinessEntity,
      String language) throws BusinessException {
    if (StringUtility.isStringPopulated(baseBusinessEntity.getSearch())) {
      return new BusinessReview(this.reviewUtility.findAllReasonsByKeyword(baseBusinessEntity.getSearch(), language));
    } else {
      return new BusinessReview(this.reviewUtility.findAllReasons());
    }
  }

  public BusinessReview addReason(BusinessAdminUser businessAdminUser, ReasonDto businessReview, String language)
      throws BusinessException {
    this.reviewUtility.addReason(businessAdminUser, businessReview);
    return new BusinessReview();
  }

  public BusinessReview editReason(BusinessAdminUser businessAdminUser, ReasonDto businessReview,
      String language)
      throws BusinessException {
    this.reviewUtility.editReason(businessAdminUser, businessReview);
    return new BusinessReview();
  }

  public BusinessReview deleteReason(BusinessAdminUser businessAdminUser, ReasonDto businessReview,
      String language) throws BusinessException {

    if (this.baseReviewListUtility.isListEmptyOrNull(this.reviewUtility.getReviewsByReasonId(businessReview))) {
      this.reviewUtility.deleteReason(businessAdminUser, businessReview);
      return new BusinessReview();
    } else {
      throw new BusinessException(ErrorCode.REASON_IN_USE);
    }
  }

  List<Long> checkQuestionMandatoriness(Long pageId, List<BusinessReview> businessReviews) {
    MyLogger.info("checking answer manda");
    List<Long> mandatoryQuestionIds = this.questionService.getQuestionIdsAndMandatorinerssByPageId(true, pageId);
    List<Long> optionalQuestionIds = this.questionService.getQuestionIdsAndMandatorinerssByPageId(false, pageId);

    List<Question> questionMandatorinessList = new ArrayList<Question>();
    List<Long> questionIds = new ArrayList<Long>();

    for (Long mandatoryId : mandatoryQuestionIds) {
      Question question = new Question();
      question.setId(mandatoryId);
      question.setIsAnswerMandatory(true);
      questionMandatorinessList.add(question);
    }
    for (Long optionalId : optionalQuestionIds) {
      Question question = new Question();
      question.setId(optionalId);
      question.setIsAnswerMandatory(false);
      questionMandatorinessList.add(question);
    }

    MyLogger.info("List::::" + questionMandatorinessList.toString());

    for (Question question : questionMandatorinessList) {
      questionIds.add(question.getId());
      for (BusinessReview businessReview : businessReviews) {
        if (NumberUtility.areLongValuesMatching(question.getId(), businessReview.getQuestionId())) {
          MyLogger.info("Question:::::::" + question.toString());

          businessReview.setMandatoryQuestion(question.getIsAnswerMandatory());
          MyLogger.info("BusinessReview:::::::" + businessReview.toString());
        }
      }
    }

    return questionIds;
  }
}
