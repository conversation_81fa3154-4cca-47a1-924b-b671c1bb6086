package innovitics.azimut.validations;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.Validator;
import org.springframework.web.multipart.MultipartFile;

import innovitics.azimut.businessmodels.kyc.BusinessRelatedAnswer;
import innovitics.azimut.businessmodels.kyc.BusinessReview;
import innovitics.azimut.businessmodels.kyc.BusinessSubmittedAnswer;
import innovitics.azimut.businessmodels.kyc.BusinessUserSubmittedAnswer;
import innovitics.azimut.businessmodels.user.BusinessClientBankAccountDetails;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.businessservices.BusinessAdminUserService;
import innovitics.azimut.configproperties.ConfigProperties;
import innovitics.azimut.controllers.kyc.SubmitAnswersDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.models.user.User;
import innovitics.azimut.security.PasswordValidation;
import innovitics.azimut.services.kyc.QuestionService;
import innovitics.azimut.services.kyc.UserAnswerSubmissionService;
import innovitics.azimut.services.user.UserService;
import innovitics.azimut.utilities.businessutilities.ChangePhoneNumberRequestUtility;
import innovitics.azimut.utilities.businessutilities.ReviewUtility;
import innovitics.azimut.utilities.businessutilities.UserUtility;
import innovitics.azimut.utilities.crosslayerenums.AnswerType;
import innovitics.azimut.utilities.crosslayerenums.KycStatus;
import innovitics.azimut.utilities.crosslayerenums.ReviewResult;
import innovitics.azimut.utilities.datautilities.ArrayUtility;
import innovitics.azimut.utilities.datautilities.BooleanUtility;
import innovitics.azimut.utilities.datautilities.ListUtility;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.datautilities.UserTypeUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.kycutilities.AnswerTypeUtility;
import innovitics.azimut.utilities.logging.FileUtility;
import innovitics.azimut.utilities.logging.MyLogger;

@Component
public class Validation {

  protected static final Logger logger = LoggerFactory.getLogger(Validation.class);

  @Autowired
  PasswordValidation passwordValidation;
  @Autowired
  UserService userService;
  @Autowired
  ChangePhoneNumberRequestUtility changePhoneNumberRequestUtility;
  @Autowired
  AnswerTypeUtility answerTypeUtility;
  @Autowired
  ArrayUtility arrayUtility;
  @Autowired
  QuestionService questionService;
  @Autowired
  FileUtility fileUtility;
  @Autowired
  ConfigProperties configProperties;
  @Autowired
  ListUtility<Long> longListUtility;
  @Autowired
  ListUtility<BusinessReview> businessReviewListUtility;
  @Autowired
  ReviewUtility reviewUtility;
  @Autowired
  UserUtility userUtility;
  @Autowired
  UserAnswerSubmissionService userAnswerSubmissionService;

  public void validate(Object data, Validator validator, String objectName) throws BusinessException {
    BeanPropertyBindingResult result = new BeanPropertyBindingResult(data, objectName);

    validator.validate(data, result);

    if (result.hasErrors()) {

      String field = "";
      String value = "";

      if (result.hasErrors()) {
        if (result != null && result.getFieldError() != null)
          field = result.getFieldError().getField();
        if (result != null && result.getFieldError() != null && result.getFieldError().getRejectedValue() != null)
          value = result.getFieldError().getRejectedValue().toString();
      }

      MyLogger.info("Invalid value " + value + " for field " + field);

      throw this.populateInavlidFieldValueBusinessException(value, field);

    }
  }

  public void validateTwoPasswords(String password1, String password2) throws BusinessException {
    this.passwordValidation.validateTwoPasswords(password1, password2);
  }

  public void validateUser(Long id, BusinessUser businessUser) throws BusinessException {
    if (id == null) {
      throw new BusinessException(ErrorCode.UNAUTHORIZED_USER);
    }

    else if (id != null && businessUser != null && businessUser.getId().longValue() != id.longValue()) {
      throw new BusinessException(ErrorCode.UNAUTHORIZED_USER);
    }

    else if (id != null && id.longValue() < 0) {
      throw new BusinessException(ErrorCode.UNAUTHORIZED_USER);
    }

  }

  public void validateFilePresence(BusinessUser businessUser) throws BusinessException {
    if (businessUser != null) {
      if (businessUser.getFile() == null) {
        throw new BusinessException(ErrorCode.MISSING_FILE);
      }

    }
  }

  public void validateFileSize(MultipartFile file, long maximumSize) throws BusinessException {
    if (file != null) {
      if (file.getSize() > maximumSize) {
        MyLogger.info("File Size::::" + file.getSize());
        BusinessException businessException = new BusinessException(ErrorCode.FILE_TOO_BIG);
        businessException.setErrorMessage(
            "File size should not exceed " + this.fileUtility.getFileSizeInMegabytes(maximumSize) + " Megabytes");
        throw businessException;
      }

    }
  }

  public void validateFileExtension(MultipartFile file, String[] extensions) throws BusinessException {
    if (file != null) {
      if (StringUtility.isStringPopulated(file.getOriginalFilename())) {
        boolean hasExtension = false;
        for (String extension : extensions) {
          if (file.getOriginalFilename().toLowerCase().contains("." + extension))
            hasExtension = true;
        }
        if (!hasExtension) {
          MyLogger.info("File Name::::" + file.getName());
          BusinessException businessException = new BusinessException(ErrorCode.INVALID_EXTENSION);
          businessException.setErrorMessage(" Invalid file extension uploaded.");
          throw businessException;
        }
      }

    }
  }

  public void validateNewPhoneNumberAvailability(BusinessUser businessUser) throws BusinessException {
    User user = new User();
    if (businessUser != null && businessUser.getUserPhone() != null) {
      try {
        user = this.userService.findByUserPhone(businessUser.getUserPhone());
      } catch (Exception exception) {
        MyLogger.info("No user was found having this number");
      }
    }

    if (user != null && user.getUserPhone() != null)
      throw new BusinessException(ErrorCode.USER_EXISTS);

  }

  public void checkForOpenChangeRequests(BusinessUser businessUser) throws BusinessException {
    if (this.changePhoneNumberRequestUtility
        .checkIfTheCurrentRequestHolderHasAnyOpenChangePhoneNumberRequests(businessUser.getUserPhone())) {
      throw new BusinessException(ErrorCode.REQUESTS_FOUND);
    }
  }

  public void checkUserAnswersValidity(SubmitAnswersDto businessUserAnswerSubmission)
      throws BusinessException {
    MyLogger.info("Validating the User Answers:::");
    String errorMessage = "";
    if (businessUserAnswerSubmission != null) {

      if (this.arrayUtility.isArrayPopulated(businessUserAnswerSubmission.getUserAnswers())) {

        List<Long> questionIds = new ArrayList<Long>();
        for (BusinessUserSubmittedAnswer businessUserSubmittedAnswer : businessUserAnswerSubmission.getUserAnswers()) {
          questionIds.add(businessUserSubmittedAnswer.getQuestionId());
        }
        int suppliedMandatoryQuestionCount = 0;
        int mandatoryQuestionCount = 0;
        List<BigInteger> mandatoryQuestionIds = new ArrayList<BigInteger>();
        try {
          mandatoryQuestionIds = this.questionService
              .getMandatoryQuestionsByPage(businessUserAnswerSubmission.getPageId());
          MyLogger.info("Mandatory Question Ids::" + mandatoryQuestionIds.toString());

          mandatoryQuestionCount = mandatoryQuestionIds.size();
        } catch (Exception exception) {
          MyLogger.logStackTrace(exception);
          throw new BusinessException(ErrorCode.ANSWER_SUBMISSION_FAILED);
        }

        for (BigInteger mandatoryQuestionId : mandatoryQuestionIds) {
          if (questionIds.contains(mandatoryQuestionId.longValue()))
            suppliedMandatoryQuestionCount++;
        }

        MyLogger.info("mandatory Question Count: " + mandatoryQuestionCount);
        MyLogger.info("submiited mandatory Question Count: " + suppliedMandatoryQuestionCount);
        if (suppliedMandatoryQuestionCount == mandatoryQuestionCount) {
          for (BusinessUserSubmittedAnswer businessUserSubmittedAnswer : businessUserAnswerSubmission
              .getUserAnswers()) {
            if (businessUserSubmittedAnswer != null) {
              if (businessUserSubmittedAnswer.getQuestionId() != null) {

                if (this.arrayUtility.isArrayPopulated(businessUserSubmittedAnswer.getAnswers())) {
                  errorMessage = this.singleAnswerValidityCheck(businessUserSubmittedAnswer);
                  if (StringUtility.isStringPopulated(errorMessage))
                    break;
                } else {
                  errorMessage = "Answers Array object is empty";
                  break;
                }
              } else {
                errorMessage = "Question Id is empty";
                break;
              }
            }
          }
        } else {
          errorMessage = "A mandatory answer is missing";
        }
      }

      else {
        errorMessage = "User Answer Array object is empty";
      }
    } else {
      MyLogger.info("Request Object is Null");
      errorMessage = "Request is empty";
    }

    if (StringUtility.isStringPopulated(errorMessage)) {
      BusinessException businessException = new BusinessException(ErrorCode.ANSWER_SUBMISSION_FAILED);
      businessException.setErrorMessage(errorMessage);
      throw businessException;

    }

  }

  private String singleAnswerValidityCheck(BusinessUserSubmittedAnswer businessUserSubmittedAnswer) {
    String errorMessage = "";
    int radioAnswerCount = 0;
    for (BusinessSubmittedAnswer businessSubmittedAnswer : businessUserSubmittedAnswer.getAnswers()) {
      if (businessSubmittedAnswer != null) {
        if (this.answerTypeUtility.isAnswerTypeValid(businessSubmittedAnswer.getAnswerType())) {
          if (StringUtility.stringsMatch(businessSubmittedAnswer.getAnswerType(), AnswerType.RADIO.getType())) {
            radioAnswerCount++;
            if (radioAnswerCount > 1) {
              errorMessage = "Too many answers for question id:" + businessUserSubmittedAnswer.getQuestionId();
              break;
            }
          }

          if (StringUtility.stringsMatch(businessSubmittedAnswer.getAnswerType(), AnswerType.TEXT.getType())) {
            if (!StringUtility.isStringPopulated(businessSubmittedAnswer.getAnswerValue())) {
              errorMessage = "Answer is empty for question id:" + businessUserSubmittedAnswer.getQuestionId();
              break;
            }
          }

          if (this.arrayUtility.isArrayPopulated(businessSubmittedAnswer.getRelatedAnswers())) {
            errorMessage = this.singleRelatedAnswerValidityCheck(businessSubmittedAnswer);
            if (StringUtility.isStringPopulated(errorMessage))
              break;
          }

        } else {
          errorMessage = "Answers Id: " + businessSubmittedAnswer.getAnswerId() + " has an invalid type: "
              + businessSubmittedAnswer.getAnswerType();
          break;
        }
      }
    }

    return errorMessage;
  }

  private String singleRelatedAnswerValidityCheck(BusinessSubmittedAnswer businessSubmittedAnswer) {
    String errorMessage = "";
    int radioAnswerCount = 0;
    for (BusinessRelatedAnswer businessRelatedAnswer : businessSubmittedAnswer.getRelatedAnswers()) {
      if (businessRelatedAnswer != null) {
        if (this.answerTypeUtility.isAnswerTypeValid(businessRelatedAnswer.getRelatedAnswerType())) {
          if (StringUtility.stringsMatch(businessRelatedAnswer.getRelatedAnswerType(), AnswerType.RADIO.getType())) {
            radioAnswerCount++;
            if (radioAnswerCount > 1) {
              errorMessage = "Too many related answers for answer id:" + businessSubmittedAnswer.getAnswerId();
              break;
            }
          }
          // if (StringUtility.stringsMatch(businessRelatedAnswer.getRelatedAnswerType(),
          // AnswerType.TEXT.getType())) {
          // if
          // (!StringUtility.isStringPopulated(businessRelatedAnswer.getRelatedAnswerValue()))
          // {
          // errorMessage = "Answer is empty for answer id:" +
          // businessSubmittedAnswer.getAnswerId();
          // break;
          // }
          // }
        } else {
          errorMessage = "Related answer Id: " + businessRelatedAnswer.getRelatedAnswerId() + " has an invalid type: "
              + businessRelatedAnswer.getRelatedAnswerType();
          break;
        }
      }
    }

    return errorMessage;
  }

  BusinessException populateInavlidFieldValueBusinessException(String value, String field) {
    BusinessException businessException = new BusinessException(ErrorCode.INVALID_FIELD_VALUE);

    businessException.setErrorMessage("Invalid value " + value + " for field " + field);

    return businessException;
  }

  public List<String> validateKYCFormCompletion(BusinessUser businessUser, Long pageCount) throws BusinessException {
    MyLogger.info("Page Count:::" + pageCount);
    List<String> solvedPageOrders = new ArrayList<String>();
    if (businessUser != null) {
      if (StringUtility.isStringPopulated(businessUser.getSolvedPages())) {
        solvedPageOrders = Arrays.asList(businessUser.getSolvedPages().split(","));
        for (String solvedPage : solvedPageOrders) {
          MyLogger.info("Page Order:::" + solvedPage);
        }
      } else {
        throw new BusinessException(ErrorCode.KYC_INCOMPLETE);
      }
    }

    else {
      MyLogger.info("KYC Incomplete");
      throw new BusinessException(ErrorCode.OPERATION_NOT_PERFORMED);
    }
    if (pageCount != null && solvedPageOrders != null && !solvedPageOrders.isEmpty()) {
      Long solvedPageCount = Long.valueOf(solvedPageOrders.size());
      MyLogger.info("Solved Page Count:::" + solvedPageCount);

      if (solvedPageCount.longValue() < pageCount.longValue()) {

        throw new BusinessException(ErrorCode.KYC_INCOMPLETE);
      }

    }

    return solvedPageOrders;
  }

  public void validateUserKYCCompletion(BusinessUser tokenizedBusinessUser) throws BusinessException {
    if (tokenizedBusinessUser.getKycStatus() != null && !NumberUtility
        .areIntegerValuesMatching(KycStatus.FIRST_TIME.getStatusId(), tokenizedBusinessUser.getKycStatus())) {
      this.validateWithCustomError(tokenizedBusinessUser, KycStatus.PENDING, ErrorCode.USER_UNDER_REVIEW);
      this.validateWithCustomError(tokenizedBusinessUser, KycStatus.APPROVED, ErrorCode.KYC_SUBMITTED);
    }
  }

  public void validateAdminUserNoneExistence(String username, BusinessAdminUserService businessAdminUserService)
      throws BusinessException {
    if (businessAdminUserService.findAdminUserByUsername(username, false) != null) {
      throw new BusinessException(ErrorCode.USER_EXISTS);
    }
  }

  public void validateReviewSubmission(List<BusinessReview> businessReviews, List<Long> questionIds, Long pageId)
      throws BusinessException {
    boolean businessReviewListPopulated = businessReviewListUtility.isListPopulated(businessReviews);
    if (pageId != null && !NumberUtility.areLongValuesMatching(pageId, 0l)) {
      // List<Long> questionIds=this.questionService.getQuestionIdsByPageId(pageId);
      if (businessReviewListPopulated) {
        List<Long> reviewQuestionIds = businessReviews.stream().map(BusinessReview::getQuestionId)
            .collect(Collectors.toList());
        if (longListUtility.isListPopulated(reviewQuestionIds) && longListUtility.isListPopulated(questionIds)) {
          if (!NumberUtility.areTheTwoListsIdentical(reviewQuestionIds, questionIds)) {
            throw new BusinessException(ErrorCode.REVIEW_SUBMISSION_FAILED);
          }
        }
        this.checkForMissingReasonsOnRejection(businessReviews);
      }
    } else if (pageId == null || (pageId != null && NumberUtility.areLongValuesMatching(pageId, 0l))) {
      if (businessReviewListPopulated) {
        this.checkForMissingReasonsOnRejection(businessReviews);
      }
    }

  }

  public void validateWithCustomError(BusinessUser tokenizedBusinessUser, KycStatus kycStatus, ErrorCode errorCode)
      throws BusinessException {
    if (tokenizedBusinessUser != null
        && NumberUtility.areIntegerValuesMatching(tokenizedBusinessUser.getKycStatus(), kycStatus.getStatusId())) {
      throw new BusinessException(errorCode, HttpStatus.NOT_FOUND);
    }
  }

  public void validateClientBankAccountsWhenUnderReview(BusinessUser businessUser,
      BusinessClientBankAccountDetails[] oldClientBankAccounts,
      BusinessClientBankAccountDetails[] newClientBankAccounts) throws BusinessException {
    MyLogger.info("Validating:::");
    if (businessUser != null
        && !NumberUtility.areIntegerValuesMatching(businessUser.getKycStatus(), KycStatus.FIRST_TIME.getStatusId())) {
      if (this.arrayUtility.isArrayPopulated(newClientBankAccounts)
          && this.arrayUtility.isArrayPopulated(oldClientBankAccounts)) {
        for (BusinessClientBankAccountDetails oldBankAccount : oldClientBankAccounts) {
          for (BusinessClientBankAccountDetails newBankAccount : newClientBankAccounts) {
            if (NumberUtility.areLongValuesMatching(oldBankAccount.getBankId(), newBankAccount.getBankId())
                && NumberUtility.areLongValuesMatching(oldBankAccount.getBranchId(), newBankAccount.getBranchId())
                && NumberUtility.areLongValuesMatching(oldBankAccount.getCurrencyId(), newBankAccount.getCurrencyId())
                && StringUtility.stringsMatch(oldBankAccount.getIban(), newBankAccount.getIban())
                && StringUtility.stringsMatch(oldBankAccount.getAccountNumber(), newBankAccount.getAccountNumber())) {
              MyLogger.info("Error found:::");
              throw new BusinessException(ErrorCode.REJECTED_ANSWER_NOT_CHANGED);
            }
          }
        }
      }
    }
  }

  void checkForMissingReasonsOnRejection(List<BusinessReview> businessReviews) throws BusinessException {
    for (BusinessReview businessReview : businessReviews) {
      if (NumberUtility.areLongValuesMatching(businessReview.getStatus(), ReviewResult.REJECTED.getResultId())
          && businessReview.getReasonId() == null) {
        throw new BusinessException(ErrorCode.REASON_MISSING);
      }
    }
  }

  public void validateKYCSigning(BusinessUser tokenizedBusinessUser) throws BusinessException {
    boolean livenessNotChecked = BooleanUtility.isFalse(tokenizedBusinessUser.getLivenessChecked());
    boolean contractNotChosen = tokenizedBusinessUser.getContractMap() == null;
    boolean answersIncomplete = !this.userAnswerSubmissionService.areUserAnswersComplete(
        UserTypeUtility.getRelevantIdType(tokenizedBusinessUser), tokenizedBusinessUser.getId());
    MyLogger.info("livenessNotChecked::" + livenessNotChecked);
    MyLogger.info("contractNotChosen::" + contractNotChosen);
    MyLogger.info("answersIncomplete::" + answersIncomplete);

    if (NumberUtility.areIntegerValuesMatching(KycStatus.FIRST_TIME.getStatusId(),
        tokenizedBusinessUser.getKycStatus())) {
      if (livenessNotChecked || contractNotChosen || answersIncomplete) {
        throw new BusinessException(ErrorCode.KYC_INCOMPLETE);
      }

    } else {
      this.validateWithCustomError(tokenizedBusinessUser, KycStatus.REJECTED, ErrorCode.KYC_INCOMPLETE);
      this.validateWithCustomError(tokenizedBusinessUser, KycStatus.PENDING, ErrorCode.USER_UNDER_REVIEW);
      this.validateWithCustomError(tokenizedBusinessUser, KycStatus.APPROVED, ErrorCode.KYC_SUBMITTED);
      boolean hasRejectedReviews = (reviewUtility
          .isListPopulated(reviewUtility.getAnyRejectedReviews(tokenizedBusinessUser.getId())));
      MyLogger.info("hasRejectedReviews::" + hasRejectedReviews);
      if (livenessNotChecked || contractNotChosen || answersIncomplete || hasRejectedReviews) {
        throw new BusinessException(ErrorCode.KYC_INCOMPLETE);
      }
    }
  }

}
