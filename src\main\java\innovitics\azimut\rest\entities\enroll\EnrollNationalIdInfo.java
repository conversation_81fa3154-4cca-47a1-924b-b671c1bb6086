package innovitics.azimut.rest.entities.enroll;

import java.util.Date;

import lombok.Data;

@Data
public class EnrollNationalIdInfo {
  String firstName;
  String familyName;
  String address;
  String addressNormalized;
  String state;
  String idNumber;
  String documentNumber;
  String documentTypeId;
  String documentTypeCode;
  Date issueDate;
  String profession;
  String maritalStatus;
  String religion;
  String nationality;
  Date expirationDate;
  String idFrontScanPath;
  String idBackScanPath;
  String photo;
  Double idFrontScanAccuracy;
  Double idBackScanAccuracy;
  Double addressFraudDetection;
  Double birthDateFraudDetection;
  Double civilStatusFraudDetection;
  Double colorProfileChangeDetectedScore;
  Double colorProfileChangeDetectedScoreBack;
  Double documentNumberFraudDetection;
  Double documentPortraitGenuineScore;
  Double expiryDateFraudDetection;
  Double fullNameFraudDetection;
  Double genderFraudDetection;
  Double looksLikeScreenshotScore;
  Double looksLikeScreenshotScoreBack;
  Double personalNumberFraudDetection;
  Double professionFraudDetection;
  Double religionFraudDetection;
  Double stateFraudDetection;
}
