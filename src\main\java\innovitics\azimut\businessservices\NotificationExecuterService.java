package innovitics.azimut.businessservices;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.models.NotificationTemplate;
import innovitics.azimut.models.user.User;
import innovitics.azimut.services.user.UserService;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class NotificationExecuterService extends AbstractBusinessService<NotificationTemplate> {

  private @Autowired UserService userService;

  @Async("taskExecutor")
  public void sendTemplateToFirebase(NotificationTemplate template, String target) throws BusinessException {
    MyLogger.info("NotificationExecuterService: Executing task on thread: " + Thread.currentThread().getName());
    var users = StringUtility.stringsMatch(target, "testUsers") ? userService.findTestUsers()
        : (StringUtility.stringsMatch(target, "allUsers") ? userService.findAll()
            : userService.findByUserStep(target));
    for (User user : users) {
      if (user.getDeletedAt() == null) {
        var businessUser = userMapper.convertBasicUnitToBusinessUnit(user);
        this.messagingService.send(pushNotificationUtility, businessUser, template.getBody(),
            template.getBodyAr(), template.getTitle(), template.getTitleAr(), StringUtility.ENGLISH,
            template.getLink());
      }
    }
    MyLogger.info("NotificationExecuterService: Task execution complete");
  }

  @Async("taskExecutor")
  public void sendTemplateToFirebaseList(NotificationTemplate template, List<Long> target) throws BusinessException {
    MyLogger.info("NotificationExecuterService: Executing task on thread: " + Thread.currentThread().getName());
    var users = userService.findByIds(target);
    for (User user : users) {
      if (user.getDeletedAt() == null) {
        var businessUser = userMapper.convertBasicUnitToBusinessUnit(user);
        this.messagingService.send(pushNotificationUtility, businessUser, template.getBody(),
            template.getBodyAr(), template.getTitle(), template.getTitleAr(), StringUtility.ENGLISH,
            template.getLink());
      }
    }
    MyLogger.info("NotificationExecuterService: Task execution complete");
  }

  @Async("taskExecutor")
  public void sendTemplateToFirebaseUserIdsList(NotificationTemplate template, List<String> target)
      throws BusinessException {
    MyLogger.info("NotificationExecuterService: Executing task on thread: " + Thread.currentThread().getName());
    var users = userService.findByUserIds(target);
    for (User user : users) {
      if (user.getDeletedAt() == null) {
        var businessUser = userMapper.convertBasicUnitToBusinessUnit(user);
        this.messagingService.send(pushNotificationUtility, businessUser, template.getBody(),
            template.getBodyAr(), template.getTitle(), template.getTitleAr(), StringUtility.ENGLISH,
            template.getLink());
      }
    }
    MyLogger.info("NotificationExecuterService: Task execution complete");
  }

}
