package innovitics.azimut.utilities.datautilities;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.chrono.HijrahDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Locale;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import innovitics.azimut.utilities.logging.MyLogger;

public final class DateUtility {

  protected static final Logger logger = LoggerFactory.getLogger(DateUtility.class);

  public static String getCurrentNanoSecond() {
    // return String.valueOf(Instant.now().getEpochSecond());
    return String.valueOf(Math.abs(System.nanoTime()));
  }

  public static Date getCurrentDate() {
    return new Date();
  }

  public static String changeStringDateFormat(String inputDateString, String sourceFormatString,
      String destinationFormatString) {
    SimpleDateFormat sourceFormat = new SimpleDateFormat(sourceFormatString);
    SimpleDateFormat destinationFormat = new SimpleDateFormat(destinationFormatString);
    if (StringUtility.isStringPopulated(inputDateString)) {
      String output = "";
      try {
        Date inputDate = sourceFormat.parse(inputDateString);
        output = destinationFormat.format(inputDate);
      } catch (ParseException e) {
        MyLogger.logStackTrace(e);
        return null;
      }
      return output;
    } else
      return null;
  }

  public static String changeStringDateFormat(String inputDateString, SimpleDateFormat sourceFormat,
      SimpleDateFormat destinationFormat) {
    if (StringUtility.isStringPopulated(inputDateString)) {
      String output = "";
      try {
        Date inputDate = sourceFormat.parse(inputDateString);
        output = destinationFormat.format(inputDate);
      } catch (ParseException e) {
        MyLogger.logStackTrace(e);
        return null;
      }
      return output;
    } else
      return null;
  }

  public static boolean isSameDay(Date date1, Date date2) {
    LocalDate localDate1 = date1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    LocalDate localDate2 = date2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

    return localDate1.equals(localDate2);
  }

  public static Date getCurrentDateWithOutTimeStamp() {
    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
    Date dateWithoutTime = new Date();
    try {
      dateWithoutTime = simpleDateFormat.parse(simpleDateFormat.format(new Date()));
    } catch (ParseException e) {
      MyLogger.logStackTrace(e);
    }
    return dateWithoutTime;
  }

  public static Date getCurrentDateWithOutTimeStamp(String date) {
    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
    Date dateWithoutTime = new Date();
    try {
      dateWithoutTime = simpleDateFormat.parse(simpleDateFormat.format(DateFormat.getDateInstance().parse(date)));
    } catch (ParseException e) {
      MyLogger.logStackTrace(e);
    }
    return dateWithoutTime;
  }

  public static Integer getCurrentYear() {
    return ((new Date()).toInstant().atZone(ZoneId.systemDefault()).toLocalDate()).getYear();
  }

  public static Integer getCurrentMonth() {
    return ((new Date()).toInstant().atZone(ZoneId.systemDefault()).toLocalDate()).getMonthValue();
  }

  public static Integer getCurrentDay() {
    return ((new Date()).toInstant().atZone(ZoneId.systemDefault()).toLocalDate()).getDayOfMonth();
  }

  public static String getCurrentYearMonthDay() {
    return getCurrentYear().toString() + "-" + getCurrentMonth().toString() + "-" + getCurrentDay().toString();
  }

  public static String getCurrentDayMonthYear() {
    return getCurrentDay().toString() + "-" + getCurrentMonth().toString() + "-" + getCurrentYear().toString();
  }

  public static String getNextYearDayMonthYear() {
    var date = (new Date()).toInstant().plus(365, ChronoUnit.DAYS).atZone(ZoneId.systemDefault()).toLocalDate();
    return date.getDayOfMonth() + "-" + date.getMonthValue() + "-" + date.getYear();
  }

  public static String getCurrentYearMonth() {
    return getCurrentYear().toString() + "-" + getCurrentMonth().toString();
  }

  public static String getCurrentTimeStamp() {
    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");// dd/MM/yyyy
    Date now = new Date();
    String stringDate = simpleDateFormat.format(now);
    return stringDate;
  }

  public static long getCurrentTimeInMilliSeconds() {
    return new Date().getTime();
  }

  public static boolean isFromDateBeforeToDate(String fromDate, String toDate) {
    boolean result = true;
    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("dd-MM-yyyy");
    try {

      if (StringUtility.isStringPopulated(fromDate) && StringUtility.isStringPopulated(toDate)) {
        result = simpleDateFormat.parse(fromDate).before(simpleDateFormat.parse(toDate));
      }

    } catch (ParseException e) {
      logger.info("Could not parse the date string");
      MyLogger.logStackTrace(e);
    }
    logger.info("isFromDateBeforeToDate??:::" + result);
    return result;
  }

  public static Date changeStringDateToDate(String date) {
    if (StringUtility.isStringPopulated(date)) {
      SimpleDateFormat simpleDateFormat = new SimpleDateFormat("dd-MM-yyyy");
      try {
        return simpleDateFormat.parse(date);
      } catch (ParseException e) {
        logger.info("Could not parse the date string");
        MyLogger.logStackTrace(e);
      }
    }
    return null;
  }

  public static boolean areDatesDifferent(Date date1, Date date2) {
    logger.info("Date1:::" + date1);
    logger.info("Date2:::" + date2);
    boolean result = false;
    if (date1 != null && date2 != null) {
      if (date1.compareTo(date2) > 0) {
        result = true;
      } else if (date1.compareTo(date2) < 0) {
        result = true;
      } else if (date1.compareTo(date2) == 0) {
        result = false;
      }
    }

    logger.info("Are dates different?::" + result);
    return result;
  }

  public static String changeDatetoStringDate(Date date, String destinationFormat) {
    SimpleDateFormat simpleDateFormat = new SimpleDateFormat(destinationFormat);
    return simpleDateFormat.format(date);
  }

  public static String getArabicDayName() {
    Locale locale = new Locale(StringUtility.ARABIC);
    DateTimeFormatter dayOfWeekFormatter = DateTimeFormatter.ofPattern("EEEE", locale);
    HijrahDate today = HijrahDate.now(ZoneId.of("Asia/Riyadh"));
    return today.format(dayOfWeekFormatter);
  }

  public static String changingMilliSecondTimeStampToMonthDayYear(String value) {

    GregorianCalendar calendar = new GregorianCalendar();
    calendar.setTimeInMillis(Long.valueOf(value));
    int month = calendar.get(Calendar.MONTH) + 1;
    int year = calendar.get(Calendar.YEAR);
    int day = calendar.get(Calendar.DAY_OF_MONTH) + 1;
    return month + "/" + day + "/" + year;
  }

  public static String getDayMonthYear(Date date) {
    return getDay(date).toString() + "-" + getMonth(date).toString() + "-" + getYear(date).toString();
  }

  public static String getDateForGateId(String dateString) {
    return DateUtility.changeStringDateFormat(dateString,
        new SimpleDateFormat("yyyy/MM/dd"), new SimpleDateFormat("dd-MM-yyyy"));
  }

  public static Integer getYear(Date date) {
    return ((date).toInstant().atZone(ZoneId.systemDefault()).toLocalDate()).getYear();
  }

  public static Integer getMonth(Date date) {
    return ((date).toInstant().atZone(ZoneId.systemDefault()).toLocalDate()).getMonthValue();
  }

  public static Integer getDay(Date date) {
    return ((date).toInstant().atZone(ZoneId.systemDefault()).toLocalDate()).getDayOfMonth();
  }

  public static Timestamp getMinutesBefore(String value) {
    Timestamp current = new Timestamp(System.currentTimeMillis());
    Calendar cal = Calendar.getInstance();
    cal.setTimeInMillis(current.getTime());
    int valueExpiryInMinutes = Integer.valueOf(value);
    cal.add(Calendar.MINUTE, -valueExpiryInMinutes);
    Timestamp currentMinusMinutesInValue = new Timestamp(cal.getTime().getTime());
    return currentMinusMinutesInValue;
  }
}
