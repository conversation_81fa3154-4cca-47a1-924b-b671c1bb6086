package innovitics.azimut.controllers.digitalregistry;

import java.io.IOException;
import java.util.Date;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.businessservices.BusinessUserChildService;
import innovitics.azimut.businessservices.BusinessUserService;
import innovitics.azimut.controllers.BaseController;
import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.controllers.GenericResponseHandler;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.models.digitalregistry.DigitalRegistry;
import innovitics.azimut.models.digitalregistry.DigitalRegistryAction;
import innovitics.azimut.services.digitalregistry.DigitalRegistryService;

@RestController
@RequestMapping(value = "/api/digital-registry", produces = "application/json")
public class DigitalRegistryController extends BaseController {
  private static final String API_KEY_HEADER_NAME = "X-API-KEY";
  private @Autowired DigitalRegistryService digitalRegistryService;
  private @Autowired GenericResponseHandler<BusinessUser> businessUserHandler;
  private @Autowired BusinessUserChildService businessUserChildService;
  private @Autowired BusinessUserService businessUserService;

  @GetMapping()
  Page<DigitalRegistry> getDigitalRegistry(Pageable pageable, @RequestParam Optional<Long> userId,
      @RequestParam Optional<String> nationalId,
      @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) Optional<Date> beforeDate,
      @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) Optional<Date> afterDate,
      @RequestParam Optional<DigitalRegistryAction> action,
      @RequestHeader(name = API_KEY_HEADER_NAME, required = true) Optional<String> authToken)
      throws BusinessException {
    this.digitalRegistryService.validateApiKey(authToken);
    return this.digitalRegistryService.getDigitalRegistry(pageable, userId, nationalId, beforeDate, afterDate,
        action);
  }

  @GetMapping(value = "/signed-contracts/{userId}", produces = MediaType.APPLICATION_PDF_VALUE)
  @ResponseBody
  byte[] getSignedContract(@PathVariable Long userId,
      @RequestHeader(name = API_KEY_HEADER_NAME, required = true) Optional<String> authToken)
      throws BusinessException, IOException {
    this.digitalRegistryService.validateApiKey(authToken);
    return this.businessUserService.getSignedContract(userId);
  }

  @GetMapping(value = "/digitally-signed-contracts/{userId}", produces = MediaType.APPLICATION_PDF_VALUE)
  @ResponseBody
  byte[] getDigitallySignedContract(@PathVariable Long userId,
      @RequestHeader(name = API_KEY_HEADER_NAME, required = true) Optional<String> authToken)
      throws BusinessException, IOException {
    this.digitalRegistryService.validateApiKey(authToken);
    return this.businessUserService.getDigitallySignedContract(userId);
  }

  @GetMapping(value = "/get-ocr-details/{userId}")
  @ResponseBody
  ResponseEntity<BaseGenericResponse<BusinessUser>> getUserOCRDetails(@PathVariable Long userId,
      @RequestHeader(name = API_KEY_HEADER_NAME, required = true) Optional<String> authToken)
      throws BusinessException, IOException {
    this.digitalRegistryService.validateApiKey(authToken);
    return businessUserHandler.generateBaseGenericResponse(BusinessUser.class,
        this.businessUserChildService.beautifyUser(this.businessUserChildService
            .getClientOCRDetailsAndImages(null, userId, "en")),
        null, null);
  }
}
