package innovitics.azimut.businessservices;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.itextpdf.text.pdf.PdfDocument;
import com.itextpdf.text.pdf.PdfReader;
import com.lowagie.text.pdf.parser.PdfTextExtractor;

import innovitics.azimut.businessmodels.negativelist.BusinessNegativeList;
import innovitics.azimut.businessmodels.negativelist.BusinessNegativeListMatch;
import innovitics.azimut.businessmodels.negativelist.BusinessNegativeListScreeningResult;
import innovitics.azimut.controllers.negativelist.DTOs.ScreenUsersDto;
import innovitics.azimut.controllers.negativelist.DTOs.SearchNegativeListDto;
import innovitics.azimut.controllers.negativelist.DTOs.UploadSanctionsListDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.models.NegativeList;
import innovitics.azimut.models.user.User;
import innovitics.azimut.repositories.NegativeListRepository;
import innovitics.azimut.repositories.user.UserRepository;
import innovitics.azimut.utilities.datautilities.DateUtility;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class BusinessNegativeListService extends AbstractBusinessService {

  @Autowired
  private NegativeListRepository negativeListRepository;

  @Autowired
  private UserRepository userRepository;

  public BusinessNegativeList uploadSanctionsList(UploadSanctionsListDto uploadDto)
      throws BusinessException, IntegrationException {
    try {
      MultipartFile file = uploadDto.getFile();
      String fileName = file.getOriginalFilename();

      if (uploadDto.getReplaceExisting()) {
        // Delete existing entries for this list source
        List<NegativeList> existingEntries = negativeListRepository
            .findByListSourceAndIsActive(uploadDto.getListSource(), true);
        for (NegativeList entry : existingEntries) {
          entry.setIsActive(false);
          entry.setUpdatedAt(DateUtility.getCurrentDate());
        }
        negativeListRepository.saveAll(existingEntries);
      }

      List<NegativeList> entities = new ArrayList<>();

      if (fileName != null && fileName.toLowerCase().endsWith(".xlsx")) {
        entities = parseExcelFile(file.getInputStream(), uploadDto);
      } else if (fileName != null && fileName.toLowerCase().endsWith(".pdf")) {
        entities = parsePdfFile(file.getInputStream(), uploadDto);
      } else {
        throw new BusinessException("Unsupported file format. Only Excel (.xlsx) and PDF files are supported.");
      }

      negativeListRepository.saveAll(entities);

      BusinessNegativeList result = new BusinessNegativeList();
      result.setListSource(uploadDto.getListSource());
      result.setListType(uploadDto.getListType());

      MyLogger.info("Successfully uploaded " + entities.size() + " entries for list: " + uploadDto.getListSource());

      return result;

    } catch (Exception e) {
      MyLogger.logStackTrace(e);
      throw new BusinessException("Failed to upload sanctions list: " + e.getMessage());
    }
  }

  private List<NegativeList> parseExcelFile(InputStream inputStream, UploadSanctionsListDto uploadDto)
      throws Exception {
    List<NegativeList> entities = new ArrayList<>();
    Workbook workbook = new XSSFWorkbook(inputStream);
    Sheet sheet = workbook.getSheetAt(0);

    for (Row row : sheet) {
      if (row.getRowNum() == 0)
        continue; // Skip header row

      NegativeList entity = new NegativeList();
      entity.setFullName(getCellValue(row.getCell(0)));
      entity.setFirstName(getCellValue(row.getCell(1)));
      entity.setLastName(getCellValue(row.getCell(2)));
      entity.setAlias(getCellValue(row.getCell(3)));
      entity.setNationality(getCellValue(row.getCell(4)));
      entity.setDateOfBirth(getCellValue(row.getCell(5)));
      entity.setPlaceOfBirth(getCellValue(row.getCell(6)));
      entity.setPassportNumber(getCellValue(row.getCell(7)));
      entity.setNationalId(getCellValue(row.getCell(8)));
      entity.setReferenceNumber(getCellValue(row.getCell(9)));
      entity.setAdditionalInfo(getCellValue(row.getCell(10)));

      entity.setListSource(uploadDto.getListSource());
      entity.setListType(uploadDto.getListType());
      entity.setIsActive(true);
      entity.setLastUpdated(DateUtility.getCurrentDate());
      entity.setCreatedAt(DateUtility.getCurrentDate());
      entity.setUpdatedAt(DateUtility.getCurrentDate());

      // Skip empty rows
      if (entity.getFullName() != null && !entity.getFullName().trim().isEmpty()) {
        entities.add(entity);
      }
    }

    workbook.close();
    return entities;
  }

  private List<NegativeList> parsePdfFile(InputStream inputStream, UploadSanctionsListDto uploadDto)
      throws Exception {
    List<NegativeList> entities = new ArrayList<>();
    PdfReader reader = new PdfReader(inputStream);
    PdfDocument pdfDoc = new PdfDocument(reader);

    for (int i = 1; i <= pdfDoc.getNumberOfPages(); i++) {
      String text = PdfTextExtractor.getTextFromPage(pdfDoc.getPage(i));
      String[] lines = text.split("\n");

      for (String line : lines) {
        if (line.trim().isEmpty())
          continue;

        // Parse line based on common PDF formats (adjust as needed)
        String[] parts = line.split("\\|"); // Assuming pipe-separated values
        if (parts.length >= 4) {
          NegativeList entity = new NegativeList();
          entity.setFullName(parts[0].trim());
          entity.setAlias(parts.length > 1 ? parts[1].trim() : "");
          entity.setNationality(parts.length > 2 ? parts[2].trim() : "");
          entity.setDateOfBirth(parts.length > 3 ? parts[3].trim() : "");
          entity.setAdditionalInfo(parts.length > 4 ? parts[4].trim() : "");

          entity.setListSource(uploadDto.getListSource());
          entity.setListType(uploadDto.getListType());
          entity.setIsActive(true);
          entity.setLastUpdated(DateUtility.getCurrentDate());
          entity.setCreatedAt(DateUtility.getCurrentDate());
          entity.setUpdatedAt(DateUtility.getCurrentDate());

          entities.add(entity);
        }
      }
    }

    pdfDoc.close();
    reader.close();
    return entities;
  }

  public List<BusinessNegativeListScreeningResult> screenUsers(ScreenUsersDto screenDto)
      throws BusinessException, IntegrationException {
    try {
      List<BusinessNegativeListScreeningResult> results = new ArrayList<>();
      MultipartFile file = screenDto.getFile();

      Workbook workbook = new XSSFWorkbook(file.getInputStream());
      Sheet sheet = workbook.getSheetAt(0);

      for (Row row : sheet) {
        if (row.getRowNum() == 0)
          continue; // Skip header

        String userName = getCellValue(row.getCell(0)); // Assuming first column is name
        String passportNumber = getCellValue(row.getCell(1));
        String nationalId = getCellValue(row.getCell(2));
        String nationality = getCellValue(row.getCell(3));
        String dateOfBirth = getCellValue(row.getCell(4));

        if (userName != null && !userName.trim().isEmpty()) {
          BusinessNegativeListScreeningResult result = performScreening(
              userName, passportNumber, nationalId, nationality, dateOfBirth, screenDto);
          results.add(result);
        }
      }

      workbook.close();
      return results;

    } catch (Exception e) {
      MyLogger.logStackTrace(e);
      throw new BusinessException("Failed to screen users: " + e.getMessage());
    }
  }

  private BusinessNegativeListScreeningResult performScreening(String userName, String passportNumber,
      String nationalId, String nationality, String dateOfBirth, ScreenUsersDto screenDto) {

    BusinessNegativeListScreeningResult result = new BusinessNegativeListScreeningResult();
    result.setUserName(userName);
    result.setIsMatch(false);
    result.setMatchCount(0);
    result.setMatches(new ArrayList<>());
    result.setScreeningStatus("CLEAR");
    result.setRiskLevel("LOW");

    List<BusinessNegativeListMatch> allMatches = new ArrayList<>();

    // 1. Exact name match
    List<NegativeList> exactMatches = negativeListRepository.findByFullNameContainingIgnoreCase(userName);
    for (NegativeList match : exactMatches) {
      if (!screenDto.getIncludeInactiveEntries() && !match.getIsActive())
        continue;

      BusinessNegativeListMatch businessMatch = createBusinessMatch(match, "EXACT", 1.0, "FULL_NAME");
      allMatches.add(businessMatch);
    }

    // 2. Fuzzy name search
    List<NegativeList> fuzzyMatches = negativeListRepository.findByNameFuzzySearch(userName);
    for (NegativeList match : fuzzyMatches) {
      if (!screenDto.getIncludeInactiveEntries() && !match.getIsActive())
        continue;

      double confidence = calculateNameSimilarity(userName, match.getFullName());
      if (confidence >= screenDto.getMinimumConfidenceScore()) {
        BusinessNegativeListMatch businessMatch = createBusinessMatch(match, "FUZZY", confidence, "FULL_NAME");
        allMatches.add(businessMatch);
      }
    }

    // 3. Document-based matching
    if (passportNumber != null && !passportNumber.trim().isEmpty() ||
        nationalId != null && !nationalId.trim().isEmpty()) {
      List<NegativeList> docMatches = negativeListRepository
          .findByIdentificationDocuments(passportNumber, nationalId);
      for (NegativeList match : docMatches) {
        if (!screenDto.getIncludeInactiveEntries() && !match.getIsActive())
          continue;

        String matchedField = passportNumber != null && passportNumber.equals(match.getPassportNumber())
            ? "PASSPORT"
            : "NATIONAL_ID";
        BusinessNegativeListMatch businessMatch = createBusinessMatch(match, "EXACT", 1.0, matchedField);
        allMatches.add(businessMatch);
      }
    }

    // 4. Nationality and DOB matching
    if (nationality != null && dateOfBirth != null) {
      List<NegativeList> bioMatches = negativeListRepository
          .findByNationalityAndDateOfBirth(nationality, dateOfBirth);
      for (NegativeList match : bioMatches) {
        if (!screenDto.getIncludeInactiveEntries() && !match.getIsActive())
          continue;

        BusinessNegativeListMatch businessMatch = createBusinessMatch(match, "PARTIAL", 0.8, "NATIONALITY_DOB");
        allMatches.add(businessMatch);
      }
    }

    // Remove duplicates and set results
    allMatches = allMatches.stream()
        .collect(Collectors.toMap(
            m -> m.getNegativeListId(),
            m -> m,
            (existing, replacement) -> existing.getConfidenceScore() > replacement.getConfidenceScore()
                ? existing
                : replacement))
        .values()
        .stream()
        .collect(Collectors.toList());

    result.setMatches(allMatches);
    result.setMatchCount(allMatches.size());
    result.setIsMatch(!allMatches.isEmpty());

    if (!allMatches.isEmpty()) {
      double maxConfidence = allMatches.stream()
          .mapToDouble(BusinessNegativeListMatch::getConfidenceScore)
          .max().orElse(0.0);

      result.setConfidenceScore(maxConfidence);

      if (maxConfidence >= 0.95) {
        result.setScreeningStatus("BLOCKED");
        result.setRiskLevel("CRITICAL");
      } else if (maxConfidence >= 0.8) {
        result.setScreeningStatus("POTENTIAL_MATCH");
        result.setRiskLevel("HIGH");
      } else {
        result.setScreeningStatus("POTENTIAL_MATCH");
        result.setRiskLevel("MEDIUM");
      }
    }

    return result;
  }

  private BusinessNegativeListMatch createBusinessMatch(NegativeList negativeList, String matchType,
      double confidence, String matchedField) {
    BusinessNegativeListMatch match = new BusinessNegativeListMatch();
    match.setNegativeListId(negativeList.getId());
    match.setMatchedName(negativeList.getFullName());
    match.setListSource(negativeList.getListSource());
    match.setMatchType(matchType);
    match.setConfidenceScore(confidence);
    match.setMatchedField(matchedField);
    match.setAdditionalInfo(negativeList.getAdditionalInfo());
    return match;
  }

  private double calculateNameSimilarity(String name1, String name2) {
    if (name1 == null || name2 == null)
      return 0.0;

    name1 = name1.toLowerCase().trim();
    name2 = name2.toLowerCase().trim();

    if (name1.equals(name2))
      return 1.0;

    // Simple Levenshtein distance-based similarity
    int maxLength = Math.max(name1.length(), name2.length());
    if (maxLength == 0)
      return 1.0;

    int distance = levenshteinDistance(name1, name2);
    return 1.0 - (double) distance / maxLength;
  }

  private int levenshteinDistance(String s1, String s2) {
    int[][] dp = new int[s1.length() + 1][s2.length() + 1];

    for (int i = 0; i <= s1.length(); i++) {
      for (int j = 0; j <= s2.length(); j++) {
        if (i == 0) {
          dp[i][j] = j;
        } else if (j == 0) {
          dp[i][j] = i;
        } else {
          dp[i][j] = Math.min(
              dp[i - 1][j - 1] + (s1.charAt(i - 1) == s2.charAt(j - 1) ? 0 : 1),
              Math.min(dp[i - 1][j] + 1, dp[i][j - 1] + 1));
        }
      }
    }

    return dp[s1.length()][s2.length()];
  }

  public List<BusinessNegativeList> searchNegativeList(SearchNegativeListDto searchDto)
      throws BusinessException {
    try {
      List<NegativeList> results = new ArrayList<>();

      if (searchDto.getSearchTerm() != null && !searchDto.getSearchTerm().trim().isEmpty()) {
        if ("EXACT".equals(searchDto.getSearchType())) {
          results = negativeListRepository.findByFullNameContainingIgnoreCase(searchDto.getSearchTerm());
        } else {
          results = negativeListRepository.findByNameFuzzySearch(searchDto.getSearchTerm());
        }
      } else if (searchDto.getPassportNumber() != null || searchDto.getNationalId() != null) {
        results = negativeListRepository.findByIdentificationDocuments(
            searchDto.getPassportNumber(), searchDto.getNationalId());
      } else if (searchDto.getNationality() != null && searchDto.getDateOfBirth() != null) {
        results = negativeListRepository.findByNationalityAndDateOfBirth(
            searchDto.getNationality(), searchDto.getDateOfBirth());
      } else if (searchDto.getListSource() != null) {
        results = negativeListRepository.findByListSourceAndIsActive(
            searchDto.getListSource(), searchDto.getIsActive());
      } else {
        results = negativeListRepository.findByIsActive(searchDto.getIsActive());
      }

      return results.stream()
          .map(this::convertToBusinessModel)
          .collect(Collectors.toList());

    } catch (Exception e) {
      MyLogger.logStackTrace(e);
      throw new BusinessException("Failed to search negative list: " + e.getMessage());
    }
  }

  private BusinessNegativeList convertToBusinessModel(NegativeList entity) {
    BusinessNegativeList business = new BusinessNegativeList();
    business.setId(entity.getId());
    business.setFullName(entity.getFullName());
    business.setFirstName(entity.getFirstName());
    business.setLastName(entity.getLastName());
    business.setAlias(entity.getAlias());
    business.setNationality(entity.getNationality());
    business.setDateOfBirth(entity.getDateOfBirth());
    business.setPlaceOfBirth(entity.getPlaceOfBirth());
    business.setPassportNumber(entity.getPassportNumber());
    business.setNationalId(entity.getNationalId());
    business.setListSource(entity.getListSource());
    business.setListType(entity.getListType());
    business.setReferenceNumber(entity.getReferenceNumber());
    business.setAdditionalInfo(entity.getAdditionalInfo());
    business.setIsActive(entity.getIsActive());
    business.setLastUpdated(entity.getLastUpdated());
    business.setCreatedAt(entity.getCreatedAt());
    business.setUpdatedAt(entity.getUpdatedAt());
    return business;
  }

  public List<BusinessNegativeListScreeningResult> flagExistingUsers()
      throws BusinessException, IntegrationException {
    try {
      List<BusinessNegativeListScreeningResult> flaggedUsers = new ArrayList<>();
      List<User> allUsers = userRepository.findAll();

      for (User user : allUsers) {
        // Screen each user against the negative list
        BusinessNegativeListScreeningResult result = performScreening(
            user.getNickName() != null ? user.getNickName() : (user.getFirstName() + " " + user.getLastName()).trim(),
            null, // passport number not available in User entity
            user.getUserId(), // using userId as national ID
            null, // nationality not available in User entity
            null, // date of birth not available in User entity
            createDefaultScreeningDto());

        if (result.getIsMatch()) {
          result.setUserName(user.getUserPhone()); // Use phone as identifier
          flaggedUsers.add(result);

          // Log the flagged user
          MyLogger.info("User flagged in negative list screening: " + user.getUserPhone() +
              " - Match count: " + result.getMatchCount() +
              " - Risk level: " + result.getRiskLevel());
        }
      }

      MyLogger.info("Completed screening of " + allUsers.size() + " users. " +
          flaggedUsers.size() + " users flagged.");

      return flaggedUsers;

    } catch (Exception e) {
      MyLogger.logStackTrace(e);
      throw new BusinessException("Failed to flag existing users: " + e.getMessage());
    }
  }

  private ScreenUsersDto createDefaultScreeningDto() {
    ScreenUsersDto dto = new ScreenUsersDto();
    dto.setIncludeInactiveEntries(false);
    dto.setMinimumConfidenceScore(0.8); // Higher threshold for existing users
    dto.setScreeningMode("COMPREHENSIVE");
    return dto;
  }

  private String getCellValue(Cell cell) {
    if (cell == null)
      return "";
    switch (cell.getCellType()) {
      case STRING:
        return cell.getStringCellValue().trim();
      case NUMERIC:
        return String.valueOf((long) cell.getNumericCellValue());
      default:
        return "";
    }
  }
}
