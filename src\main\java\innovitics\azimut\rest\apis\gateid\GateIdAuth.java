package innovitics.azimut.rest.apis.gateid;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;

import innovitics.azimut.rest.RestBaseApi;
import innovitics.azimut.rest.entities.gateid.GateIdAuthOutput;

@Service
public class GateIdAuth extends RestBaseApi<Void, GateIdAuthOutput> {

  private String token;

  public String getToken() {
    return token;
  }

  public void setToken(String token) {
    this.token = token;
  }

  @Override
  protected String generateURL(String params) {
    return this.configProperties.getGateIdUrl() + "/users/login";
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.POST;
  }

  @Override
  public HttpHeaders generateHeaders() {
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
    return headers;
  }

  @Override
  protected HttpEntity<?> generateRequestFromInput(Void input) {
    LinkedMultiValueMap<String, Object> map = new LinkedMultiValueMap<>();

    map.add("username", configProperties.getGateIdUsername());
    map.add("password", configProperties.getGateIdPassword());
    return new HttpEntity<>(map, this.generateHeaders());
  };

}
