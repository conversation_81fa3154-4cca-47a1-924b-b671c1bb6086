package innovitics.azimut.models;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import innovitics.azimut.utilities.CustomJsonRootName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Entity
@Table(name = "negative_list")
@CustomJsonRootName(plural = "negativeListEntries", singular = "negativeListEntry")
@Getter
@Setter
@ToString
public class NegativeList extends DbBaseEntity {

  @Column(name = "full_name", nullable = false)
  private String fullName;

  @Column(name = "first_name")
  private String firstName;

  @Column(name = "last_name")
  private String lastName;

  @Column(name = "alias", columnDefinition = "TEXT")
  private String alias;

  @Column(name = "nationality")
  private String nationality;

  @Column(name = "date_of_birth")
  private String dateOfBirth;

  @Column(name = "place_of_birth")
  private String placeOfBirth;

  @Column(name = "passport_number")
  private String passportNumber;

  @Column(name = "national_id")
  private String nationalId;

  @Column(name = "list_source", nullable = false)
  private String listSource;

  @Column(name = "list_type")
  private String listType;

  @Column(name = "reference_number")
  private String referenceNumber;

  @Column(name = "additional_info", columnDefinition = "TEXT")
  private String additionalInfo;

  @Column(name = "is_active")
  private Boolean isActive = true;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  @Column(name = "last_updated")
  private Date lastUpdated;
}
