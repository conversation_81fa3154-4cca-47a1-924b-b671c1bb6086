package innovitics.azimut.services.teacomputer;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.transaction.Transactional;
import javax.transaction.Transactional.TxType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import innovitics.azimut.businessmodels.user.BusinessClientBankAccountDetails;
import innovitics.azimut.models.teacomputers.Bank;
import innovitics.azimut.models.teacomputers.Branch;
import innovitics.azimut.models.teacomputers.City;
import innovitics.azimut.models.teacomputers.ClientBankAccount;
import innovitics.azimut.models.teacomputers.Country;
import innovitics.azimut.models.teacomputers.Currency;
import innovitics.azimut.models.teacomputers.Nationality;
import innovitics.azimut.repositories.teacomputers.BankDynamicRepository;
import innovitics.azimut.repositories.teacomputers.BranchDynamicRepository;
import innovitics.azimut.repositories.teacomputers.CityDynamicRepository;
import innovitics.azimut.repositories.teacomputers.ClientBankAccountDynamicRepository;
import innovitics.azimut.repositories.teacomputers.CountryDynamicRepository;
import innovitics.azimut.repositories.teacomputers.CurrencyDynamicRepository;
import innovitics.azimut.repositories.teacomputers.NationalityDynamicRepository;
import innovitics.azimut.services.AbstractService;
import innovitics.azimut.utilities.datautilities.BooleanUtility;
import innovitics.azimut.utilities.dbutilities.SearchCriteria;
import innovitics.azimut.utilities.dbutilities.SearchOperation;
import innovitics.azimut.utilities.dbutilities.specifications.child.BankChildSpecification;
import innovitics.azimut.utilities.dbutilities.specifications.child.BranchChildSpecification;
import innovitics.azimut.utilities.dbutilities.specifications.child.CityChildSpecification;
import innovitics.azimut.utilities.dbutilities.specifications.child.ClientBankAccountEntitySpecification;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class TeaComputerService extends AbstractService<Country, String> {

  @Autowired
  CountryDynamicRepository countryDynamicRepository;
  @Autowired
  CityDynamicRepository cityDynamicRepository;
  @Autowired
  NationalityDynamicRepository nationalityDynamicRepository;
  @Autowired
  CityChildSpecification cityChildSpecification;
  @Autowired
  BankChildSpecification bankChildSpecification;
  @Autowired
  BranchChildSpecification branchChildSpecification;
  @Autowired
  BankDynamicRepository bankDynamicRepository;
  @Autowired
  BranchDynamicRepository branchDynamicRepository;
  @Autowired
  CurrencyDynamicRepository currencyDynamicRepository;
  @Autowired
  ClientBankAccountDynamicRepository clientBankAccountDynamicRepository;
  @Autowired
  ClientBankAccountEntitySpecification clientBankAccountEntitySpecification;

  public List<Country> saveAllCountries(List<Country> countries) {
    return this.countryDynamicRepository.saveAll(countries);
  }

  public List<City> saveAllCities(List<City> cities) {
    return this.cityDynamicRepository.saveAll(cities);
  }

  public List<Nationality> saveAllNationalities(List<Nationality> nationalities) {
    return this.nationalityDynamicRepository.saveAll(nationalities);
  }

  public List<Bank> saveAllBanks(List<Bank> banks) {
    return this.bankDynamicRepository.saveAll(banks);
  }

  public List<Branch> saveAllBranches(List<Branch> branches) {
    return this.branchDynamicRepository.saveAll(branches);
  }

  public List<Currency> saveAllCurrencies(List<Currency> currencies) {
    return this.currencyDynamicRepository.saveAll(currencies);
  }

  public List<Country> getAllCountries() {
    return this.countryDynamicRepository.findAll(Sort.by("countryId"));
  }

  public List<City> getAllCitiesByCountryId(Long countryId) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("countryId", countryId, SearchOperation.EQUAL, null));
    return this.cityDynamicRepository.findAll(cityChildSpecification.findByCriteria(searchCriteriaList),
        Sort.by("cityId"));
  }

  public List<Nationality> getAllNationalities() {
    return this.nationalityDynamicRepository.findAll(Sort.by("systemNationalityCode"));
  }

  public List<Bank> getAllBanks(String bankType) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("bankType", bankType, SearchOperation.EQUAL, null));
    return this.bankDynamicRepository.findAll(bankChildSpecification.findByCriteria(searchCriteriaList),
        Sort.by("bankId"));
  }

  public Bank getBank(Long id) {
    return this.bankDynamicRepository.findByBankId(id);
  }

  public Branch getBranch(Long id) {
    return this.branchDynamicRepository.findByBranchId(id);
  }

  public Branch getBranchById(Long id) {
    return this.branchDynamicRepository.findById(id).orElse(null);
  }

  public Currency getCurrency(Long id) {
    return this.currencyDynamicRepository.findByCurrencyId(id);
  }

  public List<Branch> getAllBranches() {
    return this.branchDynamicRepository.findAll();
  }

  public List<Branch> getAllBranchesByBankId(Long bankId) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("bankId", bankId, SearchOperation.EQUAL, null));
    return this.branchDynamicRepository.findAll(branchChildSpecification.findByCriteria(searchCriteriaList),
        Sort.by("branchId"));
  }

  public List<Currency> getAllCurrencies() {
    return this.currencyDynamicRepository.findAll(Sort.by("currencyId"));
  }

  public void deleteAllCountries() {
    this.countryDynamicRepository.deleteAllInBatch();
  }

  public void deleteAllCities() {
    this.cityDynamicRepository.deleteAllInBatch();
  }

  public void deleteAllNationalities() {
    this.nationalityDynamicRepository.deleteAllInBatch();
  }

  public void deleteAllBanks() {
    this.bankDynamicRepository.deleteAllInBatch();
  }

  public void deleteAllBranches() {
    this.branchDynamicRepository.deleteAllInBatch();
  }

  public void deleteAllCurrencies() {
    this.currencyDynamicRepository.deleteAllInBatch();
  }

  public List<ClientBankAccount> getUserClientBankAccounts(Long userId, Long accountId) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("userId", userId, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("id", accountId, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));
    return this.clientBankAccountDynamicRepository
        .findAll(this.clientBankAccountEntitySpecification.findByCriteria(searchCriteriaList));
  }

  public List<ClientBankAccount> getUserClientBankAccounts(Long userId) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("userId", userId, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));
    return this.clientBankAccountDynamicRepository
        .findAll(this.clientBankAccountEntitySpecification.findByCriteria(searchCriteriaList));
  }

  public List<ClientBankAccount> getUserClientBankAccounts(Long userId, Boolean kycOnly) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("userId", userId, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));

    if (BooleanUtility.isTrue(kycOnly))
      searchCriteriaList.add(new SearchCriteria("kycOnly", true, SearchOperation.EQUAL, null));

    return this.clientBankAccountDynamicRepository
        .findAll(this.clientBankAccountEntitySpecification.findByCriteria(searchCriteriaList));
  }

  public void saveClientBankAccountsTemporarily(List<ClientBankAccount> clientBankAccounts) {
    this.clientBankAccountDynamicRepository.saveAll(clientBankAccounts);
  }

  public ClientBankAccount removeClientBankAccount(Long id) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("id", id, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));

    ClientBankAccount clientBankAccount = new ClientBankAccount();
    clientBankAccount = this.clientBankAccountDynamicRepository
        .findOne(this.clientBankAccountEntitySpecification.findByCriteria(searchCriteriaList)).get();

    clientBankAccount.setDeletedAt(new Date());

    this.clientBankAccountDynamicRepository.save(clientBankAccount);
    return clientBankAccount;

  }

  public void deleteClientBankAccounts(Long userId) {
    this.clientBankAccountDynamicRepository.softDeleteClientBankAccounts(userId);
  }

  public void deleteKycClientBankAccounts(Long userId) {
    this.clientBankAccountDynamicRepository.softDeleteKycClientBankAccounts(userId);
  }

  @Transactional(value = TxType.REQUIRES_NEW)
  public void deleteKycClientBankAccounts(BusinessClientBankAccountDetails[] oldClientBankAccounts) {
    MyLogger.info("Deleting the old client bank accounts");
    StopWatch timer = new StopWatch();
    String sql = "update client_bank_accounts SET deleted_at=sysdate()  WHERE id=:id";

    List<MapSqlParameterSource> params = new ArrayList<MapSqlParameterSource>();

    for (BusinessClientBankAccountDetails oldAccount : oldClientBankAccounts) {
      MapSqlParameterSource source = new MapSqlParameterSource();
      MyLogger.info("ID::::::::::" + oldAccount.getAccountId());
      source.addValue("id", oldAccount.getAccountId());
      params.add(source);
    }

    timer.start();
    namedJdbcTemplate.batchUpdate(sql, params.toArray(MapSqlParameterSource[]::new));

    timer.stop();
    MyLogger.info("batchUpdate -> Total time in seconds: " + timer.getTotalTimeSeconds());
  }
}
