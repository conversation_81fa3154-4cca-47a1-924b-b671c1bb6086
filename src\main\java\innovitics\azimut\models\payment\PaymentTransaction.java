package innovitics.azimut.models.payment;

import java.util.Date;
import java.util.Map;

import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import innovitics.azimut.models.DbBaseEntity;
import innovitics.azimut.models.user.User;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Entity
@Table(name = "payment_transactions")
@Setter
@Getter
@ToString
public class PaymentTransaction extends DbBaseEntity {

  @ManyToOne
  @NotFound(action = NotFoundAction.IGNORE)
  @JoinColumn(name = "app_user_id", foreignKey = @javax.persistence.ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
  private User user;

  private Double transactionAmount;

  private String status;

  private String referenceTransactionId;

  private Long paymentGateway;

  private String paymentMethod;

  private Date createdAt;

  private Date updatedAt;

  private Date deletedAt;

  private String message;

  private String parameterNames;

  private String parameterValues;

  private Integer action;

  private Long currencyId;

  private String initiator;

  private Integer teacomputerStatus;

  private String teacomputerMessage;

  @Transient
  protected Map<String, String> keyValueMap;

}
