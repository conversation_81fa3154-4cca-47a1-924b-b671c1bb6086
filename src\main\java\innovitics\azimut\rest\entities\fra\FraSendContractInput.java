package innovitics.azimut.rest.entities.fra;

import com.fasterxml.jackson.annotation.JsonProperty;

import innovitics.azimut.rest.entities.enroll.EnrollLocationInfo;
import lombok.Data;

@Data
public class FraSendContractInput {

  private String companyName;
  @JsonProperty("CustomerId")
  private String customerId;
  @JsonProperty("CustomerNationalId")
  private String customerNationalId;
  @JsonProperty("CustomerName")
  private String customerName;
  private String contractId;
  private String contractStatus;
  private String contractBase64;
  @JsonProperty("Location")
  private EnrollLocationInfo location;

  @Override
  public String toString() {
    return "FraSendContractInput{" +
        "companyName='" + companyName + '\'' +
        ", customerId='" + customerId + '\'' +
        ", customerNationalId='" + customerNationalId + '\'' +
        ", customerName='" + customerName + '\'' +
        ", contractId='" + contractId + '\'' +
        ", contractStatus='" + contractStatus + '\'' +
        ", contractBase64='...'" +
        ", location=" + location +
        '}';
  }

}
