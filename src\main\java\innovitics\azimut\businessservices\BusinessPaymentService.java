package innovitics.azimut.businessservices;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import innovitics.azimut.businessmodels.BusinessPayment;
import innovitics.azimut.businessmodels.payment.PaymobObject;
import innovitics.azimut.businessmodels.payment.PaymobResult;
import innovitics.azimut.businessmodels.payment.PaytabsResult;
import innovitics.azimut.businessmodels.trading.BaseAzimutTrading;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.businessutilities.BusinessSearchCriteria;
import innovitics.azimut.controllers.payment.DTOs.InitiatePaymentDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.GeneralException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.models.payment.PaymentTransaction;
import innovitics.azimut.rest.apis.paymob.PaymobInitiatePaymentApi;
import innovitics.azimut.rest.apis.paytabs.PaytabsInitiatePaymentApi;
import innovitics.azimut.rest.apis.paytabs.PaytabsQueryPaymentApi;
import innovitics.azimut.rest.entities.paymob.PaymobInitiatePaymentRequest;
import innovitics.azimut.rest.entities.paytabs.PaytabsInitiatePaymentRequest;
import innovitics.azimut.rest.entities.paytabs.PaytabsQueryRequest;
import innovitics.azimut.rest.models.paymob.PaymobBillingData;
import innovitics.azimut.rest.models.paymob.PaymobItem;
import innovitics.azimut.rest.models.paytabs.CustomerDetails;
import innovitics.azimut.rest.models.paytabs.ShippingDetails;
import innovitics.azimut.security.HmacUtil;
import innovitics.azimut.utilities.crosslayerenums.Action;
import innovitics.azimut.utilities.crosslayerenums.CurrencyType;
import innovitics.azimut.utilities.crosslayerenums.PaymentGateway;
import innovitics.azimut.utilities.crosslayerenums.PaymentTransactionStatus;
import innovitics.azimut.utilities.crosslayerenums.TransactionStatus;
import innovitics.azimut.utilities.datautilities.BooleanUtility;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.datautilities.PaginatedEntity;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;
import innovitics.azimut.validations.validators.payment.InitiatePayment;

@Service
public class BusinessPaymentService extends AbstractBusinessService<BusinessPayment> {

  @Autowired
  BusinessAzimutTradingService businessAzimutTradingService;
  @Autowired
  InitiatePayment initiatePayment;
  @Autowired
  PaytabsInitiatePaymentApi paytabsInitiatePaymentApi;
  @Autowired
  PaymobInitiatePaymentApi paymobInitiatePaymentApi;
  @Autowired
  PaytabsQueryPaymentApi paytabsQueryPaymentApi;
  @Autowired
  HmacUtil hmacUtil;

  public static final String TRANSACTION_ID_PARAM = "transactionId";
  public static final String AMOUNT_PARAM = "amount";

  public BusinessPayment initiatePayment(InitiatePaymentDto initiatePaymentDto, BusinessUser tokenizedBusinessUser,
      String language, boolean isMobile) throws IntegrationException, BusinessException {
    var businessPayment = initiatePaymentDto.toBusinessPayment();
    this.validation.validate(businessPayment, initiatePayment, BusinessPayment.class.getName());

    try {
      PaymentTransaction paymentTransaction = new PaymentTransaction();
      this.populateDynamicParameters(paymentTransaction, initiatePaymentDto);
      paymentTransaction = this.paymentService.addPaymentTransaction(
          this.userMapper.convertBusinessUnitToBasicUnit(tokenizedBusinessUser, false),
          initiatePaymentDto.getOrderValue(), PaymentGateway.PAYTABS, initiatePaymentDto.getCurrencyId(),
          initiatePaymentDto.getAction(), paymentTransaction.getParameterNames(),
          paymentTransaction.getParameterValues(), isMobile ? "mobile" : "web");

      if (!isMobile) {
        businessPayment = this.preparePaymentInputs(paymentTransaction, tokenizedBusinessUser, businessPayment,
            language);
        businessPayment = getPaytabsResponse(businessPayment);

        this.updateTransactionAfterGatewayCall(businessPayment, paymentTransaction,
            PaymentTransactionStatus.PG);
      } else {
        businessPayment.setCartId(String.valueOf(paymentTransaction.getId()));
        // businessPayment.setCartId(this.aes.encrypt(String.valueOf(paymentTransaction.getId())+"-"+String.valueOf(StringUtility.generateAmountStringWithoutDecimalPoints(businessPayment.getAmount()))));
        businessPayment.setTransactionId(paymentTransaction.getId());
      }
    } catch (Exception exception) {
      throw this.exceptionHandler.handleException(exception);
    }

    return businessPayment;
  }

  private BusinessPayment getPaytabsResponse(BusinessPayment businessPayment)
      throws IntegrationException, BusinessException {
    paytabsInitiatePaymentApi.setLocale(businessPayment.getLanguage());
    var input = generatePaytabsRequestFromInput(businessPayment);
    var response = paytabsInitiatePaymentApi.getData(input);
    businessPayment = new BusinessPayment();
    businessPayment.setReferenceTransactionId(response.getTransactionReference());
    businessPayment.setRedirectUrl(response.getRedirectUrl());
    return businessPayment;
  }

  public BusinessPayment initiatePaymobPayment(InitiatePaymentDto initiatePaymentDto,
      BusinessUser tokenizedBusinessUser,
      String language, boolean isMobile) throws IntegrationException, BusinessException {
    var businessPayment = initiatePaymentDto.toBusinessPayment();
    this.validation.validate(businessPayment, initiatePayment, BusinessPayment.class.getName());

    try {
      PaymentTransaction paymentTransaction = new PaymentTransaction();
      this.populateDynamicParameters(paymentTransaction, initiatePaymentDto);
      paymentTransaction = this.paymentService.addPaymentTransaction(
          this.userMapper.convertBusinessUnitToBasicUnit(tokenizedBusinessUser, false),
          initiatePaymentDto.getOrderValue(), PaymentGateway.PAYMOB, initiatePaymentDto.getCurrencyId(),
          initiatePaymentDto.getAction(), paymentTransaction.getParameterNames(),
          paymentTransaction.getParameterValues(), isMobile ? "mobile" : "web");

      businessPayment.setCartId(String.valueOf(paymentTransaction.getId()));
      businessPayment = this.preparePaymentInputs(paymentTransaction, tokenizedBusinessUser, businessPayment,
          language);
      businessPayment = getPaymobResponse(businessPayment);

      this.updateTransactionAfterPaymobCall(businessPayment, paymentTransaction);

    } catch (Exception exception) {
      throw this.exceptionHandler.handleException(exception);
    }

    return businessPayment;
  }

  private BusinessPayment getPaymobResponse(BusinessPayment businessPayment)
      throws IntegrationException, BusinessException {

    var input = generatePaymobRequestFromInput(businessPayment);
    var response = paymobInitiatePaymentApi.getData(input);
    businessPayment = new BusinessPayment();
    businessPayment.setReferenceTransactionId(response.getIntentionOrderId().toString());
    businessPayment.setSecretKey(response.getClientSecret());
    businessPayment.setPublicKey(this.configProperties.getPaymobPublicKey());
    businessPayment.setStatusMessage(response.getStatus());
    return businessPayment;
  }

  private void updateTransactionAfterPaymobCall(BusinessPayment businessPayment,
      PaymentTransaction paymentTransaction) {
    paymentTransaction.setStatus(businessPayment.getStatusMessage());
    paymentTransaction.setReferenceTransactionId(businessPayment.getReferenceTransactionId());
    this.paymentService.updatePaymentTransaction(paymentTransaction);

  }

  public PaytabsResult updateTransactionAfterPaymobCallback(PaymobResult paymobCallbackRequest,
      String hmac) throws BusinessException {

    if (paymobCallbackRequest == null)
      throw new BusinessException(ErrorCode.INVALID_FIELD_VALUE);

    if (!StringUtility.stringsMatch(paymobCallbackRequest.getType(), "TRANSACTION")
        && paymobCallbackRequest.getTransaction() == null)
      return new PaytabsResult();

    var obj = paymobCallbackRequest.getObj() == null ? paymobCallbackRequest.getTransaction()
        : paymobCallbackRequest.getObj();

    if (!StringUtility.stringsMatch(hmac,
        hmacUtil.generateHmac512(obj.getHmac(), configProperties.getPaymobHmac()))) {
      throw new BusinessException(ErrorCode.PAYMENT_TRANSACTION_CORRUPTED);
    }

    try {
      this.findUpdateAndExecutePaymob(paymobCallbackRequest);

    } catch (Exception exception) {
      throw this.handleBusinessException(exception, ErrorCode.PAYMENT_FAILURE);
    }

    return new PaytabsResult();

  }

  public PaginatedEntity<BusinessPayment> listTransactions(BusinessSearchCriteria businessSearchCriteria) {
    var transactions = this.paymentService
        .getFinishedTransactions(generateDatabaseConditions(businessSearchCriteria));
    PaginatedEntity<BusinessPayment> paginatedEntity = new PaginatedEntity<BusinessPayment>();

    paginatedEntity.setCurrentPage(businessSearchCriteria.getPageNumber());
    paginatedEntity.setPageSize(businessSearchCriteria.getPageSize());
    paginatedEntity.setNumberOfPages(transactions.getTotalPages());
    paginatedEntity.setNumberOfItems(transactions.getTotalElements());
    paginatedEntity.setHasNext(!transactions.isLast());
    paginatedEntity.setHasPrevious(!transactions.isFirst());
    List<BusinessPayment> list = new ArrayList<>();
    if (!transactions.isEmpty()) {
      for (var transaction : transactions.getContent()) {
        var businessPayment = new BusinessPayment();
        businessPayment.setId(transaction.getId());
        if (transaction.getUser() != null)
          businessPayment.setEmailAddress(transaction.getUser().getEmailAddress());
        if (transaction.getUser() != null)
          businessPayment.setUserPhone(transaction.getUser().getUserPhone());
        businessPayment.setCreatedAt(transaction.getCreatedAt());
        businessPayment.setAmount(transaction.getTransactionAmount());
        businessPayment.setReferenceTransactionId(transaction.getReferenceTransactionId());
        if (transaction.getTeacomputerStatus() != null)
          businessPayment.setTransactionStatus(transaction.getTeacomputerStatus().toString());
        list.add(businessPayment);
      }
    } else {
      list.add(null);
    }
    paginatedEntity.setDataList(list);
    return paginatedEntity;
  }

  public void retryInjection(Long transactionId)
      throws IntegrationException, BusinessException, IOException, Exception {
    var paymentTransaction = this.paymentService.getTransactionById(transactionId);
    if (paymentTransaction.getTeacomputerStatus() == null)
      return;
    this.execute(paymentTransaction);
    paymentTransaction.setTeacomputerStatus(null);
    paymentTransaction.setTeacomputerMessage(null);
    this.paymentService.updatePaymentTransaction(paymentTransaction);
  }

  public BusinessPayment queryPayment(BusinessPayment businessPayment, BusinessUser tokenizedBusinessUser,
      String language, boolean isMobile) throws IntegrationException, BusinessException {

    PaymentTransaction paymentTransaction = new PaymentTransaction();
    paymentTransaction = this.paymentTransactionUtility.getTransactionByUser(tokenizedBusinessUser.getId(),
        businessPayment.getReferenceTransactionId(), PaymentGateway.PAYTABS);

    if (paymentTransaction == null
        || TransactionStatus.UNPAID.getPaymentStatuses().contains(paymentTransaction.getStatus())) {
      PaytabsQueryRequest request = new PaytabsQueryRequest();
      request.setTransactionReference(businessPayment.getReferenceTransactionId());
      paytabsQueryPaymentApi.setLocale(language);
      var response = paytabsQueryPaymentApi.getData(request);
      try {
        paymentTransaction = this.findPaymentTransaction(response, Long.valueOf(response.getCartId()));
        if (paymentTransaction != null) {
          this.populateThePaymentTransaction(paymentTransaction, response);
        }
      } catch (Exception e) {
        throw this.handleBusinessException(e, ErrorCode.PAYMENT_TRANSACTION_NOT_FOUND);
      }
    }

    if (paymentTransaction != null) {
      businessPayment.setTransactionStatus(
          PaymentTransactionStatus.getById(paymentTransaction.getStatus()).getStatusId());
      if (StringUtility.isStringPopulated(language)) {
        if (StringUtility.stringsMatch(language, StringUtility.ENGLISH)) {
          businessPayment.setStatusMessage(
              PaymentTransactionStatus.getById(paymentTransaction.getStatus()).getStatus());
        } else if (StringUtility.stringsMatch(language, StringUtility.ARABIC)) {
          businessPayment.setStatusMessage(
              PaymentTransactionStatus.getById(paymentTransaction.getStatus()).getStatusAr());
        }
      }
      if (paymentTransaction.getTeacomputerMessage() != null) {
        businessPayment.setStatusMessage(paymentTransaction.getTeacomputerMessage());
        businessPayment.setTransactionStatus(paymentTransaction.getTeacomputerStatus().toString());
      }
    } else {
      throw new BusinessException(ErrorCode.PAYMENT_TRANSACTION_NOT_FOUND);
    }

    return businessPayment;
  }

  public BusinessPayment queryPaymobPayment(BusinessPayment businessPayment, BusinessUser tokenizedBusinessUser,
      String language, boolean isMobile) throws IntegrationException, BusinessException {

    PaymentTransaction paymentTransaction = new PaymentTransaction();
    paymentTransaction = this.paymentTransactionUtility.getTransactionByUser(tokenizedBusinessUser.getId(),
        businessPayment.getReferenceTransactionId(), PaymentGateway.PAYMOB);

    if (paymentTransaction == null
        || StringUtility.stringsMatch(paymentTransaction.getStatus(), StringUtility.PAYMOB_PENDING_STATUS)) {
      // todo query paymob
      throw new BusinessException(ErrorCode.PAYMENT_TRANSACTION_NOT_FOUND);
    }

    if (paymentTransaction != null) {
      businessPayment.setTransactionStatus(
          PaymentTransactionStatus.getById(paymentTransaction.getStatus()).getStatusId());
      if (StringUtility.isStringPopulated(language)) {
        if (StringUtility.stringsMatch(language, StringUtility.ENGLISH)) {
          businessPayment.setStatusMessage(
              PaymentTransactionStatus.getById(paymentTransaction.getStatus()).getStatus());
        } else if (StringUtility.stringsMatch(language, StringUtility.ARABIC)) {
          businessPayment.setStatusMessage(
              PaymentTransactionStatus.getById(paymentTransaction.getStatus()).getStatusAr());
        }
      }
      if (paymentTransaction.getTeacomputerMessage() != null) {
        businessPayment.setStatusMessage(paymentTransaction.getTeacomputerMessage());
        businessPayment.setTransactionStatus(paymentTransaction.getTeacomputerStatus().toString());
      }
    } else {
      throw new BusinessException(ErrorCode.PAYMENT_TRANSACTION_NOT_FOUND);
    }

    return businessPayment;
  }

  public PaytabsInitiatePaymentRequest generatePaytabsRequestFromInput(BusinessPayment input) throws BusinessException {
    if (input == null)
      throw new BusinessException(ErrorCode.INVALID_FIELD_VALUE);

    PaytabsInitiatePaymentRequest request = new PaytabsInitiatePaymentRequest();
    String cartId = (input.getTransactionId() != null) ? String.valueOf(input.getTransactionId())
        : null;
    String name = input.getFirstName() + " " + input.getLastName();

    request.setPayPageLang(input.getLanguage());
    request.setCartId(cartId);
    var amount = (input.getAmount() + 1.5) / 0.9835; // paytabs fees 1.65% + 1.5
    amount = Math.ceil(amount * 100.0) / 100.0;
    request.setCartAmount(amount);
    request.setCartCurrency(CurrencyType.getById(input.getCurrencyId()).getType());
    request.setCartDescription("Cart with transaction id" + " " + cartId + " from Innovitics");
    CustomerDetails customerDetails = new CustomerDetails();
    customerDetails.setCity(input.getCity());
    customerDetails.setCountry(input.getCountry());
    customerDetails.setEmail(input.getEmailAddress());
    customerDetails.setName(name);
    customerDetails.setPhone(input.getUserPhone());

    customerDetails.setZip(null);
    request.setCustomerDetails(customerDetails);
    ShippingDetails shippingDetails = new ShippingDetails();
    shippingDetails.setCity(input.getCity());
    shippingDetails.setCountry(input.getCountry());
    shippingDetails.setEmail(input.getEmailAddress());
    shippingDetails.setName(name);
    shippingDetails.setPhone(input.getUserPhone());
    shippingDetails.setZip(null);
    request.setShippingDetails(shippingDetails);

    String serial = paytabsInitiatePaymentApi.generateSerial(request);

    request.setCallbackUrl(this.configProperties.getPaytabsCallBackUrl() + "?"
        + StringUtility.TRANSACTION_SERIAL_PARAM_NAME + "=" + serial);
    request.setReturnUrl(input.getReturnUrl());

    return request;
  }

  public PaymobInitiatePaymentRequest generatePaymobRequestFromInput(BusinessPayment input) throws BusinessException {
    if (input == null)
      throw new BusinessException(ErrorCode.INVALID_FIELD_VALUE);

    PaymobInitiatePaymentRequest request = new PaymobInitiatePaymentRequest();
    String cartId = (input.getTransactionId() != null) ? String.valueOf(input.getTransactionId())
        : null;

    request.setSpecialReference(cartId);
    var amount = input.getAmount() / 0.988; // paymob fees 1.1%
    amount = Math.ceil(amount * 100.0);
    request.setAmount((int) amount);
    request.setCurrency(CurrencyType.getById(input.getCurrencyId()).getType());
    PaymobItem item = new PaymobItem();
    item.setName("Wallet Top-up");
    item.setAmount((int) amount);
    item.setDescription("Wallet Top-up");
    item.setQuantity(1);
    PaymobItem[] items = { item };
    request.setItems(items);

    if (input.getReturnUrl() != null)
      request.setRedirectionUrl(input.getReturnUrl());
    PaymobBillingData customerDetails = new PaymobBillingData();
    customerDetails.setState(input.getCity());
    customerDetails.setCountry(input.getCountry());
    customerDetails.setEmail(input.getEmailAddress());
    customerDetails.setFirstName(input.getFirstName());
    customerDetails.setLastName(input.getLastName());
    customerDetails.setPhoneNumber(input.getUserPhone());
    customerDetails.setStreet("NA");
    customerDetails.setApartment("NA");
    customerDetails.setBuilding("NA");
    customerDetails.setFloor("NA");
    request.setBillingData(customerDetails);
    request.setCustomer(customerDetails);

    request.setNotificationUrl(this.configProperties.getPaymobCallBackUrl());

    return request;
  }

  private BusinessPayment preparePaymentInputs(PaymentTransaction paymentTransaction,
      BusinessUser tokenizedBusinessUser, BusinessPayment businessPayment, String language) {
    businessPayment.setTransactionId(paymentTransaction.getId());
    businessPayment.setUserPhone(tokenizedBusinessUser.getUserPhone());
    businessPayment.setCity(tokenizedBusinessUser.getCity());
    businessPayment.setCountry(tokenizedBusinessUser.getCountry());
    businessPayment.setEmailAddress(tokenizedBusinessUser.getEmailAddress());
    businessPayment.setLanguage(language);
    businessPayment.setFirstName(tokenizedBusinessUser.getFirstName() == null ? tokenizedBusinessUser.getNickName()
        : tokenizedBusinessUser.getFirstName());
    businessPayment
        .setLastName(tokenizedBusinessUser.getLastName() == null ? "Azimut" : tokenizedBusinessUser.getLastName());
    return businessPayment;
  }

  private void updateTransactionAfterGatewayCall(BusinessPayment businessPayment,
      PaymentTransaction paymentTransaction, PaymentTransactionStatus paymentTransactionStatus) {
    paymentTransaction.setStatus(paymentTransactionStatus.getStatusId());
    paymentTransaction.setReferenceTransactionId(businessPayment.getReferenceTransactionId());
    this.paymentService.updatePaymentTransaction(paymentTransaction);

  }

  public PaytabsResult updateTransactionAfterGatewayCallback(PaytabsResult paytabsCallbackRequest,
      String serial) throws BusinessException {

    PaymentTransaction paymentTransaction = new PaymentTransaction();
    String valueToEncrypt = "";
    String amountWithoutDecimalPoint = "";

    try {
      if (paytabsCallbackRequest == null)
        throw new BusinessException(ErrorCode.INVALID_FIELD_VALUE);

      if (!StringUtility.stringsMatch(paytabsCallbackRequest.getTransactionType(), "Sale"))
        return new PaytabsResult();

      boolean areParamsPopulated = (StringUtility.isStringPopulated(paytabsCallbackRequest.getCartId())
          && StringUtility.isStringPopulated(paytabsCallbackRequest.getCartAmount()));
      if (areParamsPopulated) {
        amountWithoutDecimalPoint = StringUtility
            .generateAmountStringWithoutDecimalPoints(paytabsCallbackRequest.getCartAmount());
      }
      this.checkPaymentStatus(paytabsCallbackRequest.getTransactionReference(),
          Double.valueOf(paytabsCallbackRequest.getCartAmount()),
          paytabsCallbackRequest.getPaymentResult().getResponseStatus());
      if (StringUtility.isStringPopulated(serial)) {
        MyLogger.info("Serial populated:::");
        valueToEncrypt = areParamsPopulated ? amountWithoutDecimalPoint : null;
        if (StringUtility.stringsMatch(serial,
            StringUtility.isStringPopulated(valueToEncrypt)
                ? this.aes.ecryptWithoutSpecialCharacters(valueToEncrypt)
                : null)) {
          this.findUpdateAndExecute(paymentTransaction, paytabsCallbackRequest);
        } else {
          throw new BusinessException(ErrorCode.PAYMENT_TRANSACTION_NOT_FOUND);
        }
      } else {
        MyLogger.info("Serial Not populated:::");
        this.findUpdateAndExecute(paymentTransaction, paytabsCallbackRequest);
      }
    } catch (Exception exception) {
      throw this.handleBusinessException(exception, ErrorCode.PAYMENT_FAILURE);
    }

    return new PaytabsResult();

  }

  private PaymentTransaction findPaymentTransaction(PaytabsResult paytabsCallbackRequest, Long transactionId)
      throws BusinessException {
    PaymentTransaction paymentTransaction = new PaymentTransaction();
    try {
      if (transactionId != null) {
        MyLogger.info("Get By Id");
        paymentTransaction = this.paymentService.getTransactionById(transactionId);
      } else {
        MyLogger.info("Get By Reference Id");
        paymentTransaction = this.paymentService.getTransactionByReferenceId(
            paytabsCallbackRequest.getTransactionReference(), PaymentGateway.PAYTABS);
      }
    } catch (Exception exception) {
      this.exceptionHandler.getNullIfNonExistent(exception);
      return null;
    }
    return paymentTransaction;

  }

  private void populateThePaymentTransaction(PaymentTransaction paymentTransaction,
      PaytabsResult paytabsCallbackRequest) {

    paymentTransaction.setReferenceTransactionId(paytabsCallbackRequest.getTransactionReference());

    if (paymentTransaction != null && paytabsCallbackRequest != null
        && paytabsCallbackRequest.getPaymentResult() != null) {
      if (paytabsCallbackRequest.getPaymentResult().getResponseStatus() != null) {
        paymentTransaction.setStatus(paytabsCallbackRequest.getPaymentResult().getResponseStatus());
      }
      if (paytabsCallbackRequest.getPaymentResult().getResponseMessage() != null) {
        paymentTransaction.setMessage(paytabsCallbackRequest.getPaymentResult().getResponseMessage());
      }
      if (paytabsCallbackRequest.getPaymentInfo() != null
          && StringUtility.isStringPopulated(paytabsCallbackRequest.getPaymentInfo().getPaymentMethod())) {
        paymentTransaction.setPaymentMethod(paytabsCallbackRequest.getPaymentInfo().getPaymentMethod());
      }
    }
  }

  private PaymentTransaction findPaymobPaymentTransaction(PaymobObject paymobObject, Long transactionId)
      throws BusinessException {
    PaymentTransaction paymentTransaction = new PaymentTransaction();
    try {
      if (transactionId != null) {
        MyLogger.info("Get By Id");
        paymentTransaction = this.paymentService.getTransactionById(transactionId);
      } else {
        MyLogger.info("Get By Reference Id");
        paymentTransaction = this.paymentService.getTransactionByReferenceId(
            paymobObject.getOrder().getId(), PaymentGateway.PAYMOB);
      }
    } catch (Exception exception) {
      this.exceptionHandler.getNullIfNonExistent(exception);
      return null;
    }
    return paymentTransaction;

  }

  private void populatePaymobTransaction(PaymentTransaction paymentTransaction,
      PaymobObject paymobObject) {

    if (paymentTransaction != null && paymobObject != null
        && paymobObject.getOrder() != null) {
      paymentTransaction.setStatus(BooleanUtility.isTrue(paymobObject.getPending())
          ? StringUtility.PAYMOB_PENDING_STATUS
          : BooleanUtility.isTrue(paymobObject.getSuccess()) ? StringUtility.PAYMOB_SUCCESS_STATUS
              : StringUtility.PAYMOB_FAILED_STATUS);
      if (paymobObject.getSourceData() != null
          && StringUtility.isStringPopulated(paymobObject.getSourceData().getSubType())) {
        paymentTransaction.setPaymentMethod(paymobObject.getSourceData().getSubType());
      }
    }
  }

  private void executePaymob(PaymentTransaction paymentTransaction)
      throws IntegrationException, BusinessException, IOException, Exception {
    if (paymentTransaction == null)
      throw new BusinessException(ErrorCode.PAYMENT_TRANSACTION_NOT_FOUND);

    var isMatched = StringUtility.stringsMatch(paymentTransaction.getStatus(),
        StringUtility.PAYMOB_SUCCESS_STATUS);

    if (isMatched) {
      MyLogger.info("Payment Succeeded:::");
      if (NumberUtility.areIntegerValuesMatching(Action.INJECT.getActionId(), paymentTransaction.getAction())) {
        this.inject(paymentTransaction);
      }
    }
  }

  private void checkPaymentStatus(String transactionReference, Double amount, String responseStatus)
      throws BusinessException {
    try {
      PaytabsQueryRequest request = new PaytabsQueryRequest();
      request.setTransactionReference(transactionReference);
      var input = new BusinessPayment(transactionReference);
      paytabsQueryPaymentApi.setLocale(input.getLanguage());

      var response = paytabsQueryPaymentApi.getData(request);
      BusinessPayment queryBusinessPayment = new BusinessPayment();
      queryBusinessPayment.setAmount(Double.valueOf(response.getCartAmount()));
      queryBusinessPayment.setTransactionStatus(response.getPaymentResult().getResponseStatus());

      if (!NumberUtility.areDoubleValuesMatching(amount, queryBusinessPayment.getAmount())
          || StringUtility.stringsDontMatch(responseStatus, queryBusinessPayment.getTransactionStatus()))

        throw new BusinessException(ErrorCode.PAYMENT_TRANSACTION_CORRUPTED);
    } catch (Exception exception) {
      throw this.exceptionHandler.handleException(exception);
    }

  }

  private void execute(PaymentTransaction paymentTransaction)
      throws IntegrationException, BusinessException, IOException, Exception {
    if (paymentTransaction == null)
      throw new BusinessException(ErrorCode.PAYMENT_TRANSACTION_NOT_FOUND);

    var isMatched = StringUtility.stringsMatch(paymentTransaction.getStatus(),
        StringUtility.PAYTABS_SUCCESS_STATUS);

    if (isMatched) {
      MyLogger.info("Payment Succeeded:::");
      if (NumberUtility.areIntegerValuesMatching(Action.INJECT.getActionId(), paymentTransaction.getAction())) {
        this.inject(paymentTransaction);
      }
    }
  }

  private void inject(PaymentTransaction paymentTransaction)
      throws IntegrationException, BusinessException, IOException, Exception {

    BaseAzimutTrading baseAzimutTrading = new BaseAzimutTrading();
    baseAzimutTrading.setOrderValue(paymentTransaction.getTransactionAmount());
    baseAzimutTrading.setCurrencyId(paymentTransaction.getCurrencyId());
    baseAzimutTrading.setReferenceNo(paymentTransaction.getReferenceTransactionId());
    // if (paymentTransaction != null && paymentTransaction.getKeyValueMap() !=
    // null) {
    // if
    // (StringUtility.isStringPopulated(paymentTransaction.getKeyValueMap().get(StringUtility.BANK_ID)))
    // {
    // baseAzimutTrading
    // .setBankId(Long.valueOf(paymentTransaction.getKeyValueMap().get(StringUtility.BANK_ID)));
    // }
    // if
    // (StringUtility.isStringPopulated(paymentTransaction.getKeyValueMap().get(StringUtility.ACCOUNT_ID)))
    // {
    // baseAzimutTrading
    // .setAccountId(Long.valueOf(paymentTransaction.getKeyValueMap().get(StringUtility.ACCOUNT_ID)));
    // }
    // }
    baseAzimutTrading.setBankId(34L); // Banque Misr
    baseAzimutTrading.setAccountId(55L); // valU-Smart Village Branch
    this.businessAzimutTradingService.inject(
        this.userMapper.convertBasicUnitToBusinessUnit(paymentTransaction.getUser()), baseAzimutTrading);

  }

  public void populateDynamicParameters(PaymentTransaction paymentTransaction, InitiatePaymentDto businessPayment) {

    StringBuffer parameterNames = new StringBuffer();
    StringBuffer parameterValues = new StringBuffer();
    if (businessPayment != null && businessPayment.getAction() != null) {
      if (NumberUtility.areIntegerValuesMatching(Action.INJECT.getActionId(), businessPayment.getAction())) {
        parameterNames.append(StringUtility.BANK_ID);
        parameterNames.append(StringUtility.COMMA);
        parameterNames.append(StringUtility.ACCOUNT_ID);
        parameterValues.append(String.valueOf(businessPayment.getBankId()));
        parameterValues.append(StringUtility.COMMA);
        parameterValues.append(String.valueOf(businessPayment.getAccountId()));
      }

      paymentTransaction.setParameterNames(parameterNames.toString());
      paymentTransaction.setParameterValues(parameterValues.toString());
    }
  }

  public Map<String, String> generateIdAndAmount(String value) {
    MyLogger.info("Value:::" + value);
    Map<String, String> stringMap = new HashMap<String, String>();
    if (StringUtility.isStringPopulated(value)) {
      List<String> values = StringUtility.splitStringUsingCharacter(aes.decrypt(value), "-");
      MyLogger.info("Values:::" + StringUtility.splitStringUsingCharacter(aes.decrypt(value), "-"));
      stringMap.put(TRANSACTION_ID_PARAM, values.get(0));
      stringMap.put(AMOUNT_PARAM, values.get(1));

      return stringMap;
    } else
      return null;
  }

  public void findUpdateAndExecute(PaymentTransaction paymentTransaction,
      PaytabsResult paytabsCallbackRequest) throws IOException, Exception {
    try {
      paymentTransaction = this.findPaymentTransaction(paytabsCallbackRequest,
          Long.valueOf(paytabsCallbackRequest.getCartId()));
    } catch (Exception exception) {
      throw this.handleBusinessException(exception, ErrorCode.PAYMENT_TRANSACTION_NOT_FOUND);
    }

    if (paymentTransaction != null) {
      this.populateThePaymentTransaction(paymentTransaction, paytabsCallbackRequest);
      this.paymentService.updatePaymentTransaction(paymentTransaction);
      try {
        this.execute(paymentTransaction);
      } catch (GeneralException generalException) {
        MyLogger.logStackTrace(generalException);
        paymentTransaction.setTeacomputerStatus(generalException.getErrorCode());
        paymentTransaction.setTeacomputerMessage(generalException.getErrorMessage());
        this.paymentService.updatePaymentTransaction(paymentTransaction);
      }
    }
  }

  public void findUpdateAndExecutePaymob(PaymobResult paymobCallbackRequest) throws IOException, Exception {
    PaymentTransaction paymentTransaction = new PaymentTransaction();
    try {
      paymentTransaction = this.findPaymobPaymentTransaction(paymobCallbackRequest.getObj(), null);
    } catch (Exception exception) {
      throw this.handleBusinessException(exception, ErrorCode.PAYMENT_TRANSACTION_NOT_FOUND);
    }

    if (paymentTransaction != null) {
      this.populatePaymobTransaction(paymentTransaction, paymobCallbackRequest.getObj());
      this.paymentService.updatePaymentTransaction(paymentTransaction);
      try {
        this.executePaymob(paymentTransaction);
      } catch (GeneralException generalException) {
        MyLogger.logStackTrace(generalException);
        paymentTransaction.setTeacomputerStatus(generalException.getErrorCode());
        paymentTransaction.setTeacomputerMessage(generalException.getErrorMessage());
        this.paymentService.updatePaymentTransaction(paymentTransaction);
      }
    }
  }

}
