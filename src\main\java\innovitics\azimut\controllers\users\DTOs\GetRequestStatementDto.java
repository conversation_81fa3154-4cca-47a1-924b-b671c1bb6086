package innovitics.azimut.controllers.users.DTOs;

import innovitics.azimut.utilities.datautilities.StringUtility;
import lombok.Data;

@Data
public class GetRequestStatementDto {
  String searchFromDate;
  String searchToDate;
  Long currencyId;

  Long partnerId;

  public String getSearchToDate() {
    return StringUtility.convertArabicToEnglish(searchToDate);
  }

  public String getSearchFromDate() {
    return StringUtility.convertArabicToEnglish(searchFromDate);
  }
}
