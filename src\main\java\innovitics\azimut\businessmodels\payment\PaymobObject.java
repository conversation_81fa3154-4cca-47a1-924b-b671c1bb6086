package innovitics.azimut.businessmodels.payment;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class PaymobObject {
  @JsonProperty("amount_cents")
  private Integer amountCents;
  @JsonProperty("created_at")
  private String createdAt;
  private String currency;
  @JsonProperty("error_occured")
  private Boolean errorOccured;
  @JsonProperty("has_parent_transaction")
  private Boolean hasParentTransaction;
  private String id;
  @JsonProperty("integration_id")
  private Integer integrationId;
  @JsonProperty("is_3d_secure")
  private Boolean is3dSecure;
  @JsonProperty("is_auth")
  private Boolean isAuth;
  @JsonProperty("is_captured")
  private Boolean isCaptured;
  @JsonProperty("is_refunded")
  private Boolean isRefunded;
  @JsonProperty("is_standalone_payment")
  private Boolean isStandalonePayment;
  @JsonProperty("is_voided")
  private Boolean isVoided;
  private PaymobOrder order;
  private String owner;
  private Boolean pending;
  @JsonProperty("source_data")
  private PaymobSourceData sourceData;
  private Boolean success;

  public String getHmac() {
    StringBuilder sb = new StringBuilder();
    sb.append(this.amountCents);
    sb.append(this.createdAt);
    sb.append(this.currency);
    sb.append(this.errorOccured);
    sb.append(this.hasParentTransaction);
    sb.append(this.id);
    sb.append(this.integrationId);
    sb.append(this.is3dSecure);
    sb.append(this.isAuth);
    sb.append(this.isCaptured);
    sb.append(this.isRefunded);
    sb.append(this.isStandalonePayment);
    sb.append(this.isVoided);
    if (this.order != null) {
      sb.append(this.order.getId());
    }
    sb.append(this.owner);
    sb.append(this.pending);
    if (this.sourceData != null) {
      sb.append(this.sourceData.getPan());
      sb.append(this.sourceData.getSubType());
      sb.append(this.sourceData.getType());
    }
    sb.append(this.success);
    return sb.toString();
  }

}
