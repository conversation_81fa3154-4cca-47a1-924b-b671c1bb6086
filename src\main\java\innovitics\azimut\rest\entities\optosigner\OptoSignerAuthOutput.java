package innovitics.azimut.rest.entities.optosigner;

import com.fasterxml.jackson.annotation.JsonProperty;

import innovitics.azimut.utilities.CustomJsonRootName;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@CustomJsonRootName(plural = "auth", singular = "auth")
public class OptoSignerAuthOutput {
  @JsonProperty("access_token")
  String accessToken;

  @Override
  public String toString() {
    return "OptoSignerAuthOutput []";
  }
}
