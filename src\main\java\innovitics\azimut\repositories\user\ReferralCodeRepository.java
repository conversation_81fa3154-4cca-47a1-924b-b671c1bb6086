package innovitics.azimut.repositories.user;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import innovitics.azimut.models.user.ReferralCode;

@Repository
public interface ReferralCodeRepository
    extends JpaRepository<ReferralCode, Long>, JpaSpecificationExecutor<ReferralCode> {
  Optional<ReferralCode> findByCode(String code);

}
