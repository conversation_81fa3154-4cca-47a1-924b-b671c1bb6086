package innovitics.azimut.rest.apis.teacomputers;

import java.math.BigDecimal;
import java.util.ArrayList;

import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import innovitics.azimut.businessmodels.user.BusinessClientCashBalance;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.rest.entities.teacomputers.ClientCashBalanceRequest;
import innovitics.azimut.rest.entities.teacomputers.ClientCashBalanceResponse;
import innovitics.azimut.rest.entities.teacomputers.TeaComputerResponse;
import innovitics.azimut.utilities.crosslayerenums.CurrencyType;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.logging.MyLogger;

@Component
// we have to rename GetClientCashBalanceRequest and Response to
// GetClientBalanceRequest and Response
public class TeaComputersGetClientCashBalanceApi
    extends RestTeaComputersApi<ClientCashBalanceRequest, ClientCashBalanceResponse[]> {
  public static final String PATH = "/GetClientCashBalance";

  @Override
  protected String generateURL(String params) {
    return super.generateBaseURL() + PATH;
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.GET;
  }

  @Override
  protected String generateSignature(ClientCashBalanceRequest getClientCashBalanceRequest) {
    return this.teaComputersSignatureGenerator.generateSignature(true,
        getClientCashBalanceRequest.getUserName() != null ? getClientCashBalanceRequest.getUserName()
            : this.configProperties.getTeaComputersKey(),
        "",
        getClientCashBalanceRequest.getIdTypeId().toString(), getClientCashBalanceRequest.getIdNumber());
  }

  @Override
  protected String generateResponseSignature(ClientCashBalanceRequest request,
      TeaComputerResponse teaComputerResponse) {
    ClientCashBalanceResponse clientCashBalanceResponse = (ClientCashBalanceResponse) teaComputerResponse;
    return this.teaComputersSignatureGenerator.generateSignature(true,
        request.getUserName() != null ? request.getUserName() : this.configProperties.getTeaComputersKey(),
        "",
        clientCashBalanceResponse.getCurrencyID().toString(), clientCashBalanceResponse.getBalance().toString());
  }

  @Override
  protected String generateResponseSignature(TeaComputerResponse teaComputerResponse) {
    // Not used
    ClientCashBalanceResponse clientCashBalanceResponse = (ClientCashBalanceResponse) teaComputerResponse;
    return this.teaComputersSignatureGenerator.generateSignature("",
        clientCashBalanceResponse.getCurrencyID().toString(), clientCashBalanceResponse.getBalance().toString());
  }

  public ClientCashBalanceRequest prepareRequest(BusinessUser tokenizedBusinessUser, String partnerUsername)
      throws BusinessException {
    ClientCashBalanceRequest request = new ClientCashBalanceRequest();

    request.setIdTypeId(tokenizedBusinessUser.getAzimutIdTypeId());
    request.setIdNumber(tokenizedBusinessUser.getUserId());
    if (partnerUsername != null)
      request.setUserName(partnerUsername);
    request.setSignature(this.generateSignature(request));

    MyLogger.info("newCashBalanceRequest:" + request.toString());
    return request;
  }

  public ArrayList<BusinessClientCashBalance> createListBusinessEntityFromResponse(
      ClientCashBalanceResponse[] clientBalanceResponses, String language) {

    ArrayList<BusinessClientCashBalance> businessClientCashBalances = new ArrayList<>();
    if (clientBalanceResponses == null)
      return businessClientCashBalances;

    for (ClientCashBalanceResponse response : clientBalanceResponses) {
      if (response == null) {
        continue;
      }

      BusinessClientCashBalance businessClientCashBalance = new BusinessClientCashBalance();
      double balance = 0D;
      double value = 0D;

      if (response.getBalance() != null) {
        BigDecimal r = new BigDecimal(response.getBalance()).subtract(new BigDecimal(response.getTotalBuyValue()))
            .subtract(new BigDecimal(response.getOutPendingTrans()));
        balance = r.doubleValue();
      }

      businessClientCashBalance.setBalance(balance);
      businessClientCashBalance.setBalanceFormatted(balance != 0D ? NumberUtility.changeFormat(balance) : "0");

      if (StringUtility.isStringPopulated(response.getCurrencyID())) {
        Long currencyID = Long.valueOf(response.getCurrencyID());
        businessClientCashBalance.setCurrencyID(currencyID);
        var currencyType = CurrencyType.getById(currencyID);
        if (currencyType != null && StringUtility.isStringPopulated(currencyType.getType())) {
          businessClientCashBalance
              .setCurrencyName(StringUtility.stringsMatch(language, "ar") ? currencyType.getTypeAr()
                  : currencyType.getType());
        } else {
          businessClientCashBalance.setCurrencyName(response.getCurrencyName());
        }
      }

      if (StringUtility.isStringPopulated(response.getCurrencyRate())) {
        businessClientCashBalance.setCurrencyRate(Double.valueOf(response.getCurrencyRate()));
      }

      if (response.getOutPendingTrans() != null && response.getInPendingTrans() != null) {
        value = response.getInPendingTrans().doubleValue() - response.getOutPendingTrans().doubleValue();
      }
      businessClientCashBalance.setPendingTransfer(value);
      businessClientCashBalance.setPendingTransferFormatted(value != 0D ? NumberUtility.changeFormat(value) : "0");
      businessClientCashBalance.setTotalBuyValue(response.getTotalBuyValue());

      businessClientCashBalances.add(businessClientCashBalance);

    }
    return businessClientCashBalances;
  }
}
