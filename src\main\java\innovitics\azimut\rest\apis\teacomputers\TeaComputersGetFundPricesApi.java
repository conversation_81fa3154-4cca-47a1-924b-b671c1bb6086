package innovitics.azimut.rest.apis.teacomputers;

import java.util.ArrayList;
import java.util.List;

import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import innovitics.azimut.businessmodels.funds.BusinessFundPrice;
import innovitics.azimut.rest.entities.teacomputers.FundPriceResponse;
import innovitics.azimut.rest.entities.teacomputers.GetFundsPricesRequest;
import innovitics.azimut.rest.entities.teacomputers.TeaComputerResponse;
import innovitics.azimut.utilities.datautilities.DateUtility;

@Component
public class TeaComputersGetFundPricesApi extends RestTeaComputersApi<GetFundsPricesRequest, FundPriceResponse[]> {
  public static final String PATH = "/GetFundPrice";

  @Override
  protected String generateURL(String params) {
    return super.generateBaseURL() + PATH;
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.GET;
  }

  @Override
  protected String generateSignature(GetFundsPricesRequest getFundsPricesRequest) {
    return this.teaComputersSignatureGenerator.generateSignature("", getFundsPricesRequest.getFromDate(),
        getFundsPricesRequest.getToDate());
  }

  @Override
  protected String generateResponseSignature(TeaComputerResponse teaComputerResponse) {
    FundPriceResponse response = (FundPriceResponse) teaComputerResponse;
    return this.teaComputersSignatureGenerator.generateSignature("",
        response != null && response.getFundID() != null
            ? response.getFundID().toString()
            : null);
  }

  public GetFundsPricesRequest generateRequest() {
    GetFundsPricesRequest request = new GetFundsPricesRequest();

    request.setFromDate(DateUtility.getCurrentDayMonthYear());
    request.setToDate(DateUtility.getNextYearDayMonthYear());
    request.setSignature(this.generateSignature(request));
    return request;
  }

  public List<BusinessFundPrice> getBusinessFundPrices(FundPriceResponse[] responses) {
    List<BusinessFundPrice> businessFundPrices = new ArrayList<BusinessFundPrice>();
    if (responses == null)
      return businessFundPrices;
    for (FundPriceResponse response : responses) {
      BusinessFundPrice businessFundPrice = new BusinessFundPrice();
      businessFundPrice.setFundId(response.getFundID());
      businessFundPrice.setTradePrice(response.getTradePrice());
      businessFundPrice.setNav(response.getTradePrice());
      businessFundPrice.setPriceDate(response.getPriceDate());

      businessFundPrices.add(businessFundPrice);
    }
    return businessFundPrices;
  }
}
