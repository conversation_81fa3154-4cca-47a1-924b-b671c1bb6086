package innovitics.azimut.utilities.mapping;

import java.util.Date;

import org.springframework.stereotype.Component;

import innovitics.azimut.businessmodels.user.BusinessUserOTP;
import innovitics.azimut.models.user.UserOTP;

@Component
public class UserOtpMapper extends Mapper<UserOTP, BusinessUserOTP> {

  @Override
  public UserOTP convertBusinessUnitToBasicUnit(BusinessUserOTP businessUserOTP, boolean save) {
    UserOTP userOTP = new UserOTP();
    userOTP.setId(businessUserOTP.getId());
    userOTP.setUserPhone(businessUserOTP.getUserPhone());
    userOTP.setFunctionality(businessUserOTP.getFunctionality());
    userOTP.setOtpMethod(businessUserOTP.getOtpMethod());
    userOTP.setCreatedAt(save ? new Date() : businessUserOTP.getCreatedAt());
    userOTP.setUpdatedAt(new Date());
    userOTP.setDeletedAt(save ? null : new Date());
    userOTP.setNextTrial(null);
    userOTP.setOtp(businessUserOTP.getOtp());
    userOTP.setSessionInfo(businessUserOTP.getSessionInfo());
    userOTP.setAssessmentId(businessUserOTP.getAssessmentId());
    userOTP.setContractType(businessUserOTP.getContractType());
    userOTP.setUserId(businessUserOTP.getUserId());
    return userOTP;
  }

  @Override
  public BusinessUserOTP convertBasicUnitToBusinessUnit(UserOTP userOTP) {

    BusinessUserOTP businessUserOTP = new BusinessUserOTP();
    if (userOTP != null) {
      businessUserOTP.setId(userOTP.getId());
      businessUserOTP.setUserPhone(userOTP.getUserPhone());
      businessUserOTP.setFunctionality(userOTP.getFunctionality());
      businessUserOTP.setOtpMethod(userOTP.getOtpMethod());
      businessUserOTP.setCreatedAt(userOTP.getCreatedAt());
      businessUserOTP.setDeletedAt(userOTP.getDeletedAt());
      businessUserOTP.setUpdatedAt(userOTP.getUpdatedAt());
      businessUserOTP.setNextTrial(null);
      businessUserOTP.setSessionInfo(userOTP.getSessionInfo());
      businessUserOTP.setAssessmentId(userOTP.getAssessmentId());
      businessUserOTP.setContractType(userOTP.getContractType());
      businessUserOTP.setUserId(userOTP.getUserId());
      businessUserOTP.setOtp(userOTP.getOtp());
      return businessUserOTP;
    } else
      return null;

  }

  @Override
  protected BusinessUserOTP convertBasicUnitToBusinessUnit(UserOTP s, String language) {
    // TODO Auto-generated method stub
    return null;
  }

}
