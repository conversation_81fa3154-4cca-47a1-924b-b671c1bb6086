package innovitics.azimut.utilities.businessutilities.documents;

import java.io.IOException;
import java.util.Map.Entry;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.models.user.User;
import innovitics.azimut.services.user.UserService;
import innovitics.azimut.utilities.crosslayerenums.DocumentType;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.fileutilities.ParentStorage;
@Service
public class UserSingedPDFLocationService extends DocumentLocationService{
	
		@Autowired UserService userService;
		@Override
		public boolean isType(String type) {
			return StringUtility.stringsMatch(type, DocumentType.SIGNED_PDF.getType());
		}
		
		@Override
		public Entry<MediaType,byte[]> getFileAndExtension(ParentStorage storage,BusinessUser businessUser, String validityToken, Long documentId) throws IOException 
		{		User user=this.userService.findById(documentId);
				if(user!=null)
				{
					return storage.getFileWithAbsolutePath(storage.generateLocalPath(this.configProperties.getBlobSignedPdfPath(), user.getSignedPdf(),user.getPdfPath(),true,2L));
				}
				else
					return null;
		}
}
