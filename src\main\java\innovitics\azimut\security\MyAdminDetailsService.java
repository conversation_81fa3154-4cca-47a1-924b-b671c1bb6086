package innovitics.azimut.security;

import java.util.ArrayList;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import innovitics.azimut.businessmodels.admin.BusinessAdminUser;
import innovitics.azimut.businessservices.BusinessAdminUserService;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class MyAdminDetailsService extends MyUserDetailsService {

  @Autowired
  BusinessAdminUserService businessAdminUserService;

  @Override
  public BusinessUserHolder loadUserByUsername(String username) throws UsernameNotFoundException {

    BusinessAdminUser businessAdminUser = new BusinessAdminUser();
    try {
      businessAdminUser = this.businessAdminUserService.findAdminUserByUsername(username, true);
    } catch (BusinessException exception) {

      MyLogger.logStackTrace(exception);
    }

    return new BusinessUserHolder(businessAdminUser.getEmailAddress(), " ", new ArrayList<>(), businessAdminUser);
  }
}
