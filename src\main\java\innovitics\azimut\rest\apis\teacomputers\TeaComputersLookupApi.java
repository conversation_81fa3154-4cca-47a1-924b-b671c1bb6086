package innovitics.azimut.rest.apis.teacomputers;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.models.teacomputers.Bank;
import innovitics.azimut.models.teacomputers.Branch;
import innovitics.azimut.models.teacomputers.City;
import innovitics.azimut.models.teacomputers.Country;
import innovitics.azimut.models.teacomputers.Currency;
import innovitics.azimut.models.teacomputers.Nationality;
import innovitics.azimut.rest.entities.teacomputers.LookupRequest;
import innovitics.azimut.rest.entities.teacomputers.LookupResponse;
import innovitics.azimut.rest.entities.teacomputers.TeaComputerResponse;
import innovitics.azimut.services.teacomputer.TeaComputerService;
import innovitics.azimut.utilities.crosslayerenums.AzimutEntityType;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;

@Service
public class TeaComputersLookupApi extends RestTeaComputersApi<LookupRequest, LookupResponse[]> {

  @Autowired
  TeaComputerService teaComputerService;

  @Override
  protected String generateURL(String params) {
    return super.generateBaseURL() + "/lookups/" + params;
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.GET;
  }

  @Override
  protected String generateSignature(LookupRequest request) {
    if (request != null) {
      if (NumberUtility.areLongValuesMatching(request.getTypeId(), AzimutEntityType.COUNTRY.getTypeId())) {
        return this.teaComputersSignatureGenerator.generateSignature("Country");
      } else if (NumberUtility.areLongValuesMatching(request.getTypeId(), AzimutEntityType.CITY.getTypeId())) {
        return this.teaComputersSignatureGenerator.generateSignature("City");
      } else if (NumberUtility.areLongValuesMatching(request.getTypeId(), AzimutEntityType.NATIONALITY.getTypeId())) {
        return this.teaComputersSignatureGenerator.generateSignature("Nationality");
      } else if (NumberUtility.areLongValuesMatching(request.getTypeId(), AzimutEntityType.BRANCH.getTypeId())) {
        return this.teaComputersSignatureGenerator.generateSignature("Branches");
      } else if (NumberUtility.areLongValuesMatching(request.getTypeId(), AzimutEntityType.BANK.getTypeId())) {
        return this.teaComputersSignatureGenerator.generateSignature("Banks");
      } else if (NumberUtility.areLongValuesMatching(request.getTypeId(), AzimutEntityType.CURRENCY.getTypeId())) {
        return this.teaComputersSignatureGenerator.generateSignature("Currencies");
      }
    }
    return null;
  }

  @Override
  protected String generateResponseSignature(TeaComputerResponse teaComputerResponse) {
    return null;
  }

  public void sendSyncRequest(AzimutEntityType entityType) throws IntegrationException {
    LookupRequest request = new LookupRequest();
    request.setTypeId(entityType.getTypeId());
    request.setSignature(this.generateSignature(request));
    LookupResponse[] responses = this.getData(request, entityType.getParam());
    transferData(responses, entityType);
  }

  private void transferData(LookupResponse[] lookupResponses, AzimutEntityType entityType) {
    switch (entityType) {
      case COUNTRY:
        this.synchronizeCountries(lookupResponses);
        break;
      case CITY:
        this.synchronizeCities(lookupResponses);
        break;
      case NATIONALITY:
        this.synchronizeNationalities(lookupResponses);
        break;
      case BANK:
        this.synchronizeBanks(lookupResponses);
        break;
      case BRANCH:
        this.synchronizeBranches(lookupResponses);
        break;
      case CURRENCY:
        this.synchronizeCurrencies(lookupResponses);
        break;
      default:
        break;
    }
  }

  private void synchronizeCountries(LookupResponse[] lookUpResponses) {
    List<Country> countries = new ArrayList<Country>();
    this.teaComputerService.deleteAllCountries();
    for (LookupResponse lookUpResponse : lookUpResponses) {
      Country country = new Country();
      country.setArabicCountryName(lookUpResponse.getArabicCountryName());
      country.setEnglishCountryName(lookUpResponse.getEnglishCountryName());
      country.setCountryId(lookUpResponse.getCountryId());
      country.setSystemCountryCode(lookUpResponse.getSystemCountryCode());
      countries.add(country);
    }
    this.teaComputerService.saveAllCountries(countries);
  }

  private void synchronizeCities(LookupResponse[] lookupResponses) {
    this.teaComputerService.deleteAllCities();

    List<City> cities = new ArrayList<City>();
    for (LookupResponse lookupResponse : lookupResponses) {
      City city = new City();
      city.setArabicCityName(lookupResponse.getArabicCityName());
      city.setEnglishCityName(lookupResponse.getEnglishCityName());
      city.setCountryId(lookupResponse.getCountryId());
      city.setSystemCountryCode(lookupResponse.getSystemCountryCode());
      city.setCityId(lookupResponse.getCityId());
      city.setSystemCityCode(lookupResponse.getSystemCityCode());
      cities.add(city);
    }
    this.teaComputerService.saveAllCities(cities);
  }

  private void synchronizeNationalities(LookupResponse[] lookupResponses) {
    this.teaComputerService.deleteAllNationalities();

    List<Nationality> nationalities = new ArrayList<Nationality>();
    for (LookupResponse lookupResponse : lookupResponses) {
      Nationality nationality = new Nationality();
      nationality.setArabicNationalityName(lookupResponse.getArabicNationalityName());
      nationality.setEnglishNationalityName(lookupResponse.getEnglishNationalityName());
      nationality.setNationalityId(lookupResponse.getNationalityId());
      nationality.setSystemNationalityCode(lookupResponse.getSystemNationalityCode());

      nationalities.add(nationality);
    }
    this.teaComputerService.saveAllNationalities(nationalities);
  }

  private void synchronizeBanks(LookupResponse[] lookupResponses) {
    this.teaComputerService.deleteAllBanks();

    List<Bank> banks = new ArrayList<Bank>();
    for (LookupResponse lookupResponse : lookupResponses) {
      Bank bank = new Bank();
      bank.setArabicBankName(lookupResponse.getArabicBankName());
      bank.setEnglishBankName(lookupResponse.getEnglishBankName());
      bank.setBankType(StringUtility.isStringPopulated(lookupResponse.getBankType()) ? lookupResponse.getBankType()
          : "BANK"); // Default to BANK if not provided
      bank.setBankId(lookupResponse.getBankId());
      bank.setSystemBankCode(lookupResponse.getSystemBankCode());

      banks.add(bank);
    }
    this.teaComputerService.saveAllBanks(banks);
  }

  private void synchronizeBranches(LookupResponse[] lookupResponses) {
    this.teaComputerService.deleteAllBranches();

    List<Branch> branches = new ArrayList<Branch>();
    for (LookupResponse lookupResponse : lookupResponses) {
      Branch branch = new Branch();
      branch.setArabicBranchName(lookupResponse.getArabicBranchName());
      branch.setEnglishBranchName(lookupResponse.getEnglishBranchName());
      branch.setBankId(lookupResponse.getSystemBankCode());
      branch.setSystemBankCode(lookupResponse.getSystemBankCode());
      branch.setBranchId(lookupResponse.getBranchId());
      branch.setSystemBranchCode(lookupResponse.getSystemBranchCode());

      branches.add(branch);
    }
    this.teaComputerService.saveAllBranches(branches);
  }

  private void synchronizeCurrencies(LookupResponse[] lookupResponses) {
    this.teaComputerService.deleteAllCurrencies();

    List<Currency> currencies = new ArrayList<Currency>();
    for (LookupResponse lookupResponse : lookupResponses) {
      Currency currency = new Currency();
      currency.setArabicCurrencyName(lookupResponse.getArabicCurrencyName());
      currency.setEnglishCurrencyName(lookupResponse.getEnglishCurrencyName());
      currency.setCurrencyId(lookupResponse.getCurrencyId());
      currency.setSystemCurrencyCode(lookupResponse.getSystemCurrencyCode());

      currencies.add(currency);
    }
    this.teaComputerService.saveAllCurrencies(currencies);
  }
}
