package innovitics.azimut.controllers.users.DTOs;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import innovitics.azimut.rest.entities.teacomputers.FundTransactionResponse;
import innovitics.azimut.rest.entities.teacomputers.TransactionResponse;
import lombok.Data;

@Data
public class TeaComputersCallbackDto {
  @NotBlank(message = "notification type is required")
  private String notificationType;
  @NotNull(message = "idTypeId is required")
  private Long idTypeId;
  @NotBlank(message = "idNumber is required")
  private String idNumber;
  @NotBlank(message = "signature is required")
  private String signature;

  private FundTransactionResponse order;
  private TeaComputers<PERSON>allbackUser user;
  private TransactionResponse transaction;
}
