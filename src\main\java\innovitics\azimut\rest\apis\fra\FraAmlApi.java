package innovitics.azimut.rest.apis.fra;

import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.rest.RestBaseApi;
import innovitics.azimut.rest.entities.fra.FraAmlInput;
import innovitics.azimut.rest.entities.fra.FraAmlOutput;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class FraAmlApi extends RestBaseApi<FraAmlInput, FraAmlOutput> {

  @Override
  protected String generateURL(String params) {
    return this.configProperties.getFraUrl() + "/aml/validate";
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.POST;
  }

  @Override
  public HttpHeaders generateHeaders() {
    HttpHeaders headers = super.generateHeaders();
    headers.set("ApiKey", this.configProperties.getFraApiKey());
    return headers;
  }

  @Override
  protected void validateResponse(ResponseEntity<FraAmlOutput> responseEntity) throws IntegrationException {
    super.validateResponse(responseEntity);
    if (!responseEntity.getStatusCode().is2xxSuccessful()) {
      MyLogger.info("FraAmlApi: Error in response from FRA API" + responseEntity.getStatusCode() + " "
          + responseEntity.getBody());
      throw new IntegrationException(ErrorCode.FAILED_TO_INTEGRATE);
    }
  }

}
