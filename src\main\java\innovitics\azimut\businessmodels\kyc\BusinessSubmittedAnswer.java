package innovitics.azimut.businessmodels.kyc;

import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import lombok.Data;

@Data
public class BusinessSubmittedAnswer {

  private Long id;
  private Long parentAnswerId;
  private Long answerId;
  private String answerType;
  private String answerValue;
  private MultipartFile document;
  private String countryPhoneCode;
  private String countryCode;
  private Long questionId;
  private BusinessRelatedAnswer[] relatedAnswers;
  private List<BusinessSubmittedAnswer> relatedUserAnswers;
  protected String documentName;
  protected String documentSubDirectory;
  protected Double documentSize;
  protected String documentURL;

}
