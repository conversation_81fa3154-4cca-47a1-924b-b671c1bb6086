package innovitics.azimut.rest.apis.paytabs;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;

import innovitics.azimut.rest.entities.paytabs.PaytabsInitiatePaymentRequest;
import innovitics.azimut.rest.entities.paytabs.PaytabsInitiatePaymentResponse;
import innovitics.azimut.security.AES;
import innovitics.azimut.utilities.datautilities.StringUtility;
import org.springframework.stereotype.Service;

@Service
public class PaytabsInitiatePaymentApi extends
        RestPaytabsApi<PaytabsInitiatePaymentRequest, PaytabsInitiatePaymentResponse> {

    @Autowired
    AES aes;
    private static final String PATH = "/payment/request";

    @Override
    protected String generateURL(String params) {
        return super.generateBaseURL(params) + PATH;
    }

    @Override
    protected HttpMethod chooseHttpMethod() {
        return HttpMethod.POST;
    }

    public String generateSerial(PaytabsInitiatePaymentRequest initiatePaymentRequest) {
        if (!(initiatePaymentRequest != null && StringUtility.isStringPopulated(initiatePaymentRequest.getCartId())
                && initiatePaymentRequest.getCartAmount() != null)) {
            return null;
        }

        String amountWithoutDecimalPoint = StringUtility
                .generateAmountStringWithoutDecimalPoints(initiatePaymentRequest.getCartAmount());
        return aes.ecryptWithoutSpecialCharacters(amountWithoutDecimalPoint);
    }

}
