package innovitics.azimut.rest.apis.teacomputers;

import java.io.IOException;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;

import innovitics.azimut.businessmodels.trading.BaseAzimutTrading;
import innovitics.azimut.rest.entities.teacomputers.InjectRequest;
import innovitics.azimut.rest.entities.teacomputers.InjectResponse;
import innovitics.azimut.rest.entities.teacomputers.TeaComputerResponse;
import innovitics.azimut.utilities.datautilities.DateUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.fileutilities.MultipartInputStreamFileResource;
import innovitics.azimut.utilities.logging.MyLogger;

@Component
public class TeaComputersInjectApi extends RestTeaComputersApi<InjectRequest, InjectResponse> {
  @Override
  protected String generateURL(String params) {
    return super.generateBaseURL() + "/" + params;
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.POST;
  }

  @Override
  protected String generateSignature(InjectRequest request) {
    return this.teaComputersSignatureGenerator.generateSignature("", request.getIdTypeId().toString(),
        request.getIdNumber());
  }

  @Override
  protected String generateResponseSignature(TeaComputerResponse teaComputerResponse) {
    InjectResponse request = (InjectResponse) teaComputerResponse;
    return this.teaComputersSignatureGenerator.generateSignature("", request.getOrderId());
  }

  @Override
  public HttpHeaders generateHeaders() {
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.MULTIPART_FORM_DATA);
    return headers;
  }

  @Override
  protected HttpEntity<?> generateRequestFromInput(InjectRequest input) {
    this.populateCredentials(input);
    LinkedMultiValueMap<String, Object> map = new LinkedMultiValueMap<>();

    map.add("username", input.getUserName());
    map.add("password", input.getPassword());
    map.add("IdTypeId", input.getIdTypeId());
    map.add("IdNumber", input.getIdNumber());
    map.add("OrderDate", input.getOrderDate());
    map.add("ModuleTypeId", input.getModuleTypeId());
    map.add("CurrencyId", input.getCurrencyId());
    map.add("OrderValue", input.getOrderValue());
    map.add("AccountID", input.getAccountId());
    map.add("BankId", input.getBankId());
    map.add("Signature", input.getSignature());

    if (StringUtility.isStringPopulated(input.getReferenceNumber()))
      map.add("referenceNumber", input.getReferenceNumber());
    if (input.getFile() != null && !input.getFile().isEmpty())
      try {
        map.add("TicketDoc", new MultipartInputStreamFileResource(input.getFile().getInputStream(),
            input.getFile().getOriginalFilename()));
      } catch (IOException e) {
        MyLogger.logStackTrace(e);
      }

    return new HttpEntity<>(map, this.generateHeaders());
  }

  public InjectRequest generateInjectRequest(BaseAzimutTrading baseAzimutTrading) {
    InjectRequest request = new InjectRequest();
    request.setIdTypeId(baseAzimutTrading.getAzIdType());
    request.setIdNumber(baseAzimutTrading.getAzId());
    request.setOrderDate(DateUtility.getCurrentDayMonthYear());
    request.setModuleTypeId(baseAzimutTrading.getModuleTypeId());
    request.setOrderValue(baseAzimutTrading.getOrderValue());
    request.setAccountNo(baseAzimutTrading.getAccountId());
    request.setAccountId(baseAzimutTrading.getAccountId());
    request.setBankId(baseAzimutTrading.getBankId());
    request.setCurrencyId(baseAzimutTrading.getCurrencyId());
    request.setTicketDoc(baseAzimutTrading.getFileBytes());
    request.setReferenceNumber(baseAzimutTrading.getReferenceNo());
    request.setSignature(this.generateSignature(request));
    request.setFile(baseAzimutTrading.getInjectionDocument());

    return request;
  }
}
