package innovitics.azimut.controllers.payment.DTOs;

import javax.validation.constraints.NotNull;

import innovitics.azimut.businessmodels.BusinessPayment;
import lombok.Data;

@Data
public class InitiatePaymentDto {
  @NotNull(message = "orderValue is required")
  Double orderValue;

  @NotNull(message = "action is required")
  Integer action;

  @NotNull(message = "bankId is required")
  Long bankId;

  @NotNull(message = "accountId is required")
  Long accountId;

  @NotNull(message = "currencyId is required")
  Long currencyId;

  Boolean isMobile;

  String returnUrl;

  public BusinessPayment toBusinessPayment() {
    BusinessPayment businessPayment = new BusinessPayment();
    businessPayment.setIsMobile(isMobile);
    businessPayment.setAmount(orderValue);
    businessPayment.setAction(action);
    businessPayment.setBankId(bankId);
    businessPayment.setAccountId(accountId);
    businessPayment.setCurrencyId(currencyId);
    businessPayment.setReturnUrl(returnUrl);
    return businessPayment;
  }
}
