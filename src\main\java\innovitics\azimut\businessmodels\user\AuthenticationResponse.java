package innovitics.azimut.businessmodels.user;

import innovitics.azimut.utilities.CustomJsonRootName;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Setter;

@Data
@CustomJsonRootName(plural = "credentials", singular = "credentials")
public class AuthenticationResponse<T> {

  private Token token;
  private T user;
  private int flowId;

  @Setter(AccessLevel.NONE)
  protected BusinessFlow businessFlow;

  public AuthenticationResponse(Token token, T user) {
    this.token = token;
    this.user = user;
  }

  public AuthenticationResponse() {
  }

  public void setBusinessFlow(BusinessFlow businessFlow) {
    this.businessFlow = businessFlow;
    this.flowId = businessFlow.getFlowId();
  }

}
