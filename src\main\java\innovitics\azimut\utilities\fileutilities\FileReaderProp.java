package innovitics.azimut.utilities.fileutilities;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

import innovitics.azimut.utilities.logging.MyLogger;

public final class FileReaderProp {

  public static String readUnicodeJava11(String fileName) {

    StringBuffer stringBuffer = new StringBuffer();
    try (FileReader fr = new FileReader(fileName, StandardCharsets.UTF_8);
        BufferedReader reader = new BufferedReader(fr)) {

      String str;
      while ((str = reader.readLine()) != null) {
        stringBuffer.append(str);
        System.out.println(str);
      }

    } catch (IOException e) {
      MyLogger.logStackTrace(e);
    }
    return stringBuffer.toString();
  }
}
