package innovitics.azimut.services;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import innovitics.azimut.models.Fund;
import innovitics.azimut.models.azimutdetails.FundPrice;
import innovitics.azimut.repositories.fund.FundPriceDynamicRepository;
import innovitics.azimut.repositories.fund.FundRepository;
import innovitics.azimut.utilities.dbutilities.SearchCriteria;
import innovitics.azimut.utilities.dbutilities.SearchOperation;
import innovitics.azimut.utilities.dbutilities.specifications.child.FundChildSpecification;
import innovitics.azimut.utilities.dbutilities.specifications.child.FundPriceChildSpecification;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class FundService extends AbstractService<Fund, Long> {

  private @Autowired FundRepository fundRepository;

  private @Autowired FundPriceDynamicRepository fundPriceDynamicRepository;

  private @Autowired FundPriceChildSpecification fundPriceChildSpecification;

  private @Autowired FundChildSpecification fundChildSpecification;

  public Optional<Fund> getFundsByTeacomputerId(Long teacomputerIds) {
    Specification<Fund> spec = (root, query, cb) -> cb.equal(root.get("teacomputerId"), teacomputerIds);
    return fundRepository.findOne(spec);
  }

  public Fund save(Fund fund) {
    MyLogger.info("Persisting:: Fund:::" + fund.toString());
    return this.fundRepository.save(fund);

  }

  public List<Fund> getAllFunds() {
    return this.fundRepository.findAll();
  }

  public List<Fund> getFundsWithoutNavs() {
    return this.fundRepository.getFundsWithoutNavs();
  }

  public List<FundPrice> getAllFundPrices() {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();

    Long[] teacomputerIds = new Long[] { 110l, 111l, 113l };

    searchCriteriaList.add(new SearchCriteria("teacomputerId",
        this.arrayUtility.generateObjectListFromObjectArray(teacomputerIds), SearchOperation.IN, null));

    return this.fundPriceDynamicRepository.findAll(this.fundPriceChildSpecification.findByCriteria(searchCriteriaList));
  }

  public List<FundPrice> getAllRelevantFundPrices(Long[] teacomputerFundIds) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();

    // Long[] teacomputerIds=new Long[] {110l,111l,113l};

    searchCriteriaList.add(new SearchCriteria("teacomputerId",
        this.arrayUtility.generateObjectListFromObjectArray(teacomputerFundIds), SearchOperation.IN, null));

    return this.fundPriceDynamicRepository.findAll(this.fundPriceChildSpecification.findByCriteria(searchCriteriaList));
  }

  public List<Fund> getAllRelevantFundLogos(Long[] teacomputerFundIds) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();

    searchCriteriaList.add(new SearchCriteria("teacomputerId",
        this.arrayUtility.generateObjectListFromObjectArray(teacomputerFundIds), SearchOperation.IN, null));

    return this.fundRepository.findAll(this.fundChildSpecification.findByCriteria(searchCriteriaList));
  }

}
