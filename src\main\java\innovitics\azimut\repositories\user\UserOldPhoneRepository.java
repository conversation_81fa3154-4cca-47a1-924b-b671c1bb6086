package innovitics.azimut.repositories.user;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import innovitics.azimut.models.user.UserOldPhone;

@Repository
public interface UserOldPhoneRepository
    extends JpaRepository<UserOldPhone, Long>, JpaSpecificationExecutor<UserOldPhone> {

  int countByUserIdAndDeletedAtIsNull(Long userId);

  void deleteByUserId(Long userId);

  List<UserOldPhone> findByUserId(Long id);

  Optional<UserOldPhone> findByUserPhone(String oldUserPhone);

}
