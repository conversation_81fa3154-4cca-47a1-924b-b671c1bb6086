package innovitics.azimut.services.admin;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import innovitics.azimut.models.admin.AdminUser;
import innovitics.azimut.repositories.admin.AdminUserDynamicRepository;
import innovitics.azimut.security.AES;
import innovitics.azimut.services.AbstractService;
import innovitics.azimut.utilities.dbutilities.SearchCriteria;
import innovitics.azimut.utilities.dbutilities.SearchOperation;
import innovitics.azimut.utilities.dbutilities.specifications.child.AdminUserSpecification;

@Service
public class AdminUserService extends AbstractService<AdminUser, String> {

  @Autowired
  AdminUserDynamicRepository adminUserDynamicRepository;
  @Autowired
  AdminUserSpecification adminUserSpecification;
  @Autowired
  protected AES aes;

  public AdminUser findById(Long id) {
    return adminUserDynamicRepository.getById(id);
  }

  public List<AdminUser> getAllAdminUsers() {
    var users = this.adminUserDynamicRepository.findAll();
    return users.stream().filter(user -> user.getDeletedAt() == null).collect(Collectors.toList());
  }

  public AdminUser findByUserName(String username) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("email", username, SearchOperation.EQUAL, null));
    // searchCriteriaList.add(new SearchCriteria("roleId", 1L,SearchOperation.EQUAL,
    // null));
    return this.adminUserDynamicRepository.findOne(this.adminUserSpecification.findByCriteria(searchCriteriaList))
        .get();

  }

  public AdminUser findByUserNameAndPassword(String email, String password) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("email", email, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("password", this.aes.encrypt(password), SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", null, SearchOperation.IS_NULL, null));
    return this.adminUserDynamicRepository.findOne(this.adminUserSpecification.findByCriteria(searchCriteriaList))
        .get();
  }

  public AdminUser addUser(AdminUser adminUser) {

    return this.adminUserDynamicRepository.save(adminUser);
  }

  public void deleteUser(Long id) {
    var adminUser = this.findById(id);
    adminUser.setDeletedAt(new Date());
    this.adminUserDynamicRepository.save(adminUser);
  }

  public AdminUser editAdminUser(AdminUser adminUser) {
    return this.adminUserDynamicRepository.save(adminUser);
  }

}
