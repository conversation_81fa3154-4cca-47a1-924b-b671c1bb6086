package innovitics.azimut.businessmodels.negativelist;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import innovitics.azimut.businessmodels.BaseBusinessEntity;
import innovitics.azimut.utilities.CustomJsonRootName;
import lombok.Data;

@Data
@CustomJsonRootName(plural = "negativeListEntries", singular = "negativeListEntry")
public class BusinessNegativeList extends BaseBusinessEntity {
    
    private Long id;
    private String fullName;
    private String firstName;
    private String lastName;
    private String alias;
    private String nationality;
    private String dateOfBirth;
    private String placeOfBirth;
    private String passportNumber;
    private String nationalId;
    private String listSource;
    private String listType;
    private String referenceNumber;
    private String additionalInfo;
    private Boolean isActive;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
    private Date lastUpdated;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
    private Date createdAt;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
    private Date updatedAt;
}
