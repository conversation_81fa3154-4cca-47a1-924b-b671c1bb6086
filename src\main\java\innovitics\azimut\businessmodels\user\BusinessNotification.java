package innovitics.azimut.businessmodels.user;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

@Data
public class BusinessNotification {

  protected Long id;
  private String notificationText;
  private String notificationSource;
  private String notificationHeader;
  private String link;
  private Long userId;
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  private Date createdAt;
  private Boolean isRead;
  private Integer path;
  private Long fundId;
  private String notificationType;

}
