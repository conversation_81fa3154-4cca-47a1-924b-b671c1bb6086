package innovitics.azimut.models.user;

import java.util.Date;

import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import innovitics.azimut.models.DbBaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler", "file" })
@Entity
@Table(name = "user_images")
@NamedEntityGraph(name = "UserImage.details")
@Setter
@Getter
@ToString
public class UserImage extends DbBaseEntity {
  private String userPhone;
  private String userImage;
  private Integer imageType;
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  private Date createdAt;
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  private Date updatedAt;
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  private Date deletedAt;
  private String imageUrl;
  private String imageSubDirectory;
  private String imageName;
  private String transactionId;
  private String privateUrl;
  @Transient
  private MultipartFile file;

  @ManyToOne
  @JoinColumn(name = "user_id", foreignKey = @javax.persistence.ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
  private User user;

  private Long userIdType;

}
