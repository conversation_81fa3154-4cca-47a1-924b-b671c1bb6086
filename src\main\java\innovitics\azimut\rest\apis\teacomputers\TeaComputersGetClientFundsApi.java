package innovitics.azimut.rest.apis.teacomputers;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import innovitics.azimut.businessmodels.funds.BusinessClientFund;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.controllers.users.DTOs.GetBalanceAndFundOwnershipDto;
import innovitics.azimut.rest.entities.teacomputers.ClientFundResponse;
import innovitics.azimut.rest.entities.teacomputers.GetClientFundsRequest;
import innovitics.azimut.rest.entities.teacomputers.TeaComputerResponse;
import innovitics.azimut.utilities.crosslayerenums.CurrencyType;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;

@Component
public class TeaComputersGetClientFundsApi extends RestTeaComputersApi<GetClientFundsRequest, ClientFundResponse[]> {
  public final static String PATH = "/GetClientBalance";

  @Override
  protected String generateURL(String params) {
    return super.generateBaseURL() + PATH;
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.POST;
  }

  @Override
  protected String generateSignature(GetClientFundsRequest getClientFundsRequest) {
    if (getClientFundsRequest.getFundId() != null) {
      return this.teaComputersSignatureGenerator.generateSignature(true,
          getClientFundsRequest.getUserName() != null ? getClientFundsRequest.getUserName()
              : this.configProperties.getTeaComputersKey(),
          getClientFundsRequest.getIdTypeId().toString(),
          getClientFundsRequest.getIdNumber(),
          getClientFundsRequest.getFundId().toString());
    } else {
      return this.teaComputersSignatureGenerator.generateSignature(true,
          getClientFundsRequest.getUserName() != null ? getClientFundsRequest.getUserName()
              : this.configProperties.getTeaComputersKey(),
          getClientFundsRequest.getIdTypeId().toString(),
          getClientFundsRequest.getIdNumber());
    }
  }

  @Override
  protected String generateResponseSignature(GetClientFundsRequest request, TeaComputerResponse teaComputerResponse) {
    ClientFundResponse clientFundResponse = (ClientFundResponse) teaComputerResponse;
    return this.teaComputersSignatureGenerator.generateSignature(true,
        request.getUserName() != null ? request.getUserName() : this.configProperties.getTeaComputersKey(),
        "", clientFundResponse.getMobile(),
        clientFundResponse.getTradePrice());
  }

  @Override
  protected String generateResponseSignature(TeaComputerResponse teaComputerResponse) {
    // not used
    ClientFundResponse clientFundResponse = (ClientFundResponse) teaComputerResponse;

    return this.teaComputersSignatureGenerator.generateSignature("", clientFundResponse.getMobile(),
        clientFundResponse.getTradePrice());
  }

  public GetClientFundsRequest createRequest(BusinessUser tokenizedBusinessUser,
      GetBalanceAndFundOwnershipDto businessAzimutClient, String partnerUsername) {
    GetClientFundsRequest request = new GetClientFundsRequest();

    request.setIdTypeId(tokenizedBusinessUser.getAzimutIdTypeId());
    request.setIdNumber(tokenizedBusinessUser.getUserId());
    request.setFundId(businessAzimutClient.getTeacomputerId());
    if (partnerUsername != null)
      request.setUserName(partnerUsername);
    request.setSignature(this.generateSignature(request));
    return request;
  }

  public List<BusinessClientFund> getFundsFromResponse(ClientFundResponse[] getClientFundsResponses, String language) {
    List<BusinessClientFund> businessClientFunds = new ArrayList<BusinessClientFund>();

    if (getClientFundsResponses == null)
      return businessClientFunds;

    for (ClientFundResponse response : getClientFundsResponses) {
      if (response == null || !StringUtility.isStringPopulated(response.getCertificateName()))
        continue;

      BusinessClientFund businessClientFund = new BusinessClientFund();
      long currencyId = Long.parseLong(response.getCurrencyID());
      double priceValue = 0;
      CurrencyType currency = CurrencyType.getById(currencyId);

      businessClientFund.setTeacomputerId(Long.parseLong(response.getFundID()));
      businessClientFund.setFundType(response.getAssetClass());
      businessClientFund.setFundName(response.getCertificateName());

      businessClientFund.setCurrencyName(response.getCurrencyName());
      businessClientFund.setCurrencyId(currencyId);

      if (currency != null) {
        businessClientFund
            .setCurrencyName(StringUtility.stringsMatch(language, "ar") ? currency.getTypeAr() : currency.getType());
      } else {
        businessClientFund.setCurrencyName(response.getCurrencyName());
      }

      businessClientFund.setCurrencyRate(Double.parseDouble(response.getCurrencyRate()));
      businessClientFund.setTradePrice(response.getTradePrice());
      businessClientFund.setAvgcost(response.getAvgcost());
      businessClientFund.setQuantity(response.getQuantity());
      businessClientFund.setAvailableToSell(response.getAvailableToSell());

      if (StringUtility.isStringPopulated(response.getTradePrice())
          && response.getQuantity() != null && response.getCurrencyRate() != null) {

        priceValue = (response.getQuantity()
            * Double.parseDouble(response.getTradePrice()));
      }
      businessClientFund.setTotalAmount(NumberUtility.changeFormat(new BigDecimal(priceValue)));

      businessClientFunds.add(businessClientFund);
    }

    return businessClientFunds;
  }
}
