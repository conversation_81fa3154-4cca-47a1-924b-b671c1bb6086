package innovitics.azimut.businessmodels.kyc;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import innovitics.azimut.businessmodels.BaseBusinessEntity;
import lombok.Data;

@Data
public class BusinessUserType extends BaseBusinessEntity {

  private String idType;
  private int noOfKYCPages;
  private int valifyImageCount;
  private List<BusinessKYCPage> pages = new ArrayList<BusinessKYCPage>();
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  protected Date deletedAt;
}
