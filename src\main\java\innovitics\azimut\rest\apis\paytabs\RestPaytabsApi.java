package innovitics.azimut.rest.apis.paytabs;

import java.util.Date;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.HttpClientErrorException;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.rest.RestBaseApi;
import innovitics.azimut.rest.entities.paytabs.PaytabsRequest;
import innovitics.azimut.rest.entities.paytabs.PaytabsResponse;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;
import lombok.Data;

@Data
public abstract class RestPaytabsApi<I extends PaytabsRequest, O extends PaytabsResponse>
    extends RestBaseApi<I, O> {

  private static final String TRANSACTION_TYPE = "sale";
  private static final String TRANSACTION_CLASS = "ecom";

  private String locale;

  protected String generateBaseURL(String params) {
    return this.configProperties.getPaytabsUrl();
  }

  @Override
  public IntegrationException handleException(Exception exception) {
    return this.validateExceptionType(exception);
  }

  @Override
  public HttpHeaders generateHeaders() {
    HttpHeaders headers = super.generateHeaders();
    headers.add(StringUtility.PAYTABS_AUTHORIZATION_HEADER, this.configProperties.getPaytabsServerKey());
    headers.add("lang", locale);
    MyLogger.info("Generated Headers:::" + headers.toString());
    return headers;
  }

  protected IntegrationException validateExceptionType(Exception exception) {
    MyLogger.info("Stack trace:::");

    MyLogger.logStackTrace(exception);

    if (exception instanceof IntegrationException) {
      return (IntegrationException) exception;
    }

    if (exception instanceof HttpClientErrorException) {

      IntegrationException integrationException = this.handleError((HttpClientErrorException) exception);
      return integrationException;
    }

    return new IntegrationException(ErrorCode.FAILED_TO_INTEGRATE);

  }

  @Override
  public IntegrationException handleError(HttpClientErrorException httpClientErrorException) {
    MyLogger.info("httpClientErrorException:::" + httpClientErrorException.toString());
    int errorCode = ErrorCode.FAILED_TO_INTEGRATE.getCode();
    String errorMessage = "";
    PaytabsResponse paytabsResponse = new PaytabsResponse();
    ObjectMapper mapper = new ObjectMapper();
    try {
      MyLogger.info("Parsing the exception to the teaComputerResponse:::");
      MyLogger.info("httpClientErrorException.getResponseBodyAsString():::"
          + httpClientErrorException.getResponseBodyAsString());
      paytabsResponse = mapper.readValue(httpClientErrorException.getResponseBodyAsString(),
          PaytabsResponse.class);
      errorMessage = paytabsResponse.getMessage();
      errorCode = paytabsResponse.getCode();

      MyLogger.info("paytabsResponse:::" + paytabsResponse.toString());
    } catch (JsonProcessingException e) {
      MyLogger.info("Failed to Parse:::");
      MyLogger.logStackTrace(e);
      return new IntegrationException(ErrorCode.FAILED_TO_INTEGRATE);
    }

    IntegrationException integrationException = new IntegrationException(errorCode, new Date(), errorMessage,
        errorMessage, errorMessage, httpClientErrorException.getStackTrace());
    return integrationException;
  }

  @Override
  public void validateResponse(ResponseEntity<O> responseEntity) throws IntegrationException {
    if (!this.validateResponseStatus(responseEntity)) {
      throw new IntegrationException(ErrorCode.FAILED_TO_INTEGRATE);
    }
  }

  @Override
  protected HttpEntity<?> generateRequestFromInput(I input) {
    this.populateCredentials(input);
    return this.stringify(input);
  };

  private void populateCredentials(PaytabsRequest request) {
    request.setProfileId(Integer.valueOf(this.configProperties.getPaytabsProfileId()));
    request.setTransType(TRANSACTION_TYPE);
    request.setTransClass(TRANSACTION_CLASS);
  }
}
