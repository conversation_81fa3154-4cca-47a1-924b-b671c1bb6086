package innovitics.azimut.controllers.users;

import java.io.IOException;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import innovitics.azimut.businessmodels.user.AzimutAccount;
import innovitics.azimut.businessmodels.user.BusinessAzimutClient;
import innovitics.azimut.businessservices.BusinessClientDetailsChildService;
import innovitics.azimut.controllers.BaseController;
import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.controllers.GenericResponseHandler;
import innovitics.azimut.controllers.users.DTOs.CheckAccountDto;
import innovitics.azimut.controllers.users.DTOs.GetAzimutClientBankAccountsDto;
import innovitics.azimut.controllers.users.DTOs.GetAzimutLookUpDataDto;
import innovitics.azimut.controllers.users.DTOs.GetBalanceAndFundOwnershipDto;
import innovitics.azimut.controllers.users.DTOs.GetRequestStatementDto;
import innovitics.azimut.controllers.users.DTOs.HoldClientBankAccountDto;
import innovitics.azimut.controllers.users.DTOs.RemoveClientBankAccountDto;
import innovitics.azimut.controllers.users.DTOs.SaveClientBankAccountsDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.logging.MyLogger;

@RestController
@RequestMapping(value = "/api/azimut/user", produces = {
    MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE })
public class AzimutClientController extends BaseController {

  @Autowired
  protected BusinessClientDetailsChildService businessClientDetailsService;

  @Autowired
  GenericResponseHandler<BusinessAzimutClient> businessAzimutClientResponseHandler;

  @PostMapping(value = "/getAzimutClientBalanceAndTransactions", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessAzimutClient>> getAzimutClientBalanceAndTransactions(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody GetBalanceAndFundOwnershipDto searchBusinessAzimutClient)
      throws BusinessException, IOException, IntegrationException {
    MyLogger.info("SearchBusinessAzmiutClient::" + searchBusinessAzimutClient);
    return businessAzimutClientResponseHandler.generateBaseGenericResponse(BusinessAzimutClient.class,
        this.businessClientDetailsService
            .getBalanceAndTransactions(searchBusinessAzimutClient, this.getCurrentRequestHolder(token), language),
        null, null);
  }

  @GetMapping(value = "/getTemporaryAzimutClientBankAccounts")
  protected ResponseEntity<BaseGenericResponse<BusinessAzimutClient>> getTemporaryAzimutClientBankAccounts(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language)
      throws BusinessException, IOException, IntegrationException {
    return businessAzimutClientResponseHandler.generateBaseGenericResponse(BusinessAzimutClient.class,
        this.businessClientDetailsService
            .getTemporaryClientBankAccountDetails(this.getCurrentRequestHolder(token), language),
        null, null);

  }

  @PostMapping(value = "/getAzimutClientBankAccounts", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessAzimutClient>> getAzimutClientBankAccounts(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody GetAzimutClientBankAccountsDto searchBusinessAzimutClient)
      throws BusinessException, IOException, IntegrationException {
    Long accountId = (searchBusinessAzimutClient != null && searchBusinessAzimutClient.getAccountId() != null)
        ? searchBusinessAzimutClient.getAccountId().longValue()
        : null;
    return businessAzimutClientResponseHandler.generateBaseGenericResponse(BusinessAzimutClient.class,
        this.businessClientDetailsService.getBankAccountsWithDetails(searchBusinessAzimutClient,
            this.getCurrentRequestHolder(token), accountId == null, accountId),
        null, null);
  }

  @PostMapping(value = "/checkAccount", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessAzimutClient>> checkAzimutAccount(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody CheckAccountDto searchBusinessAzimutClient)
      throws BusinessException, IOException, IntegrationException {
    return businessAzimutClientResponseHandler.generateBaseGenericResponse(BusinessAzimutClient.class,
        this.businessClientDetailsService
            .checkAccountAtTeaComputers(searchBusinessAzimutClient, this.getCurrentRequestHolder(token)),
        null, null);
  }

  @PostMapping(value = "/saveTeacomputersAccountData", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessAzimutClient>> saveTeaComputersAccountData(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @RequestBody AzimutAccount azimutAccount) throws BusinessException, IOException, IntegrationException {
    return businessAzimutClientResponseHandler.generateBaseGenericResponse(BusinessAzimutClient.class,
        this.businessClientDetailsService
            .saveTeaComputersAccountData(azimutAccount, this.getCurrentRequestHolder(token)),
        null, null);
  }

  @PostMapping(value = "/getAzimutLookUpData", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessAzimutClient>> getAzimutLookupData(
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody GetAzimutLookUpDataDto businessAzimutDataLookup)
      throws BusinessException, IOException, IntegrationException {
    return businessAzimutClientResponseHandler.generateBaseGenericResponse(BusinessAzimutClient.class,
        this.businessClientDetailsService.getAzimutLookupData(businessAzimutDataLookup, null), null, null);

  }

  @PostMapping(value = "/synchronizeTeaComputersData", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessAzimutClient>> getTeaComputersLookupData(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language)
      throws BusinessException, IOException, IntegrationException {
    return businessAzimutClientResponseHandler.generateBaseGenericResponse(BusinessAzimutClient.class,
        this.businessClientDetailsService
            .synchronizeTeaComputersLookupData(this.getCurrentRequestHolder(token)),
        null, null);
  }

  @PostMapping(value = "/saveClientBankAccounts", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessAzimutClient>> saveClientBankAccounts(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody SaveClientBankAccountsDto businessAzimutClient)
      throws BusinessException, IOException, IntegrationException {
    return businessAzimutClientResponseHandler.generateBaseGenericResponse(BusinessAzimutClient.class,
        this.businessClientDetailsService
            .saveClientBankAccounts(businessAzimutClient, this.getCurrentRequestHolder(token), language),
        null, null);
  }

  @PostMapping(value = "/removeClientBankAccount", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessAzimutClient>> removeClientBankAccount(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody RemoveClientBankAccountDto businessClientBankAccountDetails)
      throws BusinessException, IOException, IntegrationException {
    return businessAzimutClientResponseHandler.generateBaseGenericResponse(BusinessAzimutClient.class,
        this.businessClientDetailsService.removeClientBankAccount(businessClientBankAccountDetails,
            this.getCurrentRequestHolder(token)),
        null, null);

  }

  @PostMapping(value = "/holdClientBankAccount", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessAzimutClient>> holdClientBankAccount(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody HoldClientBankAccountDto businessAzimutClient)
      throws BusinessException, IOException, IntegrationException {
    return businessAzimutClientResponseHandler.generateBaseGenericResponse(BusinessAzimutClient.class,
        this.businessClientDetailsService
            .holdClientBankAccount(this.getCurrentRequestHolder(token), businessAzimutClient),
        null, null);

  }

  @GetMapping(value = "/getAzimutDetails")
  protected ResponseEntity<BaseGenericResponse<BusinessAzimutClient>> getAzimutDetails(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language)
      throws BusinessException, IOException, IntegrationException {
    return businessAzimutClientResponseHandler.generateBaseGenericResponse(BusinessAzimutClient.class,
        this.businessClientDetailsService.getAzimutDetails(), null, null);

  }

  @PostMapping(value = "/getClientFunds", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessAzimutClient>> getClientFunds(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody GetBalanceAndFundOwnershipDto businessAzimutClient)
      throws BusinessException, IOException, IntegrationException {
    return businessAzimutClientResponseHandler.generateBaseGenericResponse(BusinessAzimutClient.class,
        this.businessClientDetailsService
            .getPaginatedClientFunds(this.getCurrentRequestHolder(token), businessAzimutClient, language),
        null, null);

  }

  @PostMapping(value = "/getPaginatedClientFunds", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessAzimutClient>> getPaginatedClientFunds(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody GetBalanceAndFundOwnershipDto businessAzimutClient)
      throws BusinessException, IOException, IntegrationException {
    return businessAzimutClientResponseHandler.generateBaseGenericResponse(BusinessAzimutClient.class,
        this.businessClientDetailsService
            .getPaginatedClientFunds(this.getCurrentRequestHolder(token), businessAzimutClient, language),
        null, null);
  }

  @GetMapping(value = "/getEportfolio")
  protected ResponseEntity<BaseGenericResponse<BusinessAzimutClient>> getEportfolio(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language)
      throws IOException, BusinessException, IntegrationException {
    return businessAzimutClientResponseHandler.generateBaseGenericResponse(BusinessAzimutClient.class,
        this.businessClientDetailsService.getEportfolio(this.getCurrentRequestHolder(token), language), null, null);
  }

  @GetMapping(value = "/getValuationReport")
  protected ResponseEntity<BaseGenericResponse<BusinessAzimutClient>> getValuationReport(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language)
      throws IOException, BusinessException, IntegrationException {
    return businessAzimutClientResponseHandler.generateBaseGenericResponse(BusinessAzimutClient.class,
        this.businessClientDetailsService.getValuationReport(this.getCurrentRequestHolder(token), language), null,
        null);
  }

  @PostMapping(value = "/getRequestStatement", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessAzimutClient>> getRequestStatement(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody GetRequestStatementDto businessAzimutClient)
      throws BusinessException, IOException, IntegrationException {
    return businessAzimutClientResponseHandler.generateBaseGenericResponse(BusinessAzimutClient.class,
        this.businessClientDetailsService
            .getRequestStatement(this.getCurrentRequestHolder(token), language, businessAzimutClient),
        null, null);

  }

  @GetMapping(value = "/getCompanyBankAccounts")
  protected ResponseEntity<BaseGenericResponse<BusinessAzimutClient>> getCompanyBankAccounts(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @RequestParam(name = "currencyId", required = false) Long currencyId)
      throws IOException, BusinessException, IntegrationException {
    return businessAzimutClientResponseHandler.generateBaseGenericResponse(BusinessAzimutClient.class,
        this.businessClientDetailsService.getCompanyBankAccounts(currencyId, language), null, null);
  }

  @PostMapping(value = "/getBalanceAndFundOwnership", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessAzimutClient>> getBalanceAndFundOwnership(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody GetBalanceAndFundOwnershipDto businessAzimutClient)
      throws BusinessException, IOException, IntegrationException {
    return businessAzimutClientResponseHandler.generateBaseGenericResponse(BusinessAzimutClient.class,
        this.businessClientDetailsService
            .getBalanceAndFundOwnership(this.getCurrentRequestHolder(token), language, businessAzimutClient),
        null, null);
  }

  @GetMapping(value = "/getDocuments")
  protected ResponseEntity<BaseGenericResponse<BusinessAzimutClient>> getDocuments(
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @RequestParam String documentName

  ) throws BusinessException, IOException, IntegrationException {
    return businessAzimutClientResponseHandler.generateBaseGenericResponse(BusinessAzimutClient.class,
        this.businessClientDetailsService.getDocuments(documentName), null, null);
  }
}
