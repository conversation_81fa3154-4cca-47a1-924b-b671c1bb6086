package innovitics.azimut.controllers.users.DTOs;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.Data;

@Data
public class UpdatePasswordDto {
  @NotBlank(message = "Password is required.")
  String password;

  @NotBlank(message = "New password is required.")
  String newPassword;

  @NotNull(message = "Id is required.")
  Long id;

  @NotNull(message = "OTP is required.")
  String otp;
}
