package innovitics.azimut.filters;
import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletResponse;

import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

@Component
@Order(value = Ordered.HIGHEST_PRECEDENCE)
public class CustomCorsFilter implements Filter {
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        // Allow all origins
        httpResponse.setHeader("Access-Control-Allow-Origin", "*");
        // Allow specific headers in addition to the standard headers
        httpResponse.setHeader("Access-Control-Allow-Headers","Content-Type, X-Requested-With, Accept, Authorization, lang, signature");
        httpResponse.setStatus(HttpStatus.OK.value());
        chain.doFilter(request, response);
    }
}