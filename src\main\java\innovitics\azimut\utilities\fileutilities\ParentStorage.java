package innovitics.azimut.utilities.fileutilities;

import java.io.IOException;
import java.io.InputStream;
import java.util.Map.Entry;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.multipart.MultipartFile;

import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.security.FileTokenizationUtility;
import innovitics.azimut.utilities.ParentUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;

public abstract class ParentStorage extends ParentUtility {
  @Autowired
  FileTokenizationUtility fileTokenizationUtility;

  public abstract BlobData uploadFile(MultipartFile file, boolean generateSasToken, String containerName,
      String subDirectory, boolean useOriginalFileName, Object... options) throws IOException, BusinessException;

  public abstract String generateFileRetrievalUrl(String path, String fileName, String subDirectory,
      boolean generateWithToken, Object... options) throws IOException;

  public abstract String generateFileRetrievalUrl(String path, String fileName, String subDirectory,
      boolean generateWithToken, Long tokenValidityMinutes, Object... options) throws IOException;

  public abstract String deleteFile(String path, String fileName, String subDirectory, boolean generateWithToken,
      Long tokenValidityMinutes) throws IOException;

  public abstract Entry<MediaType, byte[]> getFileWithAbsolutePath(String path) throws IOException;

  public abstract BlobData uploadFileToBlob(InputStream inputStream, boolean generateSasToken, String containerName,
      String subDirectory, String extension) throws IOException, BusinessException;

  public abstract void emptySubDirectory(String containerName, String subDirectory)
      throws IOException, BusinessException;

  public abstract BlobData uploadFileToBlob(String fileName, InputStream inputStream, boolean generateSasToken,
      String containerName, String subDirectory, String extension) throws IOException, BusinessException;

  public String generateRandomName(MultipartFile file) {
    // there is a problem here with non standard filenames (split . is not working )
    String extension = StringUtility.generateSubStringStartingFromCertainIndex(file.getOriginalFilename(), '.');
    UUID uuid = UUID.randomUUID();
    return uuid.toString() + extension;
  }

  public String generateRandomName(String extension) {
    UUID uuid = UUID.randomUUID();
    return uuid.toString() + "." + extension;
  }

  public abstract String generateLocalPath(String path, String fileName, String subDirectory, boolean generateWithToken,
      Long tokenValidityMinutes);

  protected abstract void copyFile(String sourceContainerName, String destinationContainerName, String subDirectory,
      String fileName, boolean generateWithToken) throws IOException;

  protected abstract void copyFileAbsolutePath(String sourcePath, String destinationPath) throws IOException;

  public String encrypt(String value) {
    return this.aes.encrypt(value);
  }

  public String decrypt(String value) {
    return this.aes.decrypt(StringUtility.removeEmptyCharacters(value));
  }

}
