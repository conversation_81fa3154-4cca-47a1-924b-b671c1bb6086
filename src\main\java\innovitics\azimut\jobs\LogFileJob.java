package innovitics.azimut.jobs;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class LogFileJob extends ParentJob {

  @Override
  @Scheduled(cron = "00 01 00 * * *")
  public void scheduleFixedDelayTask() {
    super.scheduleFixedDelayTask();
    /*
     * try
     * {
     * Date today=new Date();
     * 
     * Calendar cal = Calendar.getInstance();
     * cal.setTime(today);
     * cal.add(Calendar.DAY_OF_MONTH, -1);
     * Date yesterday=cal.getTime();
     * String
     * modifiedFormat=DateUtility.changeStringDateFormat(DateUtility.getDayMonthYear
     * (yesterday), new SimpleDateFormat("dd-MM-yyyy"),new
     * SimpleDateFormat("d-M-yyyy"));
     * String
     * fileName=(ApplicationContextProvider.getApplicationContext().getBeansOfType(
     * ConfigProperties.class)).get(
     * "innovitics.azimut.configproperties.ConfigProperties").getLogFilePath()
     * +"application-"+modifiedFormat+".log";
     * 
     * FileOutputStream fos = new FileOutputStream(fileName.substring(0,
     * fileName.length() - ".log".length())+".zip");
     * ZipOutputStream zipOut = new ZipOutputStream(fos);
     * 
     * File fileToZip = new File(fileName);
     * FileInputStream fis = new FileInputStream(fileToZip);
     * ZipEntry zipEntry = new ZipEntry(fileToZip.getName());
     * zipOut.putNextEntry(zipEntry);
     * 
     * byte[] bytes = new byte[1024];
     * int length;
     * while((length = fis.read(bytes)) >= 0) {
     * zipOut.write(bytes, 0, length);
     * }
     * 
     * zipOut.close();
     * fis.close();
     * fos.close();
     * Path path = Paths.get(fileName);
     * Files.deleteIfExists(path);
     * 
     * 
     * }
     * catch (Exception exception)
     * {
     * MyLogger.info("Could not update the fund prices");
     * exception.printStackTrace();
     * }
     */
  }

  @Override
  public String getName() {
    return this.getClass().getName();
  }
}
