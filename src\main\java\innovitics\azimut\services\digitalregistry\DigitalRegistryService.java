package innovitics.azimut.services.digitalregistry;

import java.util.Date;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Service;

import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.models.digitalregistry.DigitalRegistry;
import innovitics.azimut.models.digitalregistry.DigitalRegistryAction;
import innovitics.azimut.models.digitalregistry.DigitalRegistrySpecifications;
import innovitics.azimut.models.user.User;
import innovitics.azimut.repositories.digitalregistry.DigitalRegistryRepository;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.mapping.UserMapper;

@Service
public class DigitalRegistryService {
  private static String API_KEY = "r35ComRyEA2fd5NlPhhP";
  private @Autowired DigitalRegistryRepository digitalRegistryRepository;
  protected @Autowired UserMapper userMapper;

  public DigitalRegistry saveDigitalRegistryRecord(DigitalRegistry digitalRegistryRecord) {
    return this.digitalRegistryRepository.save(digitalRegistryRecord);
  }

  public void recordLogin(String ip, Long userId) {
    var user = this.getUser(userId);
    var digitalRegistry = new DigitalRegistry(DigitalRegistryAction.LOGIN, ip, user, null);
    this.digitalRegistryRepository.save(digitalRegistry);
  }

  public void recordSignup(String ip, Long userId, String otp) {
    var user = this.getUser(userId);
    var digitalRegistry = new DigitalRegistry(DigitalRegistryAction.SIGN_UP, ip, user, null);
    digitalRegistry.setOtp(otp);
    this.digitalRegistryRepository.save(digitalRegistry);
  }

  public void recordGetContract(String ip, Long userId, String jwtToken) {
    var user = this.getUser(userId);
    var digitalRegistry = new DigitalRegistry(DigitalRegistryAction.GET_CONTRACT, ip, user, jwtToken);
    this.digitalRegistryRepository.save(digitalRegistry);
  }

  public void recordSignContract(String ip, Long userId, String otp, String jwtToken) {
    var user = this.getUser(userId);
    var digitalRegistry = new DigitalRegistry(DigitalRegistryAction.SIGN_CONTRACT, ip, user, jwtToken);
    digitalRegistry.setOtp(otp);
    this.digitalRegistryRepository.save(digitalRegistry);
  }

  public void recordVerifyEmail(String ip, Long userId, String otp, String jwtToken) {
    var user = this.getUser(userId);
    var digitalRegistry = new DigitalRegistry(DigitalRegistryAction.VERIFY_EMAIL, ip, user, jwtToken);
    digitalRegistry.setOtp(otp);
    this.digitalRegistryRepository.save(digitalRegistry);
  }

  public void recordVerifyPhone(String ip, Long userId, String otp, String jwtToken) {
    var user = this.getUser(userId);
    var digitalRegistry = new DigitalRegistry(DigitalRegistryAction.VERIFY_PHONE, ip, user, jwtToken);
    digitalRegistry.setOtp(otp);
    this.digitalRegistryRepository.save(digitalRegistry);
  }

  public void recordChangePassword(String ip, Long userId, String otp, String jwtToken) {
    var user = this.getUser(userId);
    var digitalRegistry = new DigitalRegistry(DigitalRegistryAction.CHANGE_PASSWORD, ip, user, jwtToken);
    digitalRegistry.setOtp(otp);
    this.digitalRegistryRepository.save(digitalRegistry);
  }

  public void recordApproveEKYC(String ip, Long userId, String jwtToken) {
    var user = this.getUser(userId);
    var digitalRegistry = new DigitalRegistry(DigitalRegistryAction.APPROVE_EKYC, ip, user, jwtToken);
    this.digitalRegistryRepository.save(digitalRegistry);
  }

  public void recordUpdateLocation(String ip, Long userId, Float latitude, Float longitude, String jwtToken) {
    var user = this.getUser(userId);
    var digitalRegistry = new DigitalRegistry(DigitalRegistryAction.UPDATE_LOCATION, ip, user, jwtToken);
    digitalRegistry.setLatitude(latitude);
    digitalRegistry.setLongitude(longitude);

    this.digitalRegistryRepository.save(digitalRegistry);
  }

  private User getUser(Long userId) {
    var user = new User();
    user.setId(userId);
    return user;
  }

  public Page<DigitalRegistry> getDigitalRegistry(Pageable pageable, Optional<Long> userId, Optional<String> nationalId,
      @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) Optional<Date> beforeDate,
      @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) Optional<Date> afterDate, Optional<DigitalRegistryAction> action) {
    Specification<DigitalRegistry> spec = Specification.where(DigitalRegistrySpecifications.userNotNull());
    if (userId.isPresent()) {
      spec = spec.and(DigitalRegistrySpecifications.userIdEquals(userId.get()));
    }
    if (nationalId.isPresent()) {
      spec = spec.and(DigitalRegistrySpecifications.nationalIdEquals(nationalId.get()));
    }
    if (beforeDate.isPresent()) {
      spec = spec.and(DigitalRegistrySpecifications.beforeDateEquals(beforeDate.get()));
    }
    if (afterDate.isPresent()) {
      spec = spec.and(DigitalRegistrySpecifications.afterDateEquals(afterDate.get()));
    }
    if (action.isPresent()) {
      spec = spec.and(DigitalRegistrySpecifications.actionEquals(action.get()));
    }
    return this.digitalRegistryRepository.findAll(spec, pageable);
  }

  public DigitalRegistry getLastDigitalRegistry(Long userId, DigitalRegistryAction action) {
    Specification<DigitalRegistry> spec = Specification.where(DigitalRegistrySpecifications.userNotNull());
    spec = spec.and(DigitalRegistrySpecifications.userIdEquals(userId));
    spec = spec.and(DigitalRegistrySpecifications.actionEquals(action));
    var registries = this.digitalRegistryRepository.findAll(spec, Sort.by(Direction.DESC, "createdAt"));
    if (registries.size() > 0)
      return registries.get(0);
    else
      return null;
  }

  public void validateApiKey(Optional<String> authToken) throws BusinessException {
    if (authToken.isEmpty() || !authToken.get().equals(API_KEY)) {
      throw new BusinessException(ErrorCode.UNAUTHORIZED_USER);
    }
  }
}
