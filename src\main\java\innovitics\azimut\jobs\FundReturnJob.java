package innovitics.azimut.jobs;

import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import innovitics.azimut.businessservices.BusinessFundsChildService;
import innovitics.azimut.utilities.businessutilities.UserUtility;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
@Scope(value = ConfigurableBeanFactory.SCOPE_SINGLETON, proxyMode = ScopedProxyMode.TARGET_CLASS)
public class FundReturnJob extends ParentJob {
  @Autowired
  BusinessFundsChildService businessFundsChildService;

  @Autowired
  UserUtility userUtility;

  @Override
  @Scheduled(fixedDelayString = "1", timeUnit = TimeUnit.HOURS)
  public void scheduleFixedDelayTask() {
    super.scheduleFixedDelayTask();
    try {
      this.businessFundsChildService.updateFundReturns();
      MyLogger.info("Action Finished::::::");
    } catch (Exception exception) {
      MyLogger.info("Could not update the fund returns");
      exception.printStackTrace();
    }
  }

  @Override
  public String getName() {
    return this.getClass().getName();
  }

}
