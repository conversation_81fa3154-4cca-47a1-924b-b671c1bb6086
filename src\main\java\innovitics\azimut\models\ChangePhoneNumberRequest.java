package innovitics.azimut.models;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Entity
@Table(name = "change_phone_number_requests")
@Setter
@Getter
@ToString
public class ChangePhoneNumberRequest extends DbBaseEntity {
  private String oldPhoneNumber;
  private String newPhoneNumber;
  private Boolean isApproved;
  private String userId;
  private Long appUserId;
}
