package innovitics.azimut.rest.apis.teacomputers;

import java.util.ArrayList;
import java.util.List;

import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import innovitics.azimut.businessmodels.user.BusinessCompanyBankAccount;
import innovitics.azimut.rest.entities.teacomputers.CompanyBankAccountResponse;
import innovitics.azimut.rest.entities.teacomputers.GetCompanyBankAccountRequest;
import innovitics.azimut.rest.entities.teacomputers.TeaComputerResponse;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.logging.MyLogger;

@Component
public class TeaComputersGetCompanyBankAccountsApi
    extends RestTeaComputersApi<GetCompanyBankAccountRequest, CompanyBankAccountResponse[]> {
  public final static String PATH = "/lookups/GetCompanyBankAcc";

  @Override
  protected String generateURL(String params) {
    return super.generateBaseURL() + PATH;
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.GET;
  }

  @Override
  protected String generateSignature(GetCompanyBankAccountRequest teaComputerRequest) {
    return this.teaComputersSignatureGenerator.generateSignature("CompanyBankAcc");
  }

  @Override
  protected String generateResponseSignature(TeaComputerResponse teaComputerResponse) {
    return this.teaComputersSignatureGenerator.generateSignature("", "CompanyBankAccResponse");
  }

  public GetCompanyBankAccountRequest generateRequest() {
    GetCompanyBankAccountRequest request = new GetCompanyBankAccountRequest();
    request.setSignature(this.generateSignature(request));

    return request;
  }

  public List<BusinessCompanyBankAccount> generateBankAccountsFromResponses(
      CompanyBankAccountResponse[] companyBankAccountResponses) {
    List<BusinessCompanyBankAccount> businessCompanyBankAccounts = new ArrayList<BusinessCompanyBankAccount>();
    if (companyBankAccountResponses == null)
      return businessCompanyBankAccounts;

    for (CompanyBankAccountResponse response : companyBankAccountResponses) {
      if (NumberUtility.areLongValuesMatching(response.getAccountID(), 55L))
        continue;
      BusinessCompanyBankAccount businessCompanyBankAccount = new BusinessCompanyBankAccount();
      MyLogger.info("companyBankAccountOutput:::" + response.toString());
      businessCompanyBankAccount.setBankName(response.getBankName());
      businessCompanyBankAccount.setAccountNo(response.getAccountNo());
      businessCompanyBankAccount.setBranchName(response.getBranchName());
      businessCompanyBankAccount.setIban(response.getIBAN());
      businessCompanyBankAccount.setSwiftCode(response.getSwiftCode());
      businessCompanyBankAccount.setAccountNo(response.getAccountNo());
      businessCompanyBankAccount.setCurrencyId(response.getCurrencyID());
      businessCompanyBankAccount.setCurrencyName(response.getCurrencyName());
      businessCompanyBankAccount.setAccountId(response.getAccountID());
      businessCompanyBankAccount.setBankId(Long.parseLong(response.getBankId()));

      businessCompanyBankAccounts.add(businessCompanyBankAccount);
    }
    return businessCompanyBankAccounts;
  }
}
