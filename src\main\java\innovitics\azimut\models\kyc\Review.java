package innovitics.azimut.models.kyc;

import java.util.Date;

import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import innovitics.azimut.models.DbBaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })

@Entity
@Table(name = "reviews")
@Setter
@Getter
@ToString
public class Review extends DbBaseEntity {

  protected Long questionId;

  protected Long pageId;

  protected Integer pageOrder;

  protected Long result;

  protected Long actionMaker;

  protected Long userId;

  protected Long accountId;

  protected String comment;

  @Transient
  private Long appUserId;

  @ManyToOne
  @JoinColumn(name = "reason_id", foreignKey = @javax.persistence.ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
  protected Reason reason;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  private Date deletedAt;

  private Boolean mandatoryQuestion;
}
