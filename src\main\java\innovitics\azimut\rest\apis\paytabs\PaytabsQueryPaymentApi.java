package innovitics.azimut.rest.apis.paytabs;

import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import innovitics.azimut.businessmodels.payment.PaytabsResult;
import innovitics.azimut.rest.entities.paytabs.PaytabsQueryRequest;

@Component
public class PaytabsQueryPaymentApi extends RestPaytabsApi<PaytabsQueryRequest, PaytabsResult> {

  private static final String PATH = "/payment/query";

  @Override
  public String generateURL(String params) {
    return super.generateBaseURL(params) + PATH;
  }

  @Override
  public HttpMethod chooseHttpMethod() {
    return HttpMethod.POST;
  }
}
