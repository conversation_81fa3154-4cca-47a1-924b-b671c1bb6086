package innovitics.azimut.utilities.crosslayerenums;

public enum LiveNotificationNavigations {

	KYC_APPROVED("1","1",""),
	KYC_REJECTED("2","2",""),
	AZ_ACCOUNT_INJECT("3","3",""),
	AZ_ACCOUNT_WITHDRAW("4","4",""),
	FUNDS_BUY("5","5",""),
	FUNDS_SELL("6","6","");
	

	LiveNotificationNavigations(String navigationId,String screenId,String path) {
		this.navigationId=navigationId;
		this.screenId=screenId;
		this.path=path;
	}

	private final String navigationId;
	
	private final String path;
	
	private final String screenId;

	public String getNavigationId() {
		return navigationId;
	}

	public String getPath() {
		return path;
	}

	public String getScreenId() {
		return screenId;
	}
}
