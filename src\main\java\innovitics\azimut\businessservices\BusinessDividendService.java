package innovitics.azimut.businessservices;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import innovitics.azimut.businessutilities.BusinessSearchCriteria;
import innovitics.azimut.controllers.admin.DTOs.DividendDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.models.Dividend;
import innovitics.azimut.services.DividendService;
import innovitics.azimut.utilities.datautilities.PaginatedEntity;

@Service
public class BusinessDividendService extends AbstractBusinessService<Dividend> {

  private @Autowired DividendService dividendService;

  public PaginatedEntity<Dividend> listDividends(
      BusinessSearchCriteria businessSearchCriteria) {
    var dividends = this.dividendService
        .getFilteredDividends(generateDatabaseConditions(businessSearchCriteria));
    PaginatedEntity<Dividend> paginatedEntity = new PaginatedEntity<Dividend>();

    paginatedEntity.setCurrentPage(businessSearchCriteria.getPageNumber());
    paginatedEntity.setPageSize(businessSearchCriteria.getPageSize());
    paginatedEntity.setNumberOfPages(dividends.getTotalPages());
    paginatedEntity.setNumberOfItems(dividends.getTotalElements());
    paginatedEntity.setHasNext(!dividends.isLast());
    paginatedEntity.setHasPrevious(!dividends.isFirst());
    List<Dividend> list = new ArrayList<>();
    if (!dividends.isEmpty()) {
      for (var dividend : dividends.getContent()) {
        list.add(dividend);
      }
    } else {
      list.add(null);
    }
    paginatedEntity.setDataList(list);
    return paginatedEntity;
  }

  public Dividend addDividend(DividendDto businessReview)
      throws BusinessException {
    this.dividendService.addDividend(businessReview);
    return new Dividend();
  }

  public Dividend editDividend(DividendDto businessReview)
      throws BusinessException {
    this.dividendService.editDividend(businessReview);
    return new Dividend();
  }

  public Dividend deleteDividend(DividendDto businessReview) throws BusinessException {
    this.dividendService.deleteDividend(businessReview);
    return new Dividend();
  }

}
