package innovitics.azimut.controllers.negativelist.DTOs;

import javax.validation.constraints.NotNull;

import org.springframework.web.multipart.MultipartFile;

import lombok.Data;

@Data
public class ScreenUsersDto {
    
    @NotNull(message = "File is required")
    private MultipartFile file;
    
    private Boolean includeInactiveEntries = false;
    
    private Double minimumConfidenceScore = 0.7;
    
    private String screeningMode = "COMPREHENSIVE"; // COMPREHENSIVE, BASIC, STRICT
}
