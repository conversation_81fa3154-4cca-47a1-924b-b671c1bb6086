package innovitics.azimut.repositories.kyc;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;

import innovitics.azimut.models.kyc.Reason;
import innovitics.azimut.models.kyc.ReasonType;

@Repository
public interface ReasonDynamicRepository
    extends JpaRepository<Reason, Long>, EntityGraphJpaSpecificationExecutor<Reason> {

  List<Reason> findAllByOrderByIdDesc();

  List<Reason> findAllByReasonType(ReasonType reasonType);

}
