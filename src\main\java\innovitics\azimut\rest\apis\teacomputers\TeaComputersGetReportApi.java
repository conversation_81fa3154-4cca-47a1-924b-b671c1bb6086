package innovitics.azimut.rest.apis.teacomputers;

import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import innovitics.azimut.businessmodels.user.BusinessAzimutClient;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.controllers.users.DTOs.GetRequestStatementDto;
import innovitics.azimut.rest.entities.teacomputers.GetReportRequest;
import innovitics.azimut.rest.entities.teacomputers.GetReportResponse;
import innovitics.azimut.rest.entities.teacomputers.TeaComputerRequest;
import innovitics.azimut.rest.entities.teacomputers.TeaComputerResponse;
import innovitics.azimut.utilities.datautilities.StringUtility;

@Component
public class TeaComputersGetReportApi extends RestTeaComputersApi<GetReportRequest, GetReportResponse> {

  @Override
  protected void populateCredentials(TeaComputerRequest request) {
    request.setUserName(this.configProperties.getTeaComputersEportfolioUsername());
    request.setPassword(this.configProperties.getTeaComputersEportfolioPassword());
  }

  @Override
  protected String generateURL(String params) {
    return this.configProperties.getTeaComputersEportUrl() + "/" + params;
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.POST;
  }

  @Override
  protected String generateSignature(GetReportRequest getReportRequest) {
    if (getReportRequest != null) {
      if (StringUtility.stringsMatch(getReportRequest.getReportType(), StringUtility.VALUATION_REPORT)) {
        return this.teaComputersSignatureGenerator.generateSignature(true,
            this.configProperties.getTeaComputersEportfolioKey(), getReportRequest.getIdType().toString(),
            getReportRequest.getIdNumber());
      } else if (StringUtility.stringsMatch(getReportRequest.getReportType(), StringUtility.REQUEST_STATEMENT)) {

        return this.teaComputersSignatureGenerator.generateSignature(true,
            getReportRequest.getUserName() != null ? getReportRequest.getUserName()
                : this.configProperties.getTeaComputersKey(),
            getReportRequest.getIdType().toString(),
            getReportRequest.getIdNumber());
      }
    }
    return null;
  }

  @Override
  protected String generateResponseSignature(GetReportRequest request, TeaComputerResponse teaComputerResponse) {
    GetReportResponse getReportResponse = (GetReportResponse) teaComputerResponse;

    if (getReportResponse != null) {
      if (StringUtility.isStringPopulated(getReportResponse.getUrl())
          && getReportResponse.getUrl().contains(StringUtility.VALUATION_REPORT)) {
        return this.teaComputersSignatureGenerator.generateSignature(true,
            this.configProperties.getTeaComputersEportfolioKey(), "SendValReport", getReportResponse.getFilePath());

      } else if (StringUtility.isStringPopulated(getReportResponse.getUrl())
          && getReportResponse.getUrl().contains(StringUtility.REQUEST_STATEMENT)) {
        return this.teaComputersSignatureGenerator.generateSignature(true,
            request.getUserName() != null ? request.getUserName() : this.configProperties.getTeaComputersKey(),
            "AccountStatement",
            getReportResponse.getFilePath());

      }
    }
    return null;
  }

  @Override
  protected String generateResponseSignature(TeaComputerResponse teaComputerResponse) {
    // Not used
    GetReportResponse getReportResponse = (GetReportResponse) teaComputerResponse;

    if (getReportResponse != null) {
      if (StringUtility.isStringPopulated(getReportResponse.getUrl())
          && getReportResponse.getUrl().contains(StringUtility.VALUATION_REPORT)) {
        return this.teaComputersSignatureGenerator.generateSignature(true,
            this.configProperties.getTeaComputersEportfolioKey(), "SendValReport", getReportResponse.getFilePath());

      } else if (StringUtility.isStringPopulated(getReportResponse.getUrl())
          && getReportResponse.getUrl().contains(StringUtility.REQUEST_STATEMENT)) {
        return this.teaComputersSignatureGenerator.generateSignature("AccountStatement",
            getReportResponse.getFilePath());

      }
    }
    return null;
  }

  @Override
  protected void populateResponse(String url, ResponseEntity<GetReportResponse> responseEntity) {
    if (responseEntity != null && StringUtility.isStringPopulated(url)) {
      GetReportResponse response = responseEntity.getBody();
      if (response != null)
        response.setUrl(url);
    }
  }

  public GetReportRequest prepareRequest(BusinessUser tokenizedBusinessUser, String language,
      String reportType, GetRequestStatementDto businessAzimutClient, String partnerUsername) {
    GetReportRequest request = new GetReportRequest();
    request.setReportType(reportType);

    if (StringUtility.stringsMatch(request.getReportType(), StringUtility.VALUATION_REPORT)) {
      request.setShowAbsReturn(true);
      request.setTitle("Title");
    } else if (StringUtility.stringsMatch(request.getReportType(), StringUtility.REQUEST_STATEMENT)) {
      request.setUserName(this.configProperties.getTeaComputersUsername());
      request.setFromDate(businessAzimutClient.getSearchFromDate());
      request.setToDate(businessAzimutClient.getSearchToDate());
      request.setCurrencyId(businessAzimutClient.getCurrencyId());
    }

    request.setIdNumber(tokenizedBusinessUser.getUserId());
    request.setIdTypeId(tokenizedBusinessUser.getAzimutIdTypeId());
    request.setIdType(tokenizedBusinessUser.getAzimutIdTypeId());
    request.setLang(language);
    if (partnerUsername != null)
      request.setUserName(partnerUsername);
    request.setSignature(this.generateSignature(request));
    request.setReportType(null);
    return request;
  }

  public BusinessAzimutClient generateBusinessClientFromResponse(GetReportResponse response) {
    BusinessAzimutClient businessAzimutClient = new BusinessAzimutClient();
    if (response != null)
      businessAzimutClient.setDocumentURL(response.getFilePath());

    return businessAzimutClient;
  }
}
