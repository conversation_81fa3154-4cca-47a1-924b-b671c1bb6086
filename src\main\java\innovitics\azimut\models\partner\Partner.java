package innovitics.azimut.models.partner;

import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import lombok.Data;

@Entity
@Table(name = "partner")
@Data
public class Partner {
  @Id
  @GeneratedValue(strategy = GenerationType.AUTO)
  Long id;

  // TODO: implement generator function...
  @Column(unique = true)
  String apiKey;

  @Column(unique = true, nullable = false)
  String userName;

  String name;

  Long clientTypeId;

  Date createdAt;

  @OneToMany(mappedBy = "partner", fetch = FetchType.EAGER)
  private List<PartnerUser> partnerUsers;
}
