package innovitics.azimut.rest.entities.enroll;

import java.util.Date;

import lombok.Data;

@Data
public class EnrollApplication {
  String applicantId;
  String fullNameAr;
  String fullNameEn;
  Date birthdate;
  String gender;
  String phoneNumber;
  Boolean amlBlackList;
  String email;
  EnrollNationalIdInfo nationalIdInfo;
  EnrollLocationInfo locationInfo;
  EnrollPassportInfo passportInfo;
  EnrollBiometricInfo biometricInfo;
  String livePhotoPath;
  String deviceId;
  Integer status;
  // Accepted = 1,
  // Rejected = 2,
  // NeedsManualApproval = 3,
  // ManuallyAccepted = 4,
  // ManuallyRejected = 5

}
