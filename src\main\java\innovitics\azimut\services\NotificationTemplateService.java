package innovitics.azimut.services;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import innovitics.azimut.controllers.admin.DTOs.NotificationDto;
import innovitics.azimut.models.NotificationTemplate;
import innovitics.azimut.repositories.notification.NotificationTemplateRepository;
import innovitics.azimut.utilities.dbutilities.DatabaseConditions;
import innovitics.azimut.utilities.dbutilities.SearchCriteria;
import innovitics.azimut.utilities.dbutilities.specifications.child.NotificationTemplateSpecification;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class NotificationTemplateService extends AbstractService<NotificationTemplate, String> {

  @Autowired
  NotificationTemplateRepository notificationTemplateRepository;

  @Autowired
  NotificationTemplateSpecification notificationTemplateSpecification;

  public Page<NotificationTemplate> getAllNotifications(DatabaseConditions databaseConditions) {

    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    if (this.searchCriteriaListUtility.isListPopulated(databaseConditions.getSearchCriteria())) {
      searchCriteriaList = databaseConditions.getSearchCriteria();

    }
    this.changeDateFormat(searchCriteriaList, "createdAt");
    MyLogger.info("Search Criteria:::" + searchCriteriaList.toString());

    Page<NotificationTemplate> notificationTemplates = this.notificationTemplateRepository.findAll(
        this.notificationTemplateSpecification.findByCriteria(searchCriteriaList),
        databaseConditions.getPageRequest());

    return notificationTemplates;
  }

  public void addTemplate(NotificationDto businessNotification) {
    if (businessNotification != null && businessNotification.getNotification() != null) {
      NotificationTemplate template = businessNotification.getNotification();
      template.setCreatedAt(new Date());
      this.notificationTemplateRepository.save(template);
    }
  }

  public void editTemplate(NotificationDto businessNotification) {
    if (businessNotification != null && businessNotification.getNotification() != null) {
      NotificationTemplate template = businessNotification.getNotification();
      var dbTemplate = getTemplate(template.getId());
      template.setCreatedAt(dbTemplate.getCreatedAt());
      template.setSent(dbTemplate.getSent());
      template.setSentGroups(dbTemplate.getSentGroups());
      this.notificationTemplateRepository.save(template);
    }
  }

  public void deleteTemplate(NotificationDto businessNotification) {
    if (businessNotification != null && businessNotification.getNotification() != null) {
      NotificationTemplate template = businessNotification.getNotification();
      this.notificationTemplateRepository.delete(template);
    }
  }

  public void updateTemplate(NotificationTemplate template) {
    this.notificationTemplateRepository.save(template);
  }

  public NotificationTemplate getTemplate(Long id) {
    return this.notificationTemplateRepository.getById(id);
  }

}
