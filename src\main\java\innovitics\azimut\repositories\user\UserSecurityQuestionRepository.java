package innovitics.azimut.repositories.user;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import innovitics.azimut.models.user.UserSecurityQuestion;

@Repository
public interface UserSecurityQuestionRepository
    extends JpaRepository<UserSecurityQuestion, Long>, JpaSpecificationExecutor<UserSecurityQuestion> {

  long countByUserId(Long userId);
}
