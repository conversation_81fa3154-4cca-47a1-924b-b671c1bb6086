package innovitics.azimut.services.user;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Service;

import innovitics.azimut.models.OTPFunctionality;
import innovitics.azimut.models.OTPMethod;
import innovitics.azimut.models.user.UserOTP;
import innovitics.azimut.repositories.user.UserOtpRepository;
import innovitics.azimut.services.AbstractService;
import innovitics.azimut.utilities.datautilities.ListUtility;
import innovitics.azimut.utilities.dbutilities.SearchCriteria;
import innovitics.azimut.utilities.dbutilities.SearchOperation;
import innovitics.azimut.utilities.dbutilities.specifications.child.UserOtpSepcification;

@Service
public class UserOTPService extends AbstractService<UserOTP, String> {

  @Autowired
  UserOtpRepository userOtpRepository;
  @Autowired
  UserOtpSepcification userOtpSepcification;
  @Autowired
  ListUtility<UserOTP> usetOtpListUtility;

  public UserOTP save(UserOTP userOTP) {
    return this.userOtpRepository.save(userOTP);
  }

  public UserOTP findLatestByPhone(String userPhone) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("userPhone", userPhone, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));
    searchCriteriaList.add(new SearchCriteria("otpMethod", OTPMethod.SMS, SearchOperation.EQUAL, null));

    List<UserOTP> userOtps = this.userOtpRepository.findAll(userOtpSepcification.findByCriteria(searchCriteriaList),
        Sort.by(Direction.DESC, "id"));
    if (this.usetOtpListUtility.isListPopulated(userOtps)) {
      return userOtps.get(0);
    } else
      return null;
  }

  /*
   * public UserOTP findLatestByUser(String userPhone)
   * {
   * CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
   * CriteriaQuery<UserOTP[]> query =
   * criteriaBuilder.createQuery(UserOTP[].class);
   * Root<UserOTP> point = query.from(UserOTP.class);
   * query.groupBy(point.get("pointIdentity").get("id"));
   * query.multiselect(
   * point.get("pointIdentity").get("id"),
   * criteriaBuilder.max(point.get("pointIdentity").get("timestamp"))
   * );
   * TypedQuery<UserOTP[]> typedQuery = entityManager.createQuery(query);
   * List<UserOTP[]> results = typedQuery.getResultList();
   * }
   */

  public UserOTP findLatest(Long userId) {
    List<UserOTP> userOtps = this.userOtpRepository.findLastByUserIdOrderByIdDesc(userId);

    if (this.usetOtpListUtility.isListPopulated(userOtps)) {
      return userOtps.get(0);
    }
    return null;
  }

  public UserOTP findLatestByUserId(Long userId, OTPFunctionality functionality) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("userId", userId, SearchOperation.EQUAL, null));
    // searchCriteriaList.add(new SearchCriteria("deletedAt", "",
    // SearchOperation.IS_NULL, null));
    // searchCriteriaList.add(new SearchCriteria("contractType", contractType,
    // SearchOperation.EQUAL, null));
    searchCriteriaList
        .add(new SearchCriteria("functionality", functionality.name(), SearchOperation.EQUAL, null));
    List<UserOTP> userOtps = this.userOtpRepository.findAll(userOtpSepcification.findByCriteria(searchCriteriaList),
        Sort.by(Direction.DESC, "id"));

    if (this.usetOtpListUtility.isListPopulated(userOtps))
      return userOtps.get(0);

    return null;
  }

  public UserOTP deleteOTP(UserOTP userOTP) {
    userOTP.setDeletedAt(new Date());
    return this.userOtpRepository.save(userOTP);
  }

  public void deleteOldUserOTPs(Long userId) {
    this.userOtpRepository.deleteOldUserOTPs(userId);
  }
}
