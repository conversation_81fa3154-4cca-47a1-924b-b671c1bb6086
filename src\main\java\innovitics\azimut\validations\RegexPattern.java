package innovitics.azimut.validations;

public enum RegexPattern {

  EMAIL(Constants.EMAIL_VALUE),
  PHONE_NUMBER(Constants.PHONE_NUMBER_VALUE),
  YEAR(Constants.YEAR_VALUE),
  PASSWORD(Constants.PASSWORD_VALUE);

  RegexPattern(String pattern) {
    this.pattern = pattern;
  }

  private final String pattern;

  public String getPattern() {
    return pattern;
  }

  public static class Constants {
    public static final String EMAIL_VALUE = "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$";
    public static final String PHONE_NUMBER_VALUE = "\\+(9[976]\\d|8[987530]\\d|6[987]\\d|5[90]\\d|42\\d|3[875]\\d|"
        + "2[98654321]\\d|9[8543210]|8[6421]|6[6543210]|5[87654321]|"
        + "4[987654310]|3[9643210]|2[70]|7|1)\\d{1,14}$";
    public static final String YEAR_VALUE = "^(?:(?:31(\\/|-|\\.)(?:0?[13578]|1[02]))\\1|(?:(?:29|30)(\\/|-|\\.)(?:0?[13-9]|1[0-2])\\2))(?:(?:1[6-9]|[2-9]\\d)?\\d{2})$|^(?:29(\\/|-|\\.)0?2\\3(?:(?:(?:1[6-9]|[2-9]\\d)?(?:0[48]|[2468][048]|[13579][26])|(?:(?:16|[2468][048]|[3579][26])00))))$|^(?:0?[1-9]|1\\d|2[0-8])(\\/|-|\\.)(?:(?:0?[1-9])|(?:1[0-2]))\\4(?:(?:1[6-9]|[2-9]\\d)?\\d{2})$";
    public static final String PASSWORD_VALUE = "(?=.*\\d)(?=.*[a-z])(?=.*[A-Z]).*";
  }
}
