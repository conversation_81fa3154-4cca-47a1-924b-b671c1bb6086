package innovitics.azimut.businessmodels.payment;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class PaymentInfo {

  @JsonProperty("payment_method")
  private String paymentMethod;
  @JsonProperty("payment_description")
  private String paymentDescription;

  @JsonProperty("card_type")
  private String cardType;

  @JsonProperty("card_scheme")
  private String cardScheme;

  @JsonProperty("expiry_month")
  private String expiryMonth;

  @JsonProperty("expiry_year")
  private String expiryYear;

}
