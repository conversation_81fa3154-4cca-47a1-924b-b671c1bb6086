package innovitics.azimut.models.admin;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import innovitics.azimut.models.DbBaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Entity
@Table(name = "dashboard_users")
@Setter
@Getter
@ToString
public class AdminUser extends DbBaseEntity {
  private String password;
  private String email;
  private Long roleId;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  private Date deletedAt;

  private String name;
}
