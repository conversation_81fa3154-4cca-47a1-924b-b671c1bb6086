package innovitics.azimut.businessmodels;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import innovitics.azimut.utilities.CustomJsonRootName;
import lombok.Data;

@Data
@CustomJsonRootName(plural = "data", singular = "data")
public class BusinessAzimutInformationType {
  private static final long serialVersionUID = 6327924498322143931L;
  Integer type;
  String typeName;
  private Long id;
  private String question;
  private String answer;
  @JsonProperty("dataList")
  List<BusinessAzimutInformation> businessAzimutInformationList;

}
