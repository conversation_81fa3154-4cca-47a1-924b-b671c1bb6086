package innovitics.azimut.businessmodels.user;

import innovitics.azimut.controllers.users.DTOs.AddAccountDto;
import lombok.Data;

@Data
public class AzimutAccount {
  private Long id;
  private String idDate;
  private String idMaturityDate;
  private Long clientAML;
  private Long iDIssueCityId;
  private Long iDIssueCountryId;
  private Long iDIssueDistrictId;
  private String customerNameAr;
  private String customerNameEn;
  private String birthDate;
  private Long sexId;
  private Long clientTypeId;
  private String email;
  private String phone;
  private String addressAr;
  private String addressEn;
  private Long cityId;
  private Long countryId;
  private Long nationalityId;
  private String externalcode;
  private String occupation;
  private String postalNo;
  private String fullName;
  private String phoneNumber;
  private String userId;
  private String IdNumber;
  protected Long azIdType;
  protected String azId;

  protected String userIdType;
  protected String userIdTypeAr;
  protected Long idType;

  public String getFullName() {
    return fullName;
  }

  public void setFullName(String fullName) {
    this.fullName = fullName;
  }

  public AddAccountDto toAddAccountDto() {
    AddAccountDto addAccountDto = new AddAccountDto();
    addAccountDto.setId(getId());
    return addAccountDto;
  }

  @Override
  public String toString() {
    return "AzimutAccount [fullName=" + fullName + ", idDate=" + idDate + ", idMaturityDate=" + idMaturityDate
        + ", clientAML=" + clientAML + ", iDIssueCityId=" + iDIssueCityId + ", iDIssueCountryId="
        + iDIssueCountryId + ", iDIssueDistrictId=" + iDIssueDistrictId + ", customerNameAr=" + customerNameAr
        + ", customerNameEn=" + customerNameEn + ", birthDate=" + birthDate + ", sexId=" + sexId
        + ", clientTypeId=" + clientTypeId + ", email=" + email + ", phone=" + phone + ", addressAr="
        + addressAr + ", addressEn=" + addressEn + ", cityId=" + cityId + ", countryId=" + countryId
        + ", nationalityId=" + nationalityId + ", externalcode=" + externalcode + ", occupation=" + occupation
        + ", postalNo=" + postalNo + "]";
  }

}
