package innovitics.azimut.repositories.fund;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import innovitics.azimut.models.Nav;

@Repository
public interface NavRepository extends JpaRepository<Nav, Long>, NavRepositoryCustom {

  Optional<Nav> findFirstByFundIdOrderByDateAsc(Long fundId);

  Optional<Nav> findFirstByFundIdOrderByDateDesc(Long fundId);

}
