package innovitics.azimut.services.digitalregistry;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.models.digitalregistry.ApiRegistry;
import innovitics.azimut.repositories.digitalregistry.ApiRegistryRepository;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;

@Service
public class ApiRegistryService {

  private @Autowired ApiRegistryRepository apiRegistryRepository;
  private static String API_KEY = "r35ComRyEA5Rfd5aflPha5yH";

  public ApiRegistry addApiRegistry(String ip) {
    return apiRegistryRepository.save(new ApiRegistry(ip));
  }

  public List<ApiRegistry> getAllApiRegistry() {
    return apiRegistryRepository.findAll();
  }

  public void validateApiKey(Optional<String> authToken) throws BusinessException {
    if (authToken.isEmpty() || !authToken.get().equals(API_KEY)) {
      throw new BusinessException(ErrorCode.UNAUTHORIZED_USER);
    }
  }
}
