package innovitics.azimut.services.user;

import java.math.BigInteger;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Service;

import innovitics.azimut.models.user.User;
import innovitics.azimut.models.user.UserSecurityQuestion;
import innovitics.azimut.repositories.user.UserDynamicRepository;
import innovitics.azimut.repositories.user.UserRepository;
import innovitics.azimut.repositories.user.UserSecurityQuestionRepository;
import innovitics.azimut.services.AbstractService;
import innovitics.azimut.utilities.datautilities.DateUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.dbutilities.CountUserEntity;
import innovitics.azimut.utilities.dbutilities.DatabaseConditions;
import innovitics.azimut.utilities.dbutilities.SearchCriteria;
import innovitics.azimut.utilities.dbutilities.SearchOperation;
import innovitics.azimut.utilities.dbutilities.specifications.child.UserSpecification;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class UserService extends AbstractService<User, String> {

  @Autowired(required = true)
  private UserRepository userRepository;

  @Autowired(required = true)
  private UserDynamicRepository userDynamicRepository;

  @Autowired(required = true)
  private UserSpecification userSpecification;

  @Autowired
  private UserSecurityQuestionRepository userSecurityQuestionRepository;

  public List<User> findAll() {
    return this.userRepository.findAll();
  }

  public List<User> findTestUsers() {
    String[] testUser = { "+201289344552", "+201223453243", "+201001410690", "+201004977054", "+201288520000",
        "+201091202778" };
    List<Object> testUsersArr = this.arrayUtility.generateObjectListFromObjectArray(testUser);
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("userPhone", testUsersArr, SearchOperation.IN, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));
    return this.userDynamicRepository.findAll(this.userSpecification.findByCriteria(searchCriteriaList));
  }

  public List<User> findByIds(List<Long> ids) {
    return userRepository.findByIdIn(ids);
  }

  public List<User> findByUserIds(List<String> ids) {
    return userRepository.findByUserIdIn(ids);
  }

  public List<User> findByUserStep(String userStep) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    if (StringUtility.stringsMatch(userStep, "newUser"))
      searchCriteriaList.add(new SearchCriteria("userStep", Integer.valueOf(2), SearchOperation.LESS_THAN_EQUAL, null));
    else if (StringUtility.stringsMatch(userStep, "ekyc"))
      searchCriteriaList.add(new SearchCriteria("userStep", Integer.valueOf(3), SearchOperation.EQUAL, null));
    else if (StringUtility.stringsMatch(userStep, "clientData"))
      searchCriteriaList.add(new SearchCriteria("userStep", Integer.valueOf(7), SearchOperation.EQUAL, null));
    else if (StringUtility.stringsMatch(userStep, "bankAccount"))
      searchCriteriaList.add(new SearchCriteria("userStep", Integer.valueOf(8), SearchOperation.EQUAL, null));
    else if (StringUtility.stringsMatch(userStep, "kyc"))
      searchCriteriaList.add(new SearchCriteria("userStep", Integer.valueOf(9), SearchOperation.EQUAL, null));
    else if (StringUtility.stringsMatch(userStep, "signedContract"))
      searchCriteriaList.add(new SearchCriteria("userStep", Integer.valueOf(11), SearchOperation.EQUAL, null));
    else if (StringUtility.stringsMatch(userStep, "rejected"))
      searchCriteriaList.add(new SearchCriteria("userStep", Integer.valueOf(12), SearchOperation.EQUAL, null));
    else if (StringUtility.stringsMatch(userStep, "active"))
      searchCriteriaList.add(new SearchCriteria("userStep", Integer.valueOf(13), SearchOperation.EQUAL, null));

    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));
    return this.userDynamicRepository.findAll(this.userSpecification.findByCriteria(searchCriteriaList));
  }

  public void addSecurityQuestions(Long userId, List<UserSecurityQuestion> answers) {
    User user = new User();
    user.setId(userId);
    for (UserSecurityQuestion answer : answers) {
      answer.setUser(user);
    }
    userSecurityQuestionRepository.saveAll(answers);
  }

  public User save(User user) {
    MyLogger.info("Persisting:: User:::" + user.toString());
    if (user.getUuid() == null) {
      user.setUuid(UUID.randomUUID());
    }
    return this.userRepository.save(user);

  }

  public User update(User user) {
    if (user.getUuid() == null) {
      user.setUuid(UUID.randomUUID());
    }
    return this.userRepository.save(user);

  }

  public User findById(long id) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();

    searchCriteriaList.add(new SearchCriteria("id", id, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));
    return this.userDynamicRepository.findOne(this.userSpecification.findByCriteria(searchCriteriaList)).get();
    // user = this.userRepository.getById(id);

  }

  public List<User> findUsersByUserId(String userId) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("userId", userId, SearchOperation.EQUAL, null));
    return this.userDynamicRepository.findAll(this.userSpecification.findByCriteria(searchCriteriaList));
  }

  public User findUserByUserId(String userId) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("userId", userId, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));
    Sort sort = Sort.by(Direction.DESC, "updatedAt");
    var users = this.userDynamicRepository.findAll(this.userSpecification.findByCriteria(searchCriteriaList), sort);
    if (users.size() > 0) {
      return users.get(0);
    } else {
      return null;
    }
  }

  public User findUserById(long id) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();

    searchCriteriaList.add(new SearchCriteria("id", id, SearchOperation.EQUAL, null));
    return this.userDynamicRepository.findOne(this.userSpecification.findByCriteria(searchCriteriaList)).get();
    // user = this.userRepository.getById(id);

  }

  public List<User> findDeletedByPhoneNumber(String countryPhoneCode, String phoneNumber) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("countryPhoneCode", countryPhoneCode, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("phoneNumber", "deleted" + phoneNumber, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("enrollApplicantId", "", SearchOperation.IS_NOT_NULL, null));
    searchCriteriaList.add(new SearchCriteria("isOld", "", SearchOperation.IS_NULL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NOT_NULL, null));
    return this.userDynamicRepository.findAll(this.userSpecification.findByCriteria(searchCriteriaList));

  }

  public User findByPhoneNumber(String countryPhoneCode, String phoneNumber) {
    /*
     * User user= new User();
     * user=userRepository.findByPhoneNumber(countryPhoneCode, phoneNumber);
     * return user
     */;
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("countryPhoneCode", countryPhoneCode, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("phoneNumber", phoneNumber, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));
    return this.userDynamicRepository.findOne(this.userSpecification.findByCriteria(searchCriteriaList)).get();

  }

  public User findByUserPhone(String param) {
    /*
     * User user=new User();
     * user=this.userRepository.findByUserPhone(param);
     * return user
     */;
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("userPhone", param, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));
    return this.userDynamicRepository.findOne(this.userSpecification.findByCriteria(searchCriteriaList)).get();

  }

  public User findByUserPhoneAndPassword(String userPhone, String password) {
    /*
     * User user=new User();
     * user=this.userRepository.findByUserPhoneAndPassword(userPhone,password);
     * return user
     */;
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("userPhone", userPhone, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("password", password, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));
    return this.userDynamicRepository.findOne(this.userSpecification.findByCriteria(searchCriteriaList)).get();

  }

  public User findByUserPhoneAndPassword(String countryPhoneCode, String phoneNumber, String password) {
    /*
     * User user=new User();
     * user=this.userRepository.findByPhoneCodePhoneNumberPassword(countryPhoneCode,
     * phoneNumber,password);
     * return user
     */;
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("countryPhoneCode", countryPhoneCode, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("phoneNumber", phoneNumber, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("password", password, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));
    return this.userDynamicRepository.findOne(this.userSpecification.findByCriteria(searchCriteriaList)).get();
  }

  public void flagTheUserAsReviewed(Long userId) {
    this.userDynamicRepository.flagTheUserAsReviewed(userId);
  }

  public List<User> findUsersForTheDashboard() {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("solvedPages", "", SearchOperation.IS_NOT_NULL, null));
    searchCriteriaList.add(new SearchCriteria("userStep", 10, SearchOperation.GREATER_THAN_EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("kycStatus", "", SearchOperation.IS_NOT_NULL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));
    return this.userDynamicRepository.findAll(this.userSpecification.findByCriteria(searchCriteriaList));

  }

  public List<String> referralCodes() {
    var codes = this.userDynamicRepository.getReferralCodes();
    return codes;
  }

  public List<CountUserEntity> userStats(String startDate, String endDate) throws ParseException {
    var df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSX");
    Date actualStart;
    if (StringUtility.isStringPopulated(startDate)) {
      actualStart = df.parse(startDate);
    } else {
      Calendar fallbackStartDate = Calendar.getInstance();
      fallbackStartDate.add(Calendar.YEAR, -100);
      actualStart = fallbackStartDate.getTime();
    }
    Date actualEnd = StringUtility.isStringPopulated(endDate) ? df.parse(endDate) : DateUtility.getCurrentDate();
    var dbList = this.userDynamicRepository.countGroupByKycStatusFiltered(actualStart, actualEnd);
    List<CountUserEntity> res = new ArrayList<>();
    for (Object[] ob : dbList) {
      if (ob[1] != null) {
        Integer count = ((BigInteger) ob[0]).intValue();
        Integer kycStatus = (Integer) ob[1];
        Integer isVerified = (Integer) ob[2];
        String signedPdf = (String) ob[3];
        Integer isOld = (Integer) ob[4];
        res.add(new CountUserEntity(count, kycStatus, isVerified, signedPdf, isOld));
      }
    }
    return res;
  }

  public List<Integer> userKycStats(String startDate, String endDate) throws ParseException {
    List<Integer> res = new ArrayList<>();
    var df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSX");
    Date actualStart;
    if (StringUtility.isStringPopulated(startDate)) {
      actualStart = df.parse(startDate);
    } else {
      Calendar fallbackStartDate = Calendar.getInstance();
      fallbackStartDate.add(Calendar.YEAR, -100);
      actualStart = fallbackStartDate.getTime();
    }
    Date actualEnd = StringUtility.isStringPopulated(endDate) ? df.parse(endDate) : DateUtility.getCurrentDate();

    res.add(this.userDynamicRepository.countAcceptedFiltered(actualStart, actualEnd));
    res.add(this.userDynamicRepository.countRejectedFiltered(actualStart, actualEnd));
    res.add(this.userDynamicRepository.countSignedFiltered(actualStart, actualEnd));
    return res;
  }

  public List<User> findOldUsers() {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("isOld", true, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));
    return this.userDynamicRepository.findAll(this.userSpecification.findByCriteria(searchCriteriaList));
  }

  public Page<User> findFilteredUsersForTheDashboard(DatabaseConditions databaseConditions) {

    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    if (this.searchCriteriaListUtility.isListPopulated(databaseConditions.getSearchCriteria())) {
      searchCriteriaList = databaseConditions.getSearchCriteria();

    }
    // searchCriteriaList.add(new SearchCriteria("solvedPages",
    // "",SearchOperation.IS_NOT_NULL, null));
    // searchCriteriaList.add(new SearchCriteria("userStep",
    // UserStep.CONTRACT_MAP.getStepId(),SearchOperation.GREATER_THAN_EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("kycStatus", null, SearchOperation.IS_NOT_NULL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));
    this.changeDataType(searchCriteriaList, "kycStatus", Integer.class);
    this.changeDataType(searchCriteriaList, "teacomputersClientaml", Long.class);
    this.changeDataType(searchCriteriaList, "azimutIdTypeId", Long.class);
    this.changeDataType(searchCriteriaList, "teacomputersNationalityId", Long.class);
    this.changeDateFormat(searchCriteriaList, "createdAt");

    MyLogger.info("Search Criteria:::" + searchCriteriaList.toString());

    return this.userDynamicRepository.findAll(this.userSpecification.findByCriteria(searchCriteriaList),
        databaseConditions.getPageRequest());

  }

  public List<User> findFilteredUsersForScript(DatabaseConditions databaseConditions) {

    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    if (this.searchCriteriaListUtility.isListPopulated(databaseConditions.getSearchCriteria())) {
      searchCriteriaList = databaseConditions.getSearchCriteria();

    }
    searchCriteriaList.add(new SearchCriteria("kycStatus", null, SearchOperation.IS_NOT_NULL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));
    this.changeDataType(searchCriteriaList, "kycStatus", Integer.class);
    this.changeDataType(searchCriteriaList, "teacomputersClientaml", Long.class);
    this.changeDataType(searchCriteriaList, "azimutIdTypeId", Long.class);
    this.changeDataType(searchCriteriaList, "teacomputersNationalityId", Long.class);
    this.changeDateFormat(searchCriteriaList, "createdAt");
    return this.userDynamicRepository.findAll(this.userSpecification.findByCriteria(searchCriteriaList));

  }

  public void assignUserKycFlag(Long userId, Integer kycStatus) {
    this.userDynamicRepository.assignUserKycFlag(kycStatus, userId);
  }

  public User findByUserSocialId(String provider, String providerId) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("provider", provider, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("providerId", providerId, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));
    User user = this.getSingleElement(
        this.userDynamicRepository.findAll(this.userSpecification.findByCriteria(searchCriteriaList)));
    return user;
  }

  public User findByUserSocialId(String provider, String providerId, String userPhone) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("provider", provider, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("providerId", providerId, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("userPhone", userPhone, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));
    User user = this.getSingleElement(
        this.userDynamicRepository.findAll(this.userSpecification.findByCriteria(searchCriteriaList)));
    return user;
  }

  public User findUserForTC(String userId, Long userIdType, String userPhone) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("userId", userId, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("id", userIdType, SearchOperation.PARENT_EQUAL, "userType"));
    searchCriteriaList.add(new SearchCriteria("userPhone", userPhone, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));
    return this.getSingleElement(
        this.userDynamicRepository.findAll(this.userSpecification.findByCriteria(searchCriteriaList)));
  }

  public User findDeletedUserByPhoneNumberOrBySocialCredentials(Optional<String> countryPhoneCode,
      Optional<String> phoneNumber, Optional<String> provider, Optional<String> providerId) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    if (countryPhoneCode.isPresent() && phoneNumber.isPresent()) {
      searchCriteriaList
          .add(
              new SearchCriteria("userPhone", countryPhoneCode.get() + phoneNumber.get(), SearchOperation.EQUAL, null));
    } else if (provider.isPresent() && providerId.isPresent()) {
      searchCriteriaList.add(new SearchCriteria("provider", provider.get(), SearchOperation.EQUAL, null));
      searchCriteriaList.add(new SearchCriteria("providerId", providerId.get(), SearchOperation.EQUAL, null));
    }
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NOT_NULL, null));
    User user = this.getSingleElement(
        this.userDynamicRepository.findAll(this.userSpecification.findByCriteria(searchCriteriaList)));
    return user;
  }

  public void updateUserLogin(Long userId) {
    this.userDynamicRepository.updateUserLogin(new Date(), userId);
  }
}
