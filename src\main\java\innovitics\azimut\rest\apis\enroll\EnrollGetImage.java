package innovitics.azimut.rest.apis.enroll;

import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

@Service
public class EnrollGetImage extends RestEnrollApiBase<Void, String> {

  @Override
  protected String generateURL(String params) {
    return this.configProperties.getEnrollUrl() + "/api/v1/Applicant/GetImageByPath?imagePath=" + params;
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.GET;
  }

  @Override
  protected Boolean showOutput() {
    return false;
  }
  
}
