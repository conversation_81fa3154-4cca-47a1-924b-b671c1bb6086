used.profile=Production
###############################################################################################################
#Database
###############################################################################################################
spring.jpa.hibernate.ddl-auto=update
spring.jpa.database=mysql
spring.datasource.initialization-mode=always
spring.datasource.url=***********************************
spring.datasource.username=azimutDB
spring.datasource.password=azimut-DB

#spring.datasource.url=***********************************************
#spring.datasource.username=sandbox_azimut
#spring.datasource.password=Innovitics@Azimut2021
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
#spring.jpa.properties.hibernate.jdbc.time_zone=Africa/Cairo
spring.batch.initialize-schema=always
spring.jackson.default-property-inclusion = NON_NULL
spring.jackson.serialization.indent_output=true
###############################################################################################################
#Database batching
###############################################################################################################
spring.jpa.properties.hibernate.jdbc.batch_size=50
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.batch_versioned_data=true
spring.jpa.properties.hibernate.generate_statistics=true
spring.jpa.properties.hibernate.session.events.log.LOG_QUERIES_SLOWER_THAN_MS=25

spring.datasource.hikari.data-source-properties.prepStmtCacheSize=250
spring.datasource.hikari.data-source-properties.prepStmtCacheSqlLimit=2048
spring.datasource.hikari.data-source-properties.cachePrepStmts=true
spring.datasource.hikari.data-source-properties.useServerPrepStmts=true
spring.datasource.hikari.data-source-properties.rewriteBatchedStatements=true
###############################################################################################################
#Logging
###############################################################################################################
log.file.path=E:\\Tomcat\\webapps\\Application\\SystemLogs
#log.file.path=//home//site//wwwroot//webapps
cache.file.path=//home//site//wwwroot//cache
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type=DEBUG
###############################################################################################################
#System
###############################################################################################################
#app.url=http://213.212.245.74:8080/azimut-0.0.1-SNAPSHOT
app.url=https://app.azimut.eg
###############################################################################################################
#Blob
###############################################################################################################
blob.connection.string=DefaultEndpointsProtocol=https;AccountName=demostorageasmio;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net
blob.account.name=demostorageasmio
blob.container.url=https://demostorageasmio.blob.core.windows.net/
blob.container.name.profile-pictures=azimutprofilepictures/Users/<USER>
blob.container.name.user-interactions=azimutprofilepictures/Users/<USER>
blob.container.name.signed-pdfs=azimutdocuments/Users/<USER>
blob.container.name.digitally-signed-pdfs=azimut-contracts/Users
blob.container.name.unsigned-pdf=azimutdocuments/unsignedPDF
blob.container.name.unsigned-pdf.subDirectory=PhoneNumberChange
blob.phoneNumberChangeDocument.name=SimpleAPIGuide(004)-Copy.pdf

blob.container.name.kyc.documents=azimut-kyc/Users
blob.container.name.kyc.documents.container=azimut-kyc
blob.container.name.kyc.documents.temp=azimut-kyc-temp/Users
blob.container.name.kyc.documents.temp.container=azimut-kyc-temp

blob.temp.deletion.hours=120000
blob.generate.sas.token=true
blob.sas.token.duration.minutes=2
blob.sas.document.token.duration.minutes=15
profile.picture.max.size.bytes=3000000
phone.number.pdf.max.size.bytes=15000000
temp.file.delete.delay.hours=24
is.production=true
################################################################################################################
#Security
################################################################################################################
admin.token.validity.minutes=1440
token.validity.minutes=10
token.key=SDFGHJKLZXCV
token.encryption.key=KJRBGONENSGVCNDQ
token.encryption.init.vector=eio382h4tbiuboda
blockage.duration.minutes=60
blockage.number.of.trials=5
summer.time=false
################################################################################################################
spring.mvc.dispatch-options-request=true
spring.servlet.multipart.max-file-size=20MB
spring.servlet.multipart.max-request-size=20MB
################################################################################################################
#Azimut
################################################################################################################
azimut.url=https://api.azimut.innsandbox.com/api
azimut.fund.images.url=https://api.azimut.eg/storage
azimut.bcc.mail=<EMAIL>,<EMAIL>
#azimut.bcc.mail=<EMAIL>

################################################################################################################
#TeaComputers
################################################################################################################
tea.computers.url=http://**********/FITSAPI/Api/fund
tea.computers.eport.url=http://**********/EPortApi
#tea.computers.url=http://*************/FITSAPI/Api/fund
#tea.computers.eport.url=http://*************/EPortApi
tea.computers.key=MOBILEAPP
tea.computers.username=MOBILEAPP
tea.computers.password=MOBILEAPP
tea.computers.eportfolio.key=MOBILEAPP
tea.computers.eportfolio.username=MOBILEAPP
tea.computers.eportfolio.password=MOBILEAPP
tea.computers.job.delay.seconds=300
trading.path=/azimut-0.0.1-SNAPSHOT/api/azimut/trading/
otp.path=/azimut-0.0.1-SNAPSHOT/api/otp/
login.path=/azimut-0.0.1-SNAPSHOT/api/authenticate/
forgot.password.path=/azimut-0.0.1-SNAPSHOT/api/forgotPassword/
################################################################################################################
#Proxy
################################################################################################################
proxy.username=
proxy.password=
proxy.url=
proxy.port=
################################################################################################################
#eNROLL
################################################################################################################
enroll.url=https://enrollgateway.luminsoft.net/OnBoarding
enroll.tenant.id=76b3fd27-e315-4a5f-b4d8-717a270797c3
enroll.tenant.secret=cca35f31-dcb7-4778-a104-4b7e9a257072
enroll.client.id=8dd71abd-3e8e-4099-80c5-6b4635b8c4b0
enroll.client.secret=314935c8-d06e-44a1-a0ea-0656d78f6da1
################################################################################################################
#Gate ID
################################################################################################################
gateid.url=https://ekyc-backend.gateid.ai
gateid.username=<EMAIL>
gateid.password=z6Zk3psiRW43Cib
gateid.project.id=3
################################################################################################################
#Paymob
################################################################################################################
paymob.url=https://accept.paymob.com
paymob.api.key=ZXlKaGJHY2lPaUpJVXpVeE1pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SmpiR0Z6Y3lJNklrMWxjbU5vWVc1MElpd2ljSEp2Wm1sc1pWOXdheUk2TVRBME1Ea3hNeXdpYm1GdFpTSTZJbWx1YVhScFlXd2lmUS5xVndidThrckF0UjcySWpxY1o1T0h4NG5LRzYzNjJ5WGhFVFN6QnlCMENaVHQ3ZjBORWJBU21JX2c3ZHVwdlgzQ3FfRUZwZWlMcFhMcEM1VjFNSjFWdw==
paymob.public.key=egy_pk_live_DuwU22YCbe7BbLj5Kb7ZqTO6AsD2ITat
paymob.secret.key=egy_************************************************************************
paymob.wallet.integration.id=5194823
paymob.hmac=210DFAAC7DEE26EC242DDE851ECDC4B7
paymob.callback.url=https://app.azimut.eg/api/paytabs/paymobCallback
################################################################################################################
#Paytabs
################################################################################################################
paytabs.url=https://secure-egypt.paytabs.com
paytabs.profile.id=136201
paytabs.merchant.id=64951
paytabs.server.key=SDJNKTZ6NB-JHWBHLRNWM-BBMTT6G9N9
paytabs.client.key=C7KM7G-GKPD6H-V2HBRQ-HDDR7M
paytabs.mobile.server.key=SZJNKTZ6ZW-JHWBHLRN2M-9KDZKRBTZ9
paytabs.mobile.client.key=C7KM7G-GKTR6H-V2HBRQ-6VNRQK
paytabs.callback.url=https://app.azimut.eg/api/paytabs/callback
paytabs.return.url=https://azinvest.azimut.eg/payment-status
################################################################################################################
#Firebase
################################################################################################################
firebase.url=https://identitytoolkit.googleapis.com:443/v1/accounts
firebase.web.key=AIzaSyCYNtILg6bgkGE1OMDr4mUn6YV9UN4hDUk
################################################################################################################
#Victory Link SMS (VL)
################################################################################################################
vl.url=https://umvas.vlserv.com
vl.sms.url=https://smsvas.vlserv.com/KannelSending/service.asmx/SendSMSWithDLR
vl.sender=AzimutEgypt
vl.username=AzimutOTP1
vl.password=6IAllKHt99
vl.service=AzimutEgyptOTP
################################################################################################################
#eZagel SMS 
################################################################################################################
ezagel.url=https://www.ezagel.com/portex_ws/service.asmx/Send_SMSWithoutMsg_ID
ezagel.sender=AzimutEgypt
ezagel.username=Azimut
ezagel.password=7tZEYS)%T2
ezagel.service=OTP
################################################################################################################
#Twilio SMS
################################################################################################################
twilio.account.sid=**********************************
twilio.auth.token=88dbf6ec9ee7a86257c9acaa8a7a8564
twilio.service.sid=VA6ff9893bbfeb3847915be4adc3b3b825
################################################################################################################
#Mail
################################################################################################################
spring.mail.host=email-smtp.eu-west-1.amazonaws.com
spring.mail.port=587
mail.from=<EMAIL>
spring.mail.username=AKIA2R3XKDECGHRYZI7Z
spring.mail.password=BMC9FjEfKXQRV7xtjo144bOUvbHMFqgswDgYhdwp+9S2
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
################################################################################################################
#configuring ehcache.xml
################################################################################################################
spring.cache.jcache.config=classpath:ehcache.xml
################################################################################################################
#Pagination
################################################################################################################
page.size=10
################################################################################################################
#Social Login
################################################################################################################
spring.security.oauth2.client.registration.google.clientId= 92069906716-9la3jom0uo82pe1r8a782utkpn44ogbm.apps.googleusercontent.com
spring.security.oauth2.client.registration.google.clientSecret=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
spring.security.oauth2.client.registration.google.scope=email, profile
spring.security.oauth2.client.registration.facebook.clientId=959851142049147
spring.security.oauth2.client.registration.facebook.clientSecret=********************************
spring.security.oauth2.client.registration.facebook.scope=email, public_profile



app.auth.tokenSecret=04ca023b39512e46d0c2cf4b48d5aac61d34302994c87ed4eff225dcf3b0a218739f3897051a057f9b846a69ea2927a587044164b7bae5e1306219d50b588cb1
app.auth.tokenExpirationMsec=864000000
app.cors.allowedOrigins=http://localhost:8090

springdoc.swagger-ui.enabled=false
springdoc.api-docs.enabled=false
