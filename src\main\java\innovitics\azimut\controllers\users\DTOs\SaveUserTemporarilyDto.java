package innovitics.azimut.controllers.users.DTOs;

import javax.validation.Valid;

import innovitics.azimut.businessmodels.user.AzimutAccount;
import innovitics.azimut.businessmodels.user.BusinessUser;
import lombok.Data;

@Data
public class SaveUserTemporarilyDto {

  String deviceId;
  Long idType;
  String emailAddress;
  String countryPhoneCode;
  String phoneNumber;
  String nickName;
  String password;
  @Valid
  AzimutAccountDto azimutAccount;
  String countryCode;
  String socialToken;
  String providerId;
  String provider;
  String referralCode;
  String otp;

  String partnerUserToken;

  private String token;
  private String platform;

  public BusinessUser toBusinessUser() {
    BusinessUser businessUser = new BusinessUser();
    businessUser.setDeviceId(deviceId);
    businessUser.setIdType(idType);
    businessUser.setEmailAddress(emailAddress);
    businessUser.setCountryPhoneCode(countryPhoneCode);
    businessUser.setPhoneNumber(phoneNumber);
    businessUser.setNickName(nickName);
    businessUser.setAzimutAccount(azimutAccount.toAzimutAccount());
    businessUser.setCountryCode(countryCode);
    businessUser.setSocialToken(socialToken);
    businessUser.setProviderId(providerId);
    businessUser.setProvider(provider);
    businessUser.setPassword(password);
    businessUser.setReferralCode(referralCode);
    return businessUser;
  }
}

@Data
class AzimutAccountDto {
  Long cityId;
  Long countryId;

  public AzimutAccount toAzimutAccount() {
    AzimutAccount azimutAccount = new AzimutAccount();
    azimutAccount.setCityId(cityId);
    azimutAccount.setCountryId(countryId);
    return azimutAccount;
  }
}
