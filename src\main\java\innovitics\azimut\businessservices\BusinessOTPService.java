package innovitics.azimut.businessservices;

import java.time.Instant;
import java.time.temporal.ChronoUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.businessmodels.user.BusinessUserOTP;
import innovitics.azimut.controllers.otp.Dtos.VerifyDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.models.OTPFunctionality;
import innovitics.azimut.models.OTPMethod;
import innovitics.azimut.models.digitalregistry.DigitalRegistryAction;
import innovitics.azimut.rest.apis.ezagel.EzagelSendOtp;
import innovitics.azimut.rest.apis.vlotp.VLSendCustomOtp;
import innovitics.azimut.rest.entities.vl.VLOtpRequest;
import innovitics.azimut.services.digitalregistry.DigitalRegistryService;
import innovitics.azimut.utilities.crosslayerenums.ContractMap;
import innovitics.azimut.utilities.crosslayerenums.KycStatus;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class BusinessOTPService extends AbstractBusinessService<BusinessUserOTP> {

  private @Autowired VLSendCustomOtp vlSendCustomOtp;
  private @Autowired EzagelSendOtp ezagelSendOtp;
  private @Autowired DigitalRegistryService digitalRegistryService;

  public BusinessUserOTP getPhoneOTP(String phone) throws BusinessException {
    return super.getPhoneOTP(phone);
  }

  public String sendOtp(String phone, String assessmentId) throws IntegrationException, BusinessException {
    var oldPhone = this.userUtility.findLatestOTPByPhone(phone);
    if (oldPhone != null &&
        StringUtility.stringsMatch(oldPhone.getFunctionality(), OTPFunctionality.VERIFY_PHONE.name())
        && oldPhone.getCreatedAt().toInstant().isAfter(Instant.now().minus(1, ChronoUnit.MINUTES))) {
      throw new IntegrationException(ErrorCode.USER_BLOCKED);
    }
    int randomPin = (int) (Math.random() * 900000) + 100000;
    String otp = String.valueOf(randomPin);
    VLOtpRequest request = new VLOtpRequest();
    request.setPhone(phone);
    request.setOtp(otp);
    try {
      // if international phone don't send otp as we only send email for now
      if (phone.startsWith("+20")) {
        // vlSendCustomOtp.getData(request);
        ezagelSendOtp.getData(request);
      }
    } catch (IntegrationException e) {
      MyLogger.logStackTrace(e);
    }
    BusinessUserOTP userOtp = new BusinessUserOTP();
    userOtp.setUserPhone(phone);
    userOtp.setOtpMethod(OTPMethod.SMS);
    userOtp.setOtp(otp);
    userOtp.setContractType(ContractMap.REMOTELY.getMapId());
    userOtp.setFunctionality(OTPFunctionality.VERIFY_PHONE.name());
    userOtp.setAssessmentId(assessmentId);
    this.userUtility.upsertOTP(userOtp, ErrorCode.OTP_NOT_SAVED, true);
    return otp;
  }

  public void verifyOtp(BusinessUserOTP businessUserOTP, String otp, BusinessUser user)
      throws IntegrationException, BusinessException {

    if (StringUtility.stringsDontMatch(businessUserOTP.getOtp(), otp)) {
      if (businessUserOTP.getAssessmentId() != null)
        recaptchaUtility.annotateOTPAssessment(businessUserOTP, false);
      throw new BusinessException(ErrorCode.INVALID_OTP, HttpStatus.UNPROCESSABLE_ENTITY);
    }
    if (businessUserOTP.getAssessmentId() != null)
      recaptchaUtility.annotateOTPAssessment(businessUserOTP, true);
    if (user != null) {
      if (businessUserOTP.getUserId() != null) {
        throw new BusinessException(ErrorCode.OTP_NOT_VERIFIED, HttpStatus.UNPROCESSABLE_ENTITY);
      }
      businessUserOTP.setFunctionality(OTPFunctionality.OPERATIONS.name());
      businessUserOTP.setUserId(user.getId());
      this.userUtility.upsertOTP(businessUserOTP, ErrorCode.OTP_NOT_SAVED, true);
    }
  }

  public String sendOnboardingOTP(String phone, String assessmentId, boolean isEgypt)
      throws IntegrationException, BusinessException {
    if (isEgypt) {
      return sendOtp(phone, assessmentId);
    } else {
      BusinessUserOTP userOtp = new BusinessUserOTP();
      userOtp.setUserPhone(phone);
      userOtp.setOtpMethod(OTPMethod.SMS);
      userOtp.setContractType(ContractMap.REMOTELY.getMapId());
      userOtp.setFunctionality(OTPFunctionality.VERIFY_PHONE.name());
      userOtp.setAssessmentId(assessmentId);
      this.userUtility.upsertOTP(userOtp, ErrorCode.OTP_NOT_SAVED, true);
      twilioService.send(phone);
      return null;
    }
  }

  public void verifyOnboardingOtp(String phone, String otp) throws IntegrationException, BusinessException {
    BusinessUserOTP businessUserOTP = this.getPhoneOTP(phone);
    if (phone.startsWith("+20") && businessUserOTP.getOtp() != null) {
      this.verifyOtp(businessUserOTP, otp, null);
    } else {
      this.verifyTwilioOtp(businessUserOTP, otp);
    }
  }

  public BusinessUserOTP verifyContractOtp(BusinessUser tokenizedBusinessUser, VerifyDto otpDto)
      throws IntegrationException, BusinessException {
    tokenizedBusinessUser.setContractMap(ContractMap.REMOTELY.getMapId());
    this.validation.validateKYCSigning(tokenizedBusinessUser);
    BusinessUserOTP businessUserOTP = this.getPhoneOTP(tokenizedBusinessUser.getUserPhone());
    businessUserOTP.setFunctionality(OTPFunctionality.SIGN_CONTRACT.name());
    businessUserOTP.setUserId(tokenizedBusinessUser.getId());
    if (StringUtility.stringsDontMatch(businessUserOTP.getOtp(), otpDto.getOtp()))
      throw new BusinessException(ErrorCode.INVALID_OTP, HttpStatus.UNPROCESSABLE_ENTITY);
    var locationDr = digitalRegistryService.getLastDigitalRegistry(tokenizedBusinessUser.getId(),
        DigitalRegistryAction.UPDATE_LOCATION);
    if (locationDr == null) {
      throw new BusinessException(ErrorCode.LOCATION_NOT_FOUND, HttpStatus.UNPROCESSABLE_ENTITY);
    }
    this.finishDocumentAndUserStepAndOTP(tokenizedBusinessUser, businessUserOTP);
    businessUserOTP.setKycStatus(KycStatus.PENDING.getStatusId());
    return businessUserOTP;
  }

}
