package innovitics.azimut.controllers.users;

import java.io.IOException;
import java.util.Map.Entry;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.multipart.MultipartFile;

import com.google.firebase.auth.FirebaseAuthException;

import innovitics.azimut.businessmodels.user.AuthenticationResponse;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.businessservices.BusinessKYCPageService;
import innovitics.azimut.businessservices.BusinessUserService;
import innovitics.azimut.configproperties.ConfigProperties;
import innovitics.azimut.controllers.BaseController;
import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.controllers.GenericResponseHandler;
import innovitics.azimut.controllers.users.DTOs.AppVersionResponse;
import innovitics.azimut.controllers.users.DTOs.ChangePhoneDto;
import innovitics.azimut.controllers.users.DTOs.CheckReferralCodeDto;
import innovitics.azimut.controllers.users.DTOs.GetByIdDto;
import innovitics.azimut.controllers.users.DTOs.GetByUserPhoneDto;
import innovitics.azimut.controllers.users.DTOs.GetUserImagesDto;
import innovitics.azimut.controllers.users.DTOs.GetUserNotificationsDto;
import innovitics.azimut.controllers.users.DTOs.ReadNotificationDto;
import innovitics.azimut.controllers.users.DTOs.SaveClientDetailsDto;
import innovitics.azimut.controllers.users.DTOs.SaveContractMapChoiceDto;
import innovitics.azimut.controllers.users.DTOs.SaveUserLocationDto;
import innovitics.azimut.controllers.users.DTOs.SecurityQuestionsListDto;
import innovitics.azimut.controllers.users.DTOs.SetUserIdAndIdTypeDto;
import innovitics.azimut.controllers.users.DTOs.SetUserMessagingTokenDto;
import innovitics.azimut.controllers.users.DTOs.UpdatePasswordDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.models.user.SecurityQuestion;
import innovitics.azimut.security.AES;
import innovitics.azimut.services.digitalregistry.DigitalRegistryService;
import innovitics.azimut.services.user.SecurityQuestionService;
import innovitics.azimut.utilities.businessutilities.UserUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;

@Validated
@RestController
@RequestMapping(value = "/api/user", produces = {
    MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE })
public class UserController extends BaseController {

  @Autowired
  BusinessUserService businessUserService;
  @Autowired
  BusinessKYCPageService businessKYCPageService;
  @Autowired
  AES aes;

  @Autowired
  SecurityQuestionService securityQuestionService;

  @Autowired
  GenericResponseHandler<SecurityQuestion> securityQuestionHandler;
  @Autowired
  GenericResponseHandler<BusinessUser> userHandler;
  @Autowired
  DigitalRegistryService digitalRegistry;
  @Autowired
  UserUtility userUtility;

  @Autowired
  GenericResponseHandler<AuthenticationResponse<BusinessUser>> authenticationResponseHandler;

  @Autowired
  private GenericResponseHandler<String> singleAttributeHandler;

  private @Autowired GenericResponseHandler<Boolean> booleanHandler;

  @Autowired
  private ConfigProperties configProperties;

  @PostMapping(value = "/getById", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> getById(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody GetByIdDto getByIdDto) throws BusinessException, IOException {
    return userHandler.generateBaseGenericResponse(BusinessUser.class,
        this.businessUserService.beautifyUser(
            businessUserService.getById(getByIdDto.getId(), this.getCurrentRequestHolder(token), false, language)),
        null, null);
  }

  @GetMapping(value = "/getPopups", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> getById(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language)
      throws BusinessException, IOException {
    return userHandler.generateBaseGenericResponse(BusinessUser.class,
        this.businessUserService.beautifyUser(
            businessUserService.getPopup(this.getCurrentRequestHolder(token), language)),
        null, null);
  }

  @PostMapping(value = "/getClientOCRDetails", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> getClientOCRDetails(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody GetByIdDto getByUserIdDto) throws BusinessException, IOException {
    return userHandler.generateBaseGenericResponse(BusinessUser.class,
        this.businessUserService.beautifyUser(
            businessUserService.getById(getByUserIdDto.getId(), this.getCurrentRequestHolder(token), true, language)),
        null, null);
  }

  @PostMapping(value = "/getByUserPhone", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> getByUserPhone(
      @Valid @RequestBody GetByUserPhoneDto getByUserPhoneDto)
      throws BusinessException, IOException, IntegrationException {
    return userHandler.generateBaseGenericResponse(BusinessUser.class,
        this.businessUserService
            .hideUserDetails(this.businessUserService.verifyUserExistence(getByUserPhoneDto)),
        null,
        null);
  }

  @PostMapping(value = "/checkPhoneAvailability", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<Boolean>> checkPhoneNumberExistence(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody GetByUserPhoneDto phoneDto)
      throws BusinessException, IOException, IntegrationException {
    BusinessUser newBusinessUser = new BusinessUser();
    newBusinessUser.setUserPhone(phoneDto.getCountryPhoneCode() + phoneDto.getPhoneNumber());
    try {
      this.validation.validateNewPhoneNumberAvailability(newBusinessUser);
      return booleanHandler.generateBaseGenericResponse(Boolean.class, true, null, null);
    } catch (BusinessException e) {
      return booleanHandler.generateBaseGenericResponse(Boolean.class, false, null, null);
    }
  }

  @PostMapping(value = "/changePhoneNumber", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<AuthenticationResponse<BusinessUser>>> changePhoneNumber(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.IP, required = false) String ip,
      @Valid @RequestBody ChangePhoneDto changePhoneDto)
      throws BusinessException, IOException, IntegrationException, FirebaseAuthException {
    if (!this.userUtility.validateOTP(changePhoneDto.getOtp(), changePhoneDto.getCountryPhoneCode() +
        changePhoneDto.getPhoneNumber()))
      throw new BusinessException(ErrorCode.INVALID_OTP);
    var user = this.getCurrentRequestHolder(token);
    BusinessUser businessUser = this.businessUserService.changePhoneNumber(user, changePhoneDto);
    digitalRegistry.recordVerifyPhone(ip, user.getId(), changePhoneDto.getOtp(), token);
    return authenticationResponseHandler.generateBaseGenericResponse(AuthenticationResponse.class,
        new AuthenticationResponse<BusinessUser>(this.jwtUtil.generateTokenUsingUserDetails(businessUser),
            this.businessUserService.beautifyUser(businessUser)),
        null,
        null);
  }

  @PostMapping(value = "/uploadSignedPdf", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> uploadSignedPdf(
      @RequestParam("id") Long id,
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestParam(name = "file", required = false) MultipartFile file,
      @RequestParam("newCountryPhoneCode") String newCountryPhoneCode,
      @RequestParam("newPhoneNumber") String newPhoneNumber)
      throws BusinessException, IOException, MaxUploadSizeExceededException {
    MyLogger.info("newCountryPhoneCode" + newCountryPhoneCode);
    MyLogger.info("newPhoneNumber" + newPhoneNumber);
    var user = this.getCurrentRequestHolder(token);
    var uploaded = this.businessUserService.uploadSignedPdf(id, newCountryPhoneCode,
        newPhoneNumber, file, user);
    return userHandler.generateBaseGenericResponse(BusinessUser.class,
        this.businessUserService.beautifyUser(uploaded),
        null, null);
  }

  @PostMapping(value = "/signContractDigitally", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> signContractDigitally(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.IP, required = false) String ip,
      @RequestParam(name = "file") MultipartFile file)
      throws BusinessException, IOException, MaxUploadSizeExceededException, IntegrationException {
    var user = this.getCurrentRequestHolder(token);
    this.businessUserService.signPdf(user, file);
    this.digitalRegistry.recordSignContract(ip, user.getId(), null, token);

    return userHandler.generateBaseGenericResponse(BusinessUser.class,
        this.businessUserService.beautifyUser(user),
        null, null);
  }

  @PostMapping(value = "/updateUserProfile", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> editUserProfile(
      @RequestParam("id") Long id,
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestParam(name = "file", required = false) MultipartFile file,
      @RequestParam("nickName") String nickName, @RequestParam("emailAddress") String emailAddress)
      throws BusinessException, MaxUploadSizeExceededException, IllegalStateException, IOException {
    return userHandler.generateBaseGenericResponse(BusinessUser.class,
        this.businessUserService.beautifyUser(this.businessUserService.editUserProfile(id, file, nickName,
            emailAddress, this.getCurrentRequestHolder(token))),
        null, null);
  }

  @PostMapping(value = "/updatePassword", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> editUserPassword(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.IP, required = false) String ip,
      @Valid @RequestBody UpdatePasswordDto updatePasswordDto) throws BusinessException {

    BusinessUser currentUser = this.getCurrentRequestHolder(token);
    String otp = updatePasswordDto.getOtp();

    if (!this.userUtility.validateOTP(otp, currentUser.getUserPhone()))
      throw new BusinessException(ErrorCode.INVALID_OTP);
    this.digitalRegistry.recordChangePassword(ip, currentUser.getId(), otp, token);
    return userHandler.generateBaseGenericResponse(BusinessUser.class,
        this.businessUserService.beautifyUser(
            this.businessUserService.editUserPassword(updatePasswordDto, this.getCurrentRequestHolder(token), true)),
        null, null);
  }

  @PostMapping(value = "/downloadUnsignedPDF")
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> downloadUnsignedPDF(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody GetByIdDto getByIdDto, HttpServletRequest request) throws BusinessException, IOException {
    var user = this.getCurrentRequestHolder(token);
    var unsignedPdf = this.businessUserService.getUnsignedPDF(getByIdDto, user);
    return userHandler.generateBaseGenericResponse(BusinessUser.class,
        this.businessUserService.beautifyUser(
            unsignedPdf),
        null, null);
  }

  @PostMapping(value = "/getUserImages")
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> getUserImages(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody GetUserImagesDto getUserImagesDto) throws BusinessException, IOException {
    return userHandler.generateBaseGenericResponse(BusinessUser.class,
        this.businessUserService.getUserImages(this.getCurrentRequestHolder(token), getUserImagesDto, language), null,
        null);
  }

  @PostMapping(value = "/saveClientDetails")
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> saveClientDetails(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody SaveClientDetailsDto businessUser) throws BusinessException, IOException {
    return userHandler.generateBaseGenericResponse(BusinessUser.class,
        this.businessUserService.updateUserDetails(this.getCurrentRequestHolder(token), businessUser, false), null,
        null);

  }

  @PostMapping(value = "/uploadSignatureImage", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE }, produces = { MediaType.APPLICATION_JSON_VALUE,
          MediaType.APPLICATION_XML_VALUE })
  protected ResponseEntity<BaseGenericResponse<Boolean>> valifyFacialFormData(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestParam(name = "image", required = false) MultipartFile signatureImage)
      throws MaxUploadSizeExceededException,
      IllegalStateException, BusinessException, IOException, IntegrationException {

    businessUserService.saveSignatureImage(this.getCurrentRequestHolder(token), signatureImage);
    return booleanHandler.generateBaseGenericResponse(Boolean.class, Boolean.TRUE, null, null);
  }

  @PostMapping(value = "/saveContractMapChoice")
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> saveContractMapChoice(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody SaveContractMapChoiceDto businessUser) throws BusinessException, IOException {

    return userHandler.generateBaseGenericResponse(BusinessUser.class,
        this.businessUserService.saveContractMapChoice(this.getCurrentRequestHolder(token), businessUser, true), null,
        null);
  }

  @PostMapping(value = "/saveUserLocation")
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> saveUserLocation(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody SaveUserLocationDto userLocation,
      @RequestHeader(name = StringUtility.IP, required = false) String ip)
      throws BusinessException, IOException {
    var user = this.getCurrentRequestHolder(token);
    this.digitalRegistry.recordUpdateLocation(ip, user.getId(),
        Float.parseFloat(userLocation.getLat()),
        Float.parseFloat(userLocation.getLongt()), token);
    return userHandler.generateBaseGenericResponse(BusinessUser.class,
        this.businessUserService.addUserLocation(userLocation, user), null, null);
  }

  @GetMapping(value = "/getUserLocation")
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> getUserLocation(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token)
      throws BusinessException, IOException {
    return userHandler.generateBaseGenericResponse(BusinessUser.class,
        this.businessUserService.getUserLocation(this.getCurrentRequestHolder(token)), null, null);
  }

  @GetMapping(value = "/downloadContract", produces = { MediaType.APPLICATION_PDF_VALUE })
  protected @ResponseBody byte[] downloadContract(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language)
      throws IOException, BusinessException {
    var user = this.getCurrentRequestHolder(token);
    var contract = this.businessUserService.downloadUserContract(user, language)
        .toByteArray();
    return contract;
  }

  @GetMapping(value = "/downloadUnsignedContract", produces = { MediaType.APPLICATION_PDF_VALUE })
  protected @ResponseBody byte[] downloadUnsignedContract(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @RequestHeader(name = StringUtility.IP, required = false) String ip)
      throws IOException, BusinessException {
    var user = this.getCurrentRequestHolder(token);
    var contract = this.businessUserService.downloadUnsignedUserContract(user, language)
        .toByteArray();
    MyLogger.info("Contract bytes size: " + contract.length);
    this.digitalRegistry.recordGetContract(ip, user.getId(), token);
    return contract;
  }

  @PostMapping(value = "/setUserIdAndIdType")
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> setUserIdAndIdType(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody SetUserIdAndIdTypeDto setUserIdAndIdTypeDto) throws BusinessException, IOException {
    return userHandler.generateBaseGenericResponse(BusinessUser.class,
        this.businessUserService.beautifyUser(
            this.businessUserService.setUserIdAndUserIdType(this.getCurrentRequestHolder(token),
                setUserIdAndIdTypeDto)),
        null, null);
  }

  @GetMapping(value = "/verifyUser")
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> verifyUser(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token)
      throws BusinessException, IOException {

    return userHandler.generateBaseGenericResponse(BusinessUser.class, this.businessUserService
        .beautifyUser(this.businessUserService.verifyUser(this.getCurrentRequestHolder(token))), null, null);
  }

  @GetMapping(value = "/verifyUserEmail")
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> verifyUserEmail(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestParam(name = "otp") String otp, @RequestHeader(name = StringUtility.IP, required = false) String ip)
      throws BusinessException, IOException {
    var user = this.businessUserService.verifyUserEmail(this.getCurrentRequestHolder(token), otp);
    this.digitalRegistry.recordVerifyEmail(ip, user.getId(), otp, token);
    return userHandler.generateBaseGenericResponse(BusinessUser.class, this.businessUserService.beautifyUser(user),
        null, null);
  }

  @PostMapping(value = "/addUserInteraction", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> addUserInteraction(
      @RequestParam("countryCode") String countryCode,
      @RequestParam("countryPhoneCode") String countryPhoneCode,
      @RequestParam("phoneNumber") String phoneNumber,
      @RequestParam("email") String email,
      @RequestParam("body") String body,
      @RequestParam("type") Integer type,
      @RequestParam(name = "file", required = false) MultipartFile file)
      throws BusinessException, IOException, MaxUploadSizeExceededException {
    return userHandler.generateBaseGenericResponse(BusinessUser.class, this.businessUserService
        .addUserInteraction(countryCode, countryPhoneCode, phoneNumber, email, body, type, file), null, null);
  }

  @GetMapping(value = "/getUserInteraction")
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> getUserInteraction(Integer type)
      throws BusinessException, IOException {
    return userHandler.generateBaseGenericResponse(BusinessUser.class,
        this.businessUserService.getUserInteractions(type),
        null, null);
  }

  @PostMapping(value = "/setUserMessagingToken")
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> setUserMessagingToken(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody SetUserMessagingTokenDto setUserMessagingTokenDto) throws BusinessException, IOException {
    return userHandler.generateBaseGenericResponse(BusinessUser.class,
        this.businessUserService.setUserMessagingToken(this.getCurrentRequestHolder(token), setUserMessagingTokenDto),
        null,
        null);
  }

  @PostMapping(value = "/checkReferralCode")
  protected ResponseEntity<BaseGenericResponse<Boolean>> checkReferralCode(
      @Valid @RequestBody CheckReferralCodeDto referralCode) throws BusinessException {
    return booleanHandler.generateBaseGenericResponse(Boolean.class,
        this.businessUserService.checkReferralCode(referralCode.getReferralCode()), null, null);
  }

  @PostMapping(value = "/getUserNotifications", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> getUserNotifications(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody GetUserNotificationsDto getUserNotificationsDto,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language)
      throws BusinessException, IOException {
    return userHandler.generateBaseGenericResponse(BusinessUser.class,
        this.businessUserService.getUserNotifications(this.getCurrentRequestHolder(token), getUserNotificationsDto,
            language),
        null, null);
  }

  @PostMapping(value = "/readNotification")
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> readNotification(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody ReadNotificationDto businessNotification) throws BusinessException, IOException {
    return userHandler.generateBaseGenericResponse(BusinessUser.class,
        this.businessUserService.readNotification(this.getCurrentRequestHolder(token), businessNotification), null,
        null);
  }

  @GetMapping(value = "/getPrices")
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> getFundPrices(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token)
      throws IOException, BusinessException {

    BusinessUser businessUser = new BusinessUser();

    // businessUser.setFundPrices(this.fundService.getAllFundPrices());

    return userHandler.generateBaseGenericResponse(BusinessUser.class, businessUser, null, null);

  }

  @GetMapping(value = "/pushNotification")
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> pushNotification(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token)
      throws BusinessException, IOException {
    return userHandler.generateBaseGenericResponse(BusinessUser.class,
        this.businessUserService.pushNotification(this.getCurrentRequestHolder(token)), null, null);
  }

  @GetMapping(value = "/sendEmail")
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> sendEmail(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token)
      throws BusinessException, IOException {
    return userHandler.generateBaseGenericResponse(BusinessUser.class,
        this.businessUserService.sendEmail(this.getCurrentRequestHolder(token)), null, null);
  }

  @GetMapping(value = "/sendVerificationEmail")
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> sendVerificationEmail(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token)
      throws BusinessException, IOException {
    var user = this.getCurrentRequestHolder(token);
    if (user.getEmailAddress() == null)
      throw new BusinessException(ErrorCode.FAILED_TO_SEND_EMAIL);
    return userHandler.generateBaseGenericResponse(BusinessUser.class,
        this.businessUserService.sendOtpEmail(user, null), null, null);
  }

  @GetMapping(value = "/deleteAccount")
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> deleteAccount(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestParam(name = "reasonId") Integer reasonId) throws BusinessException, IOException {
    return userHandler.generateBaseGenericResponse(BusinessUser.class,
        this.businessUserService.deleteAccount(this.getCurrentRequestHolder(token), reasonId), null, null);
  }

  @GetMapping(value = "/getDeletionReasons")
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> getDeletionReasons(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token)
      throws BusinessException, IOException {
    return userHandler.generateBaseGenericResponse(BusinessUser.class,
        this.businessUserService.getDeletionReasons(this.getCurrentRequestHolder(token)), null, null);
  }

  @GetMapping(value = "/getFile")
  protected ResponseEntity<Resource> getFile(
      @RequestParam(name = "documentId", required = false) String documentId,
      @RequestParam(name = "documentType", required = false) String documentType,
      @RequestParam(name = "tokenized", required = true) boolean tokenized,
      @RequestParam(name = "token", required = false) String documentToken

  ) throws BusinessException, IOException, ClassNotFoundException {

    Entry<MediaType, ByteArrayResource> resourceWithExtension = this.businessUserService.showFile(null, this.jwtUtil,
        documentId, documentType, tokenized, documentToken);

    HttpHeaders headers = new HttpHeaders();
    headers.add("Cache-Control", "no-cache, no-store, must-revalidate");
    headers.add("Pragma", "no-cache");
    headers.add("Expires", "0");

    return ResponseEntity.ok()
        .headers(headers)
        .contentLength(resourceWithExtension.getValue().getByteArray().length)
        .contentType(resourceWithExtension.getKey())
        .body(resourceWithExtension.getValue());

  }

  @GetMapping(value = "/getTempFile")
  protected ResponseEntity<Resource> getTempFile(
      @RequestParam(name = "path") String path,
      @RequestParam(name = "fileName") String fileName,
      @RequestParam(name = "token") String token

  ) throws BusinessException, IOException, ClassNotFoundException {

    Entry<MediaType, ByteArrayResource> resourceWithExtension = this.businessUserService.showTempFile(null, fileName,
        path, token);
    MyLogger.info("FileName::" + fileName);
    MyLogger.info("Decrypted FileName::" + this.aes.decrypt(fileName));

    HttpHeaders headers = new HttpHeaders();
    headers.add("Cache-Control", "no-cache, no-store, must-revalidate");
    headers.add("Pragma", "no-cache");
    headers.add("Expires", "0");
    // headers.add("Content-Disposition", "attachment; filename="
    // +fileName!=null?this.aes.decrypt(fileName):null);
    headers.setContentDispositionFormData("attachment",
        fileName != null ? this.aes.decrypt(fileName.replaceAll(" ", "+")) : null);

    return ResponseEntity.ok()
        .headers(headers)
        .contentLength(resourceWithExtension.getValue().getByteArray().length)
        .contentType(resourceWithExtension.getKey())
        .body(resourceWithExtension.getValue());

  }

  @GetMapping(value = "/securityQuestions")
  public ResponseEntity<BaseGenericResponse<SecurityQuestion>> getQuestions() throws BusinessException {
    return securityQuestionHandler.generateBaseGenericResponse(SecurityQuestion.class, null,
        securityQuestionService.getAllQuestion(), null);

  }

  @PostMapping(value = "/saveSecurityQuestions")
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> saveUserQuestions(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody SecurityQuestionsListDto securityQuestionsListDto)
      throws BusinessException, IOException, FirebaseAuthException {

    var user = this.getCurrentRequestHolder(token);
    user = this.businessUserService.saveUserQuestions(user, securityQuestionsListDto.toUserSecurityQuestions());
    return userHandler.generateBaseGenericResponse(BusinessUser.class, user, null, null);
  }

  @GetMapping(value = "/appVersion", produces = { "application/json" })
  protected ResponseEntity<BaseGenericResponse<String>> getAppVersion() throws BusinessException {
    return singleAttributeHandler.generateBaseGenericResponse(AppVersionResponse.class,
        this.configProperties.getAppVersion(), null, null);
  }
}
