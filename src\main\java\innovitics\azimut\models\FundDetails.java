package innovitics.azimut.models;

import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Entity
@Table(name = "funds_details")
@Setter
@Getter
@ToString
public class FundDetails extends DbBaseEntity {
  @Column(name = "`key`")
  private String key;
  @Column(name = "`value`")
  private String value;

  @ManyToOne
  @JoinColumn(name = "fund_id", foreignKey = @javax.persistence.ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
  private Fund fund;

  private Boolean isDisplay;

}
