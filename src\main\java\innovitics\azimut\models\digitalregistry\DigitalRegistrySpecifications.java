package innovitics.azimut.models.digitalregistry;

import javax.persistence.criteria.Join;

import org.springframework.data.jpa.domain.Specification;

import innovitics.azimut.models.user.User;

public class DigitalRegistrySpecifications {

  public static Specification<DigitalRegistry> userNotNull() {
    return (root, query, criteriaBuilder) -> {
      Join<DigitalRegistry, User> digitalRegistryUser = root.join("user");
      return criteriaBuilder.isNotNull(digitalRegistryUser.get("id"));
    };
  }

  public static Specification<DigitalRegistry> userIdEquals(Long userId) {
    return (root, query, criteriaBuilder) -> {
      Join<DigitalRegistry, User> digitalRegistryUser = root.join("user");
      return criteriaBuilder.equal(digitalRegistryUser.get("id"), userId);
    };
  }

  public static Specification<DigitalRegistry> nationalIdEquals(String nationalId) {
    return (root, query, criteriaBuilder) -> {
      Join<DigitalRegistry, User> digitalRegistryUser = root.join("user");
      return criteriaBuilder.equal(digitalRegistryUser.get("userId"), nationalId);
    };
  }

  public static Specification<DigitalRegistry> actionEquals(DigitalRegistryAction action) {
    return (root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get("action"), action);
  }

  public static Specification<DigitalRegistry> beforeDateEquals(java.util.Date beforeDate) {
    return (root, query, criteriaBuilder) -> criteriaBuilder.lessThanOrEqualTo(root.get("createdAt"), beforeDate);
  }

  public static Specification<DigitalRegistry> afterDateEquals(java.util.Date afterDate) {
    return (root, query, criteriaBuilder) -> criteriaBuilder.greaterThanOrEqualTo(root.get("createdAt"), afterDate);
  }

}
