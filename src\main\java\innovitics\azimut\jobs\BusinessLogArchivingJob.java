package innovitics.azimut.jobs;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import innovitics.azimut.utilities.datautilities.DateUtility;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
@Scope(value = ConfigurableBeanFactory.SCOPE_SINGLETON, proxyMode = ScopedProxyMode.TARGET_CLASS)
public class BusinessLogArchivingJob extends ParentJob {

  @Override
  @Scheduled(cron = "00 59 23 * * *")
  public void scheduleFixedDelayTask() {
    super.scheduleFixedDelayTask();
    try {
      String sourceFile = this.getFileName() + ".log";
      FileOutputStream fos = new FileOutputStream(this.getFileName() + ".zip");
      ZipOutputStream zipOut = new ZipOutputStream(fos);

      File fileToZip = new File(sourceFile);
      FileInputStream fis = new FileInputStream(fileToZip);
      ZipEntry zipEntry = new ZipEntry(fileToZip.getName());
      zipOut.putNextEntry(zipEntry);

      byte[] bytes = new byte[1024];
      int length;
      while ((length = fis.read(bytes)) >= 0) {
        zipOut.write(bytes, 0, length);
      }

      zipOut.close();
      fis.close();
      fos.close();
      Path path = Paths.get(sourceFile);
      Files.deleteIfExists(path);
    } catch (Exception exception) {
      MyLogger.info("Could not update archive the file");
      exception.printStackTrace();
    }
  }

  @Override
  public String getName() {
    return this.getClass().getName();
  }

  String getFileName() {
    return this.configProperties.getLogFilePath() + "\\business" + DateUtility.getCurrentDayMonthYear();
  }

}
