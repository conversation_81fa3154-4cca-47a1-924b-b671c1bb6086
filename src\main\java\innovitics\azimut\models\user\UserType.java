package innovitics.azimut.models.user;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import innovitics.azimut.models.DbBaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Entity
@Table(name = "user_types")
@Setter
@Getter
@ToString
public class UserType extends DbBaseEntity {
  private String idType;
  private int noOfKYCPages;
  private Integer valifyImageCount;
  private Date deletedAt;
  private Long firstPageId;
  private Long azimutIdTypeId;
  private String idTypeAr;

}
