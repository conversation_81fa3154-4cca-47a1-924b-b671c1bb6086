package innovitics.azimut.models;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Entity
@Table(name = "notifications")
@Data
public class Notification {
  @Id
  @GeneratedValue(strategy = GenerationType.AUTO)
  private Long id;
  private String notificationText;
  private String notificationSource;
  private String notificationHeader;
  private Long userId;
  private String notificationTextAr;
  private String notificationHeaderAr;
  private String link;
  private Boolean isRead;
  private Integer navigation;
  private Integer liveNavigation;
  private Long fundId;
  private String notificationType;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  private Date createdAt;

}
