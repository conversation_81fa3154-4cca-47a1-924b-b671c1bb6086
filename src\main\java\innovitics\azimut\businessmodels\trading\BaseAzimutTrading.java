package innovitics.azimut.businessmodels.trading;

import org.springframework.web.multipart.MultipartFile;

import innovitics.azimut.models.user.UserBlockage;
import innovitics.azimut.utilities.CustomJsonRootName;
import lombok.Data;

@Data
@CustomJsonRootName(plural = "trading", singular = "trading")
public class BaseAzimutTrading {

  private Long orderTypeId;
  private Double orderValue;
  private Integer quantity;
  private Long currencyId;
  private Long accountId;
  private Long bankId;
  private Long orderId;
  private MultipartFile injectionDocument;
  private String userId;
  private UserBlockage userBlockage;
  private Long moduleTypeId;
  private String fileBytes;
  private Integer threshold;
  private Long transactionId;
  private String referenceNo;
  protected Long azIdType;
  protected String azId;
  private Long fundId;
  private String partnerUserName;

}
