package innovitics.azimut.businessservices;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.rest.apis.optosigner.OptoSignerAuth;
import innovitics.azimut.rest.entities.optosigner.OptoSignerAuthOutput;
import innovitics.azimut.security.JwtUtil;
import innovitics.azimut.utilities.exceptionhandling.ExceptionHandler;

@Service
public class OptoSignerService {

  @Autowired
  private OptoSignerAuth optoSignerAuth;

  @Autowired
  private JwtUtil jwtUtil;

  @Autowired
  protected ExceptionHandler exceptionHandler;

  private String token;

  public String getAccessToken() {
    if (token == null || jwtUtil.isExternalTokenExpired(token)) {
      try {
        OptoSignerAuthOutput res = optoSignerAuth.getData(null);
        token = res.getAccessToken();
      } catch (IntegrationException e) {
        this.exceptionHandler.logException(e);
      }
    }
    return token;
  }

}
