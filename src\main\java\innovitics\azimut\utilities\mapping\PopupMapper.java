package innovitics.azimut.utilities.mapping;

import org.springframework.stereotype.Component;

import innovitics.azimut.businessmodels.user.BusinessPopup;
import innovitics.azimut.models.Popup;
import innovitics.azimut.utilities.datautilities.BooleanUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;

@Component
public class PopupMapper extends Mapper<Popup, BusinessPopup> {

  @Override
  protected Popup convertBusinessUnitToBasicUnit(BusinessPopup businessPopup, boolean save) {
    return null;
  }

  @Override
  protected BusinessPopup convertBasicUnitToBusinessUnit(Popup popup) {
    return null;
  }

  @Override
  protected BusinessPopup convertBasicUnitToBusinessUnit(Popup popup, String language) {
    BusinessPopup businessPopup = new BusinessPopup();
    businessPopup.setId(popup.getId());
    businessPopup.setPopupHeader(StringUtility.determineOuputLanguage(
        popup.getPopupTemplate().getTitle(), popup.getPopupTemplate().getTitleAr(), language));
    businessPopup.setPopupText(StringUtility.determineOuputLanguage(popup.getPopupTemplate().getBody(),
        popup.getPopupTemplate().getBodyAr(), language));
    businessPopup.setIsRead(BooleanUtility.getValue(popup.getIsRead()));
    businessPopup.setCreatedAt(popup.getCreatedAt());
    businessPopup.setAction(popup.getPopupTemplate().getAction());
    businessPopup.setImage(popup.getPopupTemplate().getImage());
    businessPopup.setPopupTemplateId(popup.getPopupTemplate().getId());
    return businessPopup;
  }

}
