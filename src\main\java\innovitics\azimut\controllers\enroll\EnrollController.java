package innovitics.azimut.controllers.enroll;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.util.StreamUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import innovitics.azimut.businessmodels.enroll.BusinessEnroll;
import innovitics.azimut.businessmodels.user.Token;
import innovitics.azimut.businessservices.BusinessUserService;
import innovitics.azimut.businessservices.EnrollService;
import innovitics.azimut.controllers.BaseController;
import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.controllers.GenericResponseHandler;
import innovitics.azimut.controllers.admin.DTOs.RecordIdDto;
import innovitics.azimut.controllers.enroll.DTOs.EnrollKycDto;
import innovitics.azimut.controllers.enroll.DTOs.EnrollRequestIdDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.rest.entities.enroll.EnrollApplication;
import innovitics.azimut.rest.entities.enroll.EnrollAuthOutput;
import innovitics.azimut.security.JwtUtil;
import innovitics.azimut.services.digitalregistry.DigitalRegistryService;
import innovitics.azimut.utilities.datautilities.StringUtility;

@RestController
@Validated
@RequestMapping("/api/enroll")
public class EnrollController extends BaseController {

  private @Autowired EnrollService enrollService;
  private @Autowired GenericResponseHandler<BusinessEnroll> businessEnrollHandler;
  private @Autowired GenericResponseHandler<Boolean> booleanHandler;
  private @Autowired GenericResponseHandler<EnrollAuthOutput> onboardingSessionHandler;
  private @Autowired DigitalRegistryService digitalRegistryService;
  private @Autowired BusinessUserService businessUserService;
  protected @Autowired JwtUtil jwtUtil;

  @PostMapping(value = "/setRequestId", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE }, produces = {
          MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE })
  protected ResponseEntity<BaseGenericResponse<Boolean>> setRequestId(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody EnrollRequestIdDto enrollRequestIdDto)
      throws BusinessException {

    var user = this.getCurrentRequestHolder(token);
    enrollService.setRequestId(user, enrollRequestIdDto);
    return booleanHandler.generateBaseGenericResponse(Boolean.class, Boolean.TRUE, null, null);

  }

  @PostMapping(value = "/verify", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE }, produces = {
          MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessEnroll>> verify(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody EnrollKycDto enrollKycDto,
      @RequestHeader(name = StringUtility.IP, required = false) String ip)
      throws BusinessException, IOException, IntegrationException {

    var user = this.getCurrentRequestHolder(token);
    BusinessEnroll response = enrollService.checkAndVerify(user, language);
    this.digitalRegistryService.recordApproveEKYC(ip, user.getId(), token);
    return businessEnrollHandler.generateBaseGenericResponse(BusinessEnroll.class, response, null, null);

  }

  @PostMapping(value = "/update", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE }, produces = {
          MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessEnroll>> updateId(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @RequestHeader(name = StringUtility.IP, required = false) String ip)
      throws BusinessException, IOException, IntegrationException {

    var user = this.getCurrentRequestHolder(token);
    BusinessEnroll response = enrollService.checkAndUpdate(user, language);
    return businessEnrollHandler.generateBaseGenericResponse(BusinessEnroll.class, response, null, null);
  }

  @DeleteMapping(value = "/deleteApplicant")
  protected ResponseEntity<BaseGenericResponse<Boolean>> verify(
      @RequestParam Optional<String> update,
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token) throws BusinessException, IntegrationException {
    var user = this.getCurrentRequestHolder(token);
    boolean isUpdate = StringUtility.stringsMatch(update.orElse("false"), "true");
    return booleanHandler.generateBaseGenericResponse(Boolean.class, enrollService.deleteParticipant(user, isUpdate),
        null, null);
  }

  @PostMapping(value = "/synchronizeCustom", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<Boolean>> synchronizeFra(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody RecordIdDto record)
      throws BusinessException, IOException, IntegrationException {
    this.getCurrentRequestHolder(token);
    // businessUserService.rerunFraScript();
    businessUserService.addReviewsForAutoAccept();
    return booleanHandler.generateBaseGenericResponse(Boolean.class, Boolean.TRUE, null, null);
  }

  @PostMapping(value = "/synchronizeSingleUserData", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<Boolean>> synchronizeSingleUserData(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody RecordIdDto record)
      throws BusinessException, IOException, IntegrationException {
    this.getCurrentRequestHolder(token);
    var user = businessUserService.getByUserId(record.getId());
    enrollService.updateSingleIdData(user.getEnrollApplicantId());
    return booleanHandler.generateBaseGenericResponse(Boolean.class, Boolean.TRUE, null, null);
  }

  @PostMapping(value = "/synchronizeSingleUserImages", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<Boolean>> synchronizeSingleUserImages(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody RecordIdDto record)
      throws BusinessException, IOException, IntegrationException {
    this.getCurrentRequestHolder(token);
    enrollService.syncUserImages(record.getId());
    return booleanHandler.generateBaseGenericResponse(Boolean.class, Boolean.TRUE, null, null);
  }

  @PostMapping(value = "/synchronizeEnrollData", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<Boolean>> synchronizeEnrollData(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token)
      throws BusinessException, IOException, IntegrationException {
    this.getCurrentRequestHolder(token);
    enrollService.updateIdData();
    return booleanHandler.generateBaseGenericResponse(Boolean.class, Boolean.TRUE, null, null);
  }

  @PostMapping(value = "/getAccessToken", consumes = { MediaType.APPLICATION_FORM_URLENCODED_VALUE }, produces = {
      MediaType.APPLICATION_JSON_VALUE })
  protected String getAccessToken(
      HttpServletRequest request,
      HttpServletResponse response) throws IOException {
    byte[] body = StreamUtils.copyToByteArray(request.getInputStream());
    String requestBody = new String(body, 0, body.length, request.getCharacterEncoding());
    var data = requestBody.split("&");
    var client_id = Arrays.stream(data).filter(x -> x.contains("client_id")).toArray()[0].toString().split("=")[1];
    var client_secret = Arrays.stream(data).filter(x -> x.contains("client_secret")).toArray()[0].toString()
        .split("=")[1];

    System.out.println(requestBody);
    if (StringUtility.stringsMatch(client_id, configProperties.getEnrollClientId()) &&
        StringUtility.stringsMatch(client_secret, configProperties.getEnrollClientSecret())) {
      UserDetails user = new User("enroll", " ", new ArrayList<>());
      Token token = jwtUtil.generateToken(user);
      return "{ \"access_token\": \""
          + token.getTokenString()
          + "\", \"expires_in\": 600000, \"token_type\": \"Bearer\", \"scope\": \"IdentityServerApi\" }";
    } else {
      response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
      return "{ \"error\": \"Invalid client id or secret\"}";
    }
  }

  @PostMapping(value = "/enrollCallback")
  protected String getAccessToken(
      @RequestHeader(name = "Callback-Test", required = false) String isTest,
      @RequestBody List<EnrollApplication> applicants,
      HttpServletResponse response) throws BusinessException, IntegrationException, IOException {
    if (isTest != null)
      return "";
    for (EnrollApplication application : applicants) {
      this.enrollService.enrollCallback(application);
    }
    response.setStatus(HttpServletResponse.SC_CREATED);
    return "";
  }

  @GetMapping(value = "/onboardingSessionToken")
  protected ResponseEntity<BaseGenericResponse<EnrollAuthOutput>> onboardingSessionToken(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token) throws BusinessException, IntegrationException {
    this.getCurrentRequestHolder(token);
    return onboardingSessionHandler.generateBaseGenericResponse(EnrollAuthOutput.class, enrollService.getSessionToken(),
        null, null);
  }

}
