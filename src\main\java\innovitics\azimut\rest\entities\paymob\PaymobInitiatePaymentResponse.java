package innovitics.azimut.rest.entities.paymob;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonProperty;

import innovitics.azimut.rest.entities.BaseRestRequest;
import lombok.Data;

@Data
public class PaymobInitiatePaymentResponse extends BaseRestRequest {

  @JsonProperty("intention_order_id")
  private Integer intentionOrderId;
  private String id;

  @JsonProperty("client_secret")
  private String clientSecret;
  @JsonProperty("special_reference")
  private String specialReference;

  private Boolean confirmed;
  private String status;
  private Date created;
}
