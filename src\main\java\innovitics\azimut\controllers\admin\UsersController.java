package innovitics.azimut.controllers.admin;

import java.io.IOException;
import java.text.ParseException;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import innovitics.azimut.businessmodels.user.BusinessAzimutClient;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.businessservices.BusinessClientDetailsService;
import innovitics.azimut.businessservices.BusinessUserChildService;
import innovitics.azimut.businessservices.BusinessUserService;
import innovitics.azimut.businessservices.FraService;
import innovitics.azimut.businessutilities.BusinessSearchCriteria;
import innovitics.azimut.controllers.BaseController;
import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.controllers.GenericResponseHandler;
import innovitics.azimut.controllers.users.DTOs.EditUserAndSubmitReviewDto;
import innovitics.azimut.controllers.users.DTOs.GetBalanceAndFundOwnershipDto;
import innovitics.azimut.controllers.users.DTOs.GetByIdDto;
import innovitics.azimut.controllers.users.DTOs.TeaComputersCallbackDto;
import innovitics.azimut.controllers.users.DTOs.VerifyUserByTCDto;
import innovitics.azimut.controllers.users.DTOs.VerifyUserDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.security.TeaComputersSignatureGenerator;
import innovitics.azimut.services.user.UserService;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.dbutilities.CountUserEntity;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;

@RestController
@RequestMapping(value = "/admin/users", produces = {
    MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE })
public class UsersController extends BaseController {

  @Autowired
  BusinessUserChildService businessUserChildService;

  @Autowired
  UserService userService;

  @Autowired
  BusinessUserService businessUserService;

  @Autowired
  BusinessClientDetailsService businessClientDetailsService;

  @Autowired
  GenericResponseHandler<BusinessAzimutClient> businessAzimutClientResponseHandler;

  @Autowired
  FraService fraService;

  @Autowired
  GenericResponseHandler<BusinessUser> businessUserHandler;

  @Autowired
  GenericResponseHandler<CountUserEntity> countUserHandler;

  @Autowired
  GenericResponseHandler<Integer> integerHandler;

  @Autowired
  GenericResponseHandler<String> stringHandler;

  @Autowired
  TeaComputersSignatureGenerator teaComputersSignatureGenerator;

  private @Autowired GenericResponseHandler<Boolean> booleanHandler;

  @GetMapping(value = "/stats")
  protected ResponseEntity<BaseGenericResponse<CountUserEntity>> stats(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestParam(name = "startDate", required = false) String startDate,
      @RequestParam(name = "endDate", required = false) String endDate)
      throws BusinessException, IOException, ParseException {
    this.getCurrentAdminRequestHolder(token);
    return countUserHandler.generateBaseGenericResponse(CountUserEntity.class, null,
        this.userService.userStats(startDate, endDate), null);
  }

  @GetMapping(value = "/kycStats")
  protected ResponseEntity<BaseGenericResponse<Integer>> kycStats(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestParam(name = "startDate", required = false) String startDate,
      @RequestParam(name = "endDate", required = false) String endDate)
      throws BusinessException, IOException, ParseException {
    this.getCurrentAdminRequestHolder(token);
    return integerHandler.generateBaseGenericResponse(Integer.class, null,
        this.userService.userKycStats(startDate, endDate), null);
  }

  @GetMapping(value = "/referralCodes")
  protected ResponseEntity<BaseGenericResponse<String>> referralCodes(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token)
      throws BusinessException, IOException, ParseException {
    this.getCurrentAdminRequestHolder(token);
    return stringHandler.generateBaseGenericResponse(String.class, null,
        this.userService.referralCodes(), null);
  }

  @PostMapping(value = "/listUsersFiltered", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> listUsersFiltered(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody BusinessSearchCriteria businessSearchCriteria)
      throws BusinessException, IOException {
    return businessUserHandler.generateBaseGenericResponse(BusinessUser.class, null, null, this.businessUserChildService
        .listUsersFiltered(this.getCurrentAdminRequestHolder(token), businessSearchCriteria));
  }

  @GetMapping(value = "/listUsers")
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> listUsers(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token, Integer type)
      throws BusinessException, IOException {
    return businessUserHandler.generateBaseGenericResponse(BusinessUser.class, null,
        this.businessUserChildService.listUsers(this.getCurrentAdminRequestHolder(token)), null);
  }

  @PostMapping(value = "/assignKycStatus", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> assignKycStatus(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody GetByIdDto businessUser) throws BusinessException, IOException {
    return businessUserHandler.generateBaseGenericResponse(BusinessUser.class, this.businessUserChildService
        .assignUserKycStatus(this.getCurrentAdminRequestHolder(token), businessUser, language), null, null);
  }

  @PostMapping(value = "/getClientOCRDetails", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> getClientOCRDetails(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody GetByIdDto businessUser) throws BusinessException, IOException {
    return businessUserHandler.generateBaseGenericResponse(BusinessUser.class,
        this.businessUserChildService.beautifyUser(this.businessUserChildService
            .getClientOCRDetailsAndImages(this.getCurrentAdminRequestHolder(token), businessUser.getId(), language)),
        null, null);
  }

  @PostMapping(value = "/editUserAndSubmitReview", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> editUserAndSubmitReview(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody EditUserAndSubmitReviewDto businessUser)
      throws BusinessException, IOException, IntegrationException {
    return businessUserHandler.generateBaseGenericResponse(BusinessUser.class, this.businessUserChildService
        .editUserAndSubmitReview(this.getCurrentAdminRequestHolder(token), businessUser, language), null, null);
  }

  @PostMapping(value = "/editUserOnly", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> editUserOnly(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody EditUserAndSubmitReviewDto businessUser)
      throws BusinessException, IOException, IntegrationException {
    this.getCurrentAdminRequestHolder(token);
    return businessUserHandler.generateBaseGenericResponse(BusinessUser.class, this.businessUserChildService
        .editUserOnly(businessUser), null, null);
  }

  @PostMapping(value = "/verifyUser", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> verifyUser(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody VerifyUserDto businessUser) throws BusinessException, IOException, IntegrationException {
    return businessUserHandler.generateBaseGenericResponse(BusinessUser.class,
        this.businessUserChildService.verifyUser(this.getCurrentAdminRequestHolder(token), businessUser, language),
        null, null);
  }

  @PostMapping(value = "/sendToFits", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<Boolean>> sendToFits(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody GetByIdDto dto)
      throws BusinessException, IOException, IntegrationException {
    var businessUser = this.businessUserService.findUserById(dto.getId(), true);
    this.businessUserService.addUserToFits(businessUser);
    return booleanHandler.generateBaseGenericResponse(Boolean.class,
        true, null, null);
  }

  @PostMapping(value = "/recheckCso", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<Boolean>> recheckCso(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody GetByIdDto dto)
      throws Exception {
    var user = this.businessUserService.findUserById(dto.getId(), true);
    this.fraService.checkCso(user, true);
    this.businessUserService.postFraActions(user);
    this.businessUserService.autoAcceptActions(user, true);
    return booleanHandler.generateBaseGenericResponse(Boolean.class,
        true, null, null);
  }

  @PostMapping(value = "/recheckNtra", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<Boolean>> recheckNtra(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody GetByIdDto dto)
      throws Exception {
    var user = this.businessUserService.findUserById(dto.getId(), true);
    this.fraService.checkNtra(user, true);
    this.businessUserService.postFraActions(user);
    this.businessUserService.autoAcceptActions(user, true);
    return booleanHandler.generateBaseGenericResponse(Boolean.class,
        true, null, null);
  }

  @PostMapping(value = "/recheckAml", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<Boolean>> recheckAml(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody GetByIdDto dto)
      throws Exception {
    var user = this.businessUserService.findUserById(dto.getId(), true);
    this.fraService.checkAml(user, true);
    this.businessUserService.postFraActions(user);
    this.businessUserService.autoAcceptActions(user, true);
    return booleanHandler.generateBaseGenericResponse(Boolean.class,
        true, null, null);
  }

  @PostMapping(value = "/bypassAml", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<Boolean>> bypassAml(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody GetByIdDto dto)
      throws BusinessException, IOException, IntegrationException {
    var user = this.businessUserService.findUserById(dto.getId(), true);
    this.businessUserService.bypassAml(user);
    this.businessUserService.postFraActions(user);
    this.businessUserService.autoAcceptActions(user, true);
    return booleanHandler.generateBaseGenericResponse(Boolean.class,
        true, null, null);
  }

  @PostMapping(value = "/getClientFunds", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessAzimutClient>> getClientFunds(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody GetBalanceAndFundOwnershipDto dto)
      throws BusinessException, IOException, IntegrationException {
    var user = this.businessUserService.findUserById(dto.getId(), true);
    return businessAzimutClientResponseHandler.generateBaseGenericResponse(BusinessAzimutClient.class,
        this.businessClientDetailsService
            .getPaginatedClientFunds(user, dto, language),
        null, null);
  }

  @PostMapping(value = "/getAzimutClientBalanceAndTransactions", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessAzimutClient>> getAzimutClientBalanceAndTransactions(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody GetBalanceAndFundOwnershipDto dto)
      throws BusinessException, IOException, IntegrationException {
    var user = this.businessUserService.findUserById(dto.getId(), true);
    return businessAzimutClientResponseHandler.generateBaseGenericResponse(BusinessAzimutClient.class,
        this.businessClientDetailsService
            .getBalanceAndTransactions(dto, user, language),
        null, null);
  }

  @PostMapping(value = "/sendOfflineContract", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<Boolean>> sendOfflineContract(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody GetByIdDto dto)
      throws BusinessException, IOException, IntegrationException {
    var user = this.businessUserService.findUserById(dto.getId(), true);
    businessUserChildService.sendOfflineContractAndPushNotification(user, "en");
    return booleanHandler.generateBaseGenericResponse(Boolean.class,
        true, null, null);
  }

  @PostMapping(value = "/approveOfflineContract", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<Boolean>> approveOfflineContract(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody GetByIdDto dto)
      throws BusinessException, IOException, IntegrationException {
    var user = this.businessUserService.findUserById(dto.getId(), true);
    businessUserChildService.approveOfflineContract(user);
    return booleanHandler.generateBaseGenericResponse(Boolean.class,
        true, null, null);
  }

  @PostMapping(value = "/resyncUser", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<Boolean>> resyncUser(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody GetByIdDto dto)
      throws BusinessException, IOException, IntegrationException {
    var user = this.businessUserService.findUserById(dto.getId(), true);
    businessUserChildService.resyncUser(user);
    return booleanHandler.generateBaseGenericResponse(Boolean.class,
        true, null, null);
  }

  @PostMapping(value = "/unblockUser", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<Boolean>> unblockUser(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody GetByIdDto dto)
      throws BusinessException, IOException, IntegrationException {
    var user = this.businessUserService.findUserById(dto.getId(), true);
    businessUserService.unblockUser(user);
    return this.booleanHandler.generateBaseGenericResponse(Boolean.class, Boolean.TRUE,
        null, null);
  }

  @PostMapping(value = "/resetChangePhoneAttempts", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<Boolean>> resetChangePhoneAttempts(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody GetByIdDto dto)
      throws BusinessException, IOException, IntegrationException {
    var user = this.businessUserService.findUserById(dto.getId(), true);
    businessUserService.resetChangePhoneAttempts(user);
    return this.booleanHandler.generateBaseGenericResponse(Boolean.class, Boolean.TRUE,
        null, null);
  }

  @PostMapping(value = "/sendToFra", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<Boolean>> sendToFra(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody GetByIdDto dto)
      throws Exception {
    var user = this.businessUserService.findUserById(dto.getId(), true);
    byte[] contractBytes = businessUserChildService.getDocument(null, user.getId(), "en");
    this.fraService.sendContract(user, false, contractBytes);
    this.businessUserService.addReviewIfNotExist(user);
    return this.booleanHandler.generateBaseGenericResponse(Boolean.class, Boolean.TRUE,
        null, null);
  }

  @PostMapping(value = "/signUserContract", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> signUserContract(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @RequestParam("userId") Long userId,
      @RequestParam("file") MultipartFile file)
      throws Exception {
    var user = this.businessUserChildService.signUserPdf(userId, file, language);
    byte[] contractBytes = businessUserChildService.getCompanySignedPdf(user).toByteArray();
    this.fraService.sendContract(user, true, contractBytes);
    return businessUserHandler.generateBaseGenericResponse(BusinessUser.class,
        user,
        null, null);
  }

  @PostMapping(value = "/sendCompanyContractToFra", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<Boolean>> sendCompanyContractToFra(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody GetByIdDto dto)
      throws Exception {
    var user = this.businessUserService.findUserById(dto.getId(), true);
    byte[] contractBytes = businessUserChildService.getCompanySignedPdf(user).toByteArray();
    this.fraService.sendContract(user, true, contractBytes);
    return this.booleanHandler.generateBaseGenericResponse(Boolean.class, Boolean.TRUE,
        null, null);
  }

  @PostMapping(value = "/fitsCallback", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected String fitsCallback(
      @Valid @RequestBody TeaComputersCallbackDto data) throws BusinessException, IOException, IntegrationException {
    String signature = "";
    if (StringUtility.stringsMatch(data.getNotificationType(), "User"))
      signature = teaComputersSignatureGenerator.generateSignature(true, "", data.getIdTypeId().toString(),
          data.getIdNumber());
    else if (StringUtility.stringsMatch(data.getNotificationType(), "Order"))
      signature = teaComputersSignatureGenerator.generateSignature(true, "",
          data.getOrder().getTransactionID().toString(),
          data.getOrder().getFundId().toString());
    else if (StringUtility.stringsMatch(data.getNotificationType(), "Transaction"))
      signature = teaComputersSignatureGenerator.generateSignature(true, "", data.getIdTypeId().toString(),
          data.getIdNumber(), data.getTransaction().getOrderId());
    else
      throw new IntegrationException(ErrorCode.INVALID_NOTIFICATION_TYPE);

    if (StringUtility.stringsDontMatch(signature, data.getSignature()))
      throw new IntegrationException(ErrorCode.INVALID_SIGNATURE);

    this.businessUserService.sendCallbackNotification(data);
    return "{\"status\": \"ok\"}";
  }

  @PostMapping(value = "/verifyUserByTC", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessUser>> verifyUserByTC(
      @Valid @RequestBody VerifyUserByTCDto businessUser) throws BusinessException, IOException, IntegrationException {
    return businessUserHandler.generateBaseGenericResponse(BusinessUser.class,
        this.businessUserChildService.verifyUserByTC(businessUser), null, null);
  }

  @GetMapping(value = "/contract.pdf")
  public ResponseEntity<Resource> download(@RequestHeader(name = StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @RequestParam(name = "userId") Long userId) throws IOException, BusinessException {

    BusinessUser user = this.businessUserChildService.findUserById(userId, true);
    byte[] contractFile;

    var signedContractFile = (user.getSignedPdf() != null) ? this.businessUserChildService.getCompanySignedPdf(user)
        : null;

    if (signedContractFile == null)
      signedContractFile = this.businessUserChildService.getSignedPdf(user);

    if (signedContractFile != null)
      contractFile = signedContractFile.toByteArray();
    else
      contractFile = this.businessUserChildService.getDocument(this.getCurrentAdminRequestHolder(token), userId,
          language);

    ByteArrayResource resource = new ByteArrayResource(contractFile);
    HttpHeaders headers = new HttpHeaders();
    headers.add("Cache-Control", "no-cache, no-store, must-revalidate");
    headers.add("Pragma", "no-cache");
    headers.add("Expires", "0");

    return ResponseEntity.ok()
        .headers(headers)
        .contentLength(contractFile.length)
        .contentType(MediaType.APPLICATION_OCTET_STREAM)
        .body(resource);
  }
}
