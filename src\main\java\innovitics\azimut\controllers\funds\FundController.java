package innovitics.azimut.controllers.funds;

import java.io.IOException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import innovitics.azimut.businessmodels.funds.BusinessFundPrice;
import innovitics.azimut.businessservices.BusinessFundsService;
import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.controllers.GenericResponseHandler;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.utilities.datautilities.StringUtility;

@RestController
@RequestMapping("/api/fund")
public class FundController {

  @Autowired
  BusinessFundsService businessFundsService;

  @Autowired
  GenericResponseHandler<BusinessFundPrice> businessFundPriceResponseHandler;

  @GetMapping(value = "/updateFundPrices",

      produces = { MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessFundPrice>> updateFundPrices(
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language)
      throws BusinessException, IOException, IntegrationException {

    /*
     * List<BusinessFundPrice> business=new ArrayList<BusinessFundPrice>();
     * Collections.addAll(business, businessFundPrices);
     */
    return businessFundPriceResponseHandler.generateBaseGenericResponse(BusinessFundPrice.class, null,
        this.businessFundsService.updateFundPrices(),
        null);
  }

}
