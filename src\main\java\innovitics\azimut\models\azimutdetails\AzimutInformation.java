package innovitics.azimut.models.azimutdetails;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import innovitics.azimut.models.DbBaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Entity
@Table(name = "azimut_information")
@Setter
@Getter
@ToString
public class AzimutInformation extends DbBaseEntity {
  private String question;
  private String answer;
  private String questionAr;
  private String answerAr;
  private Integer informationType;
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  private Date deletedAt;

}
