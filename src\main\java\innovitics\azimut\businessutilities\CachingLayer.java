package innovitics.azimut.businessutilities;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.utilities.ParentUtility;
import innovitics.azimut.utilities.logging.MyLogger;
import net.sf.ehcache.Cache;
import net.sf.ehcache.CacheManager;
import net.sf.ehcache.Element;

@Component
public class CachingLayer extends ParentUtility {

  @Autowired
  CacheManager singletonManager;
  protected static final Logger logger = LoggerFactory.getLogger(CachingLayer.class);

  public Object getValueIfExisting(Object object, String methodName, Object[] parameters, Class<?>[] paramterTypes,
      String cacheKey, int timeToLive, int timeToIdle) throws BusinessException {
    /*
     * DiskStoreConfiguration diskStoreConfiguration = new DiskStoreConfiguration();
     * diskStoreConfiguration.setPath("/my/path/dir");
     * Configuration configuration=new Configuration();
     * configuration.addDiskStore(diskStoreConfiguration);
     * CacheManager mgr = new CacheManager(configuration);
     */
    Cache cache = singletonManager.getCache("cacheLayer");
    if (cache.get(cacheKey) == null) {
      MyLogger.info("Cache Empty:::");
      Object result = this.getValueUsingReflection(object, methodName, parameters, paramterTypes);
      Element element = new Element(cacheKey, result);
      element.setTimeToLive(timeToLive);
      element.setTimeToIdle(timeToIdle);
      cache.put(element);
      return result;
    } else {
      MyLogger.info("Cache Populated:::");
      return cache.get(cacheKey).getObjectValue();
    }
  }

}
