package innovitics.azimut.controllers.admin;

import java.io.IOException;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import innovitics.azimut.businessmodels.BusinessPayment;
import innovitics.azimut.businessservices.BusinessPaymentService;
import innovitics.azimut.businessutilities.BusinessSearchCriteria;
import innovitics.azimut.controllers.BaseController;
import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.controllers.GenericResponseHandler;
import innovitics.azimut.controllers.admin.DTOs.RecordIdDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.utilities.datautilities.StringUtility;

@RestController
@RequestMapping("/admin/injection")
public class InjectionController extends BaseController {

  @Autowired
  GenericResponseHandler<BusinessPayment> paymentHandler;

  @Autowired
  BusinessPaymentService businessPaymentService;

  private @Autowired GenericResponseHandler<Boolean> booleanHandler;

  @PostMapping(value = "/list")
  protected ResponseEntity<BaseGenericResponse<BusinessPayment>> listInjections(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody BusinessSearchCriteria businessSearchCriteria)
      throws BusinessException, IOException {
    this.getCurrentAdminRequestHolder(token);
    return this.paymentHandler.generateBaseGenericResponse(BusinessPayment.class, null,
        null, this.businessPaymentService.listTransactions(businessSearchCriteria));
  }

  @PostMapping(value = "/retry")
  protected ResponseEntity<BaseGenericResponse<Boolean>> retryInjection(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody RecordIdDto injectRecordIdDto)
      throws IntegrationException, Exception {
    this.getCurrentAdminRequestHolder(token);
    this.businessPaymentService.retryInjection(injectRecordIdDto.getId());
    return this.booleanHandler.generateBaseGenericResponse(Boolean.class, Boolean.TRUE,
        null, null);
  }

}
