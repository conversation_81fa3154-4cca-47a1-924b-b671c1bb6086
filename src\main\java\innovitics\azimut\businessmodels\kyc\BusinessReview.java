package innovitics.azimut.businessmodels.kyc;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import innovitics.azimut.models.kyc.Reason;
import innovitics.azimut.utilities.CustomJsonRootName;
import lombok.Data;

@Data
@CustomJsonRootName(plural = "data", singular = "data")
public class BusinessReview {

  protected Long id;
  protected Long questionId;
  protected Long pageId;
  protected Integer pageOrder;
  protected Long status;
  protected Long actionMaker;
  protected Long reasonId;
  protected Long userId;
  protected List<Long> pageIds;
  protected Reason reason;
  protected Reason[] reasons;
  protected String comment;
  protected Long bankAccountId;
  protected Boolean mandatoryQuestion;
  protected Long appUserId;
  protected Long accountId;
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  protected Date createdAt;

  public BusinessReview(Reason[] reasons) {
    this.reasons = reasons;
  }

  public BusinessReview() {
  }

}
