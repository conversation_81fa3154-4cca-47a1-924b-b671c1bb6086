package innovitics.azimut.repositories.digitalregistry;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import innovitics.azimut.models.digitalregistry.DigitalRegistry;

@Repository
public interface DigitalRegistryRepository
    extends JpaRepository<DigitalRegistry, Long>, JpaSpecificationExecutor<DigitalRegistry> {
  public Page<DigitalRegistry> findByUserId(Pageable pageable, Long userId);

}
