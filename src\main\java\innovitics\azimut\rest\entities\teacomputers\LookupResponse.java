package innovitics.azimut.rest.entities.teacomputers;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class LookupResponse extends TeaComputerResponse {

  @JsonProperty("sytemCountryCode")
  private Long systemCountryCode;
  private Long countryId;
  private String englishCountryName;
  private String arabicCountryName;

  @JsonProperty("sytemCityCode")
  private Long systemCityCode;
  private Long cityId;
  private String englishCityName;
  private String arabicCityName;

  @JsonProperty("sytemNationalityCode")
  private Long systemNationalityCode;
  private Long nationalityId;
  private String englishNationalityName;
  private String arabicNationalityName;

  private Long bankId;
  private Long systemBankCode;
  private String englishBankName;
  private String arabicBankName;
  private String bankType;

  private Long systemBranchCode;
  private Long branchId;
  private String englishBranchName;
  private String arabicBranchName;

  private Long systemCurrencyCode;
  private Long currencyId;
  private String englishCurrencyName;
  private String arabicCurrencyName;

  @Override
  public String toString() {
    return "LookUpResponse [systemCountryCode=" + systemCountryCode + ", countryId=" + countryId
        + ", englishCountryName=" + englishCountryName + ", arabicCountryName=" + arabicCountryName
        + ", systemCityCode=" + systemCityCode + ", cityId=" + cityId + ", englishCityName=" + englishCityName
        + ", arabicCityName=" + arabicCityName + ", systemNationalityCode=" + systemNationalityCode
        + ", nationalityId=" + nationalityId + ", englishNationalityName=" + englishNationalityName
        + ", arabicNationalityName=" + arabicNationalityName + "]";
  }
}