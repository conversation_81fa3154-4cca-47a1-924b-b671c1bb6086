package innovitics.azimut.businessservices;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Base64;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.models.digitalregistry.DigitalRegistryAction;
import innovitics.azimut.models.kyc.ReasonType;
import innovitics.azimut.rest.apis.fra.FraAmlApi;
import innovitics.azimut.rest.apis.fra.FraCsoApi;
import innovitics.azimut.rest.apis.fra.FraNtraApi;
import innovitics.azimut.rest.apis.fra.FraSendContractApi;
import innovitics.azimut.rest.entities.enroll.EnrollLocationInfo;
import innovitics.azimut.rest.entities.fra.FraAmlInput;
import innovitics.azimut.rest.entities.fra.FraCsoInput;
import innovitics.azimut.rest.entities.fra.FraNtraInput;
import innovitics.azimut.rest.entities.fra.FraSendContractInput;
import innovitics.azimut.services.digitalregistry.DigitalRegistryService;
import innovitics.azimut.services.kyc.ReasonService;
import innovitics.azimut.services.kyc.ReviewService;
import innovitics.azimut.utilities.crosslayerenums.KycStatus;
import innovitics.azimut.utilities.crosslayerenums.UserIdType;
import innovitics.azimut.utilities.datautilities.BooleanUtility;
import innovitics.azimut.utilities.datautilities.JsonUtility;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class FraService extends BaseBusinessService {

  @Autowired
  private FraNtraApi fraNtraApi;
  @Autowired
  private FraAmlApi fraAmlApi;
  @Autowired
  private FraCsoApi fraCsoApi;
  @Autowired
  private FraSendContractApi fraSendContractApi;

  @Autowired
  DigitalRegistryService digitalRegistryService;

  @Autowired
  public ReviewService reviewService;

  @Autowired
  public ReasonService reasonService;

  public Boolean checkCso(BusinessUser user) {
    try {
      return checkCso(user, false);
    } catch (Exception e) {
      return false;
    }
  }

  public Boolean checkCso(BusinessUser user, Boolean raiseError) throws Exception {
    if (!NumberUtility.areLongValuesMatching(user.getIdType(), UserIdType.NATIONAL_ID.getTypeId())) {
      return true;
    }
    if (user.getCso() != null) {
      var cso = JsonUtility.fromJson(user.getCso());
      if (BooleanUtility.isTrue((Boolean) cso.get("isValid"))) {
        return true;
      }
    }
    try {
      FraCsoInput fraCsoInput = new FraCsoInput();
      fraCsoInput.setNationalIdNumber(user.getUserId());
      fraCsoInput.setPersonName(user.getFirstName());
      fraCsoInput.setPersonOtherNames(user.getLastName());
      var idJsonData = JsonUtility.fromJson(user.getIdData());
      fraCsoInput.setFactoryNumber((String) idJsonData.get("fcn"));
      // Define the formatter matching the input format
      DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("d-M-yyyy");
      // Define the desired output format
      DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
      // Parse the input date
      LocalDate date = LocalDate.parse(user.getDateOfIdExpiry(), inputFormatter);
      // Format the date to desired output
      String formattedDate = date.format(outputFormatter);
      fraCsoInput.setCardExpirationDate(formattedDate);
      var res = fraCsoApi.getData(fraCsoInput);

      user.setCso(JsonUtility.toJson(res));

      if (!res.getIsValid()) {
        MyLogger.error("CSO is not valid");
        var reason = reasonService.findByReasonType(ReasonType.CSO).get(0);
        reviewService.addRejectReview(user.getId(), "CSO is not valid", reason);
        user.setKycStatus(KycStatus.REJECTED.getStatusId());
      } else {
        reviewService.deleteFraOldReviews(user.getId(), ReasonType.CSO.getType());
      }
      this.editUser(user);
      return res.getIsValid();
    } catch (Exception e) {
      if (raiseError)
        throw e;
      else
        MyLogger.logStackTrace(e);
      return false;
    }
  }

  public Boolean checkNtra(BusinessUser user) {
    try {
      return checkNtra(user, false);
    } catch (Exception e) {
      return false;
    }
  }

  public Boolean checkNtra(BusinessUser user, Boolean raiseError) throws Exception {
    if (!NumberUtility.areLongValuesMatching(user.getIdType(), UserIdType.NATIONAL_ID.getTypeId()) ||
        !StringUtility.stringsMatch(user.getCountryPhoneCode(), "+20")) {
      return true;
    }
    if (user.getNtra() != null) {
      var ntra = JsonUtility.fromJson(user.getNtra());
      if (BooleanUtility.isTrue((Boolean) ntra.get("isMatched"))) {
        return true;
      }
    }
    try {
      FraNtraInput fraNtraInput = new FraNtraInput();
      fraNtraInput.setNationalIdNumber(user.getUserId());
      fraNtraInput.setPhoneNumber("0" + user.getPhoneNumber());
      var res = fraNtraApi.getData(fraNtraInput);
      user.setNtra(JsonUtility.toJson(res));
      if (!res.getIsMatched()) {
        MyLogger.error("NTRA is not valid");
        var reason = reasonService.findByReasonType(ReasonType.NTRA).get(0);
        reviewService.addRejectReview(user.getId(), "NTRA is not valid", reason);
        user.setKycStatus(KycStatus.REJECTED.getStatusId());
      } else {
        reviewService.deleteFraOldReviews(user.getId(), ReasonType.NTRA.getType());
      }
      this.editUser(user);
      return res.getIsMatched();
    } catch (Exception e) {
      if (raiseError)
        throw e;
      else
        MyLogger.logStackTrace(e);
      return false;
    }

  }

  public Boolean checkAml(BusinessUser user) {
    try {
      return checkAml(user, false);
    } catch (Exception e) {
      return false;
    }
  }

  public Boolean checkAml(BusinessUser user, Boolean raiseError) throws Exception {
    if (!NumberUtility.areLongValuesMatching(user.getIdType(), UserIdType.NATIONAL_ID.getTypeId())) {
      return true;
    }
    if (user.getAml() != null) {
      return true;
    }
    try {
      FraAmlInput fraAmlInput = new FraAmlInput();
      fraAmlInput.setIdNumber(user.getUserId());
      fraAmlInput.setFullName(user.getFirstName() + " " + user.getLastName());
      var res = fraAmlApi.getData(fraAmlInput);
      user.setAml(JsonUtility.toJson(res));
      if (res.getData() != null && res.getData().getMatched()) {
        MyLogger.info("User is blacklisted");
        user.setKycStatus(KycStatus.REJECTED.getStatusId());
        user.getAzimutAccount().setClientAML(StringUtility.CLIENT_AML_BLACKLIST);
        this.editUser(user);
        var reason = reasonService.findByReasonType(ReasonType.AML).get(0);
        reviewService.addRejectReview(user.getId(), "User is blacklisted in AML", reason);
      } else {
        reviewService.deleteFraOldReviews(user.getId(), ReasonType.AML.getType());
      }
      this.editUser(user);
      return !res.getData().getMatched();
    } catch (Exception e) {
      if (raiseError)
        throw e;
      else
        MyLogger.logStackTrace(e);
      return false;
    }
  }

  public void sendContract(BusinessUser user, Boolean isCompany, byte[] contractBytes) throws Exception {
    try {
      FraSendContractInput fraSendContractInput = new FraSendContractInput();
      EnrollLocationInfo locationInfo = new EnrollLocationInfo();
      var locationDr = digitalRegistryService.getLastDigitalRegistry(user.getId(),
          DigitalRegistryAction.UPDATE_LOCATION);
      locationInfo.setLatitude(locationDr.getLatitude().toString());
      locationInfo.setLongitude(locationDr.getLongitude().toString());

      fraSendContractInput.setContractBase64(Base64.getEncoder().encodeToString(contractBytes));
      fraSendContractInput.setContractId(user.getUuid().toString());
      fraSendContractInput.setContractStatus(isCompany ? "ServiceProviderSigned" : "CustomerSigned");
      fraSendContractInput.setCustomerId(user.getId().toString());
      fraSendContractInput.setCustomerName(user.getFirstName() + " " + user.getLastName());
      fraSendContractInput.setCustomerNationalId(user.getUserId());
      fraSendContractInput.setLocation(locationInfo);
      MyLogger.info("Contract Request:: " + JsonUtility.toJson(fraSendContractInput));
      var signDr = digitalRegistryService.getLastDigitalRegistry(user.getId(),
          DigitalRegistryAction.SIGN_CONTRACT);
      fraSendContractApi.setJwtToken(signDr.getSessionId());
      var res = fraSendContractApi.getData(fraSendContractInput);
      if (res.getData() != null) {
        if (isCompany) {
          user.setFraCompanyStoreId(res.getData().getStoreId());
        } else {
          user.setFraStoreId(res.getData().getStoreId());
        }
        this.editUser(user);
      }
      MyLogger.info("Contract Response:: " + JsonUtility.toJson(res));
    } catch (Exception e) {
      throw e;
    }
  }

}
