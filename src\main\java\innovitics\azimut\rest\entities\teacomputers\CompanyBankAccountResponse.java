package innovitics.azimut.rest.entities.teacomputers;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class CompanyBankAccountResponse extends TeaComputerResponse {

  private Long accountID;
  private String bankId;
  private String bankName;
  private String branchId;
  private String branchName;
  private String accountNo;
  @JsonProperty("iBAN")
  private String iBAN;
  private String accountStatus;
  private String accountStatusName;
  private String swiftCode;
  private String currencyID;
  private String currencyName;

  @Override
  public String toString() {
    return "CompanyBankAccountResponse [accountID=" + accountID + ", bankId=" + bankId + ", bankName=" + bankName
        + ", branchId=" + branchId + ", branchName=" + branchName + ", accountNo=" + accountNo + ", iban="
        + iBAN + ", accountStatus=" + accountStatus + ", accountStatusName=" + accountStatusName
        + ", swiftCode=" + swiftCode + ", currencyID=" + currencyID + ", currencyName=" + currencyName + "]";
  }
}
