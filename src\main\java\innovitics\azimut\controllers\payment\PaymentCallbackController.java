package innovitics.azimut.controllers.payment;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import innovitics.azimut.businessmodels.payment.PaymobResult;
import innovitics.azimut.businessmodels.payment.PaytabsResult;
import innovitics.azimut.businessservices.BusinessPaymentService;
import innovitics.azimut.controllers.BaseController;
import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.controllers.GenericResponseHandler;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.logging.MyLogger;

@Controller
@RequestMapping(value = "/api/paytabs", produces = {
    MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE })
public class PaymentCallbackController extends BaseController {
  @Autowired
  BusinessPaymentService businessPaymentService;

  private @Autowired GenericResponseHandler<PaytabsResult> paytabsHandler;

  @PostMapping(value = "/paymobCallback", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<PaytabsResult>> paymobCallback(
      @RequestParam(name = "hmac", required = false) String hmacIn,
      @RequestBody PaymobResult paymobCallbackRequest) throws BusinessException {
    var hmac = hmacIn == null ? paymobCallbackRequest.getHmac() : hmacIn;
    return paytabsHandler.generateBaseGenericResponse(PaytabsResult.class,
        this.businessPaymentService.updateTransactionAfterPaymobCallback(paymobCallbackRequest, hmac), null, null);
  }

  @PostMapping(value = "/callback", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<PaytabsResult>> callback(
      @RequestHeader(name = "Signature", required = false) String signature,
      @RequestParam(StringUtility.TRANSACTION_SERIAL_PARAM_NAME) String serial,
      @RequestBody PaytabsResult paytabsCallbackRequest) throws BusinessException {
    return paytabsHandler.generateBaseGenericResponse(PaytabsResult.class,
        this.businessPaymentService.updateTransactionAfterGatewayCallback(paytabsCallbackRequest, serial), null, null);
  }

  @PostMapping(value = "/instantCallback", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<PaytabsResult>> instantCallback(
      @RequestHeader(name = "Signature", required = false) String signature,
      @RequestBody PaytabsResult paytabsCallbackRequest) throws BusinessException {
    MyLogger.info("Signature:::" + signature);
    return paytabsHandler.generateBaseGenericResponse(PaytabsResult.class,
        this.businessPaymentService.updateTransactionAfterGatewayCallback(paytabsCallbackRequest, null), null, null);
  }

}
