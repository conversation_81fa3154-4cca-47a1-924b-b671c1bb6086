package innovitics.azimut.controllers;

import org.apache.tomcat.util.http.fileupload.impl.SizeLimitExceededException;
import org.hibernate.type.AnyType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.multipart.MultipartException;

import innovitics.azimut.businessmodels.CurrentUser;
import innovitics.azimut.businessmodels.admin.BusinessAdminUser;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.configproperties.ConfigProperties;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.GeneralException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.security.JwtUtil;
import innovitics.azimut.utilities.datautilities.DateUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;
import innovitics.azimut.validations.Validation;

public abstract class BaseController {

  protected @Autowired CurrentUser currentUser;

  protected @Autowired Validation validation;
  protected @Autowired JwtUtil jwtUtil;
  protected @Autowired ConfigProperties configProperties;

  protected BusinessUser getCurrentRequestHolder(String token) throws BusinessException {
    BusinessUser businessUser = (BusinessUser) this.currentUser.getCurrentUser();
    // CurrentRequestHolder.get();
    // this.jwtUtil.getBusinessUserFromToken(StringUtility.generateSubStringStartingFromCertainIndex(token,'
    // '));
    if (businessUser != null) {
      MyLogger.info("Current Request Holder::" + businessUser.getUsername());
    }

    return businessUser;
  }

  protected BusinessAdminUser getCurrentAdminRequestHolder(String token) throws BusinessException {
    BusinessAdminUser businessAdminUser = (BusinessAdminUser) this.currentUser.getCurrentUser();
    // CurrentRequestHolder.get();
    // this.jwtUtil.getBusinessAdminUserFromToken(StringUtility.generateSubStringStartingFromCertainIndex(token,'
    // '));
    if (businessAdminUser != null) {
      MyLogger.info("Current Request Holder::" + businessAdminUser.getUsername());
    }

    return businessAdminUser;
  }

  protected ResponseEntity<BaseGenericResponse<AnyType>> generateFailureBaseGenericResponseEntity(
      GeneralException exception, HttpStatus httpStatus) {

    var responseEntity = new ResponseEntity<BaseGenericResponse<AnyType>>(
        this.generateBaseGenericResponseFailure(exception.getErrorCode(), exception.getErrorMessage()),
        exception.getHttpStatus() != null ? exception.getHttpStatus() : httpStatus);
    return responseEntity;
  }

  protected ResponseEntity<BaseGenericResponse<AnyType>> generateFailureBaseGenericResponseEntity(
      GeneralException exception, HttpStatus httpStatus, String locale) {
    String errorMessage = "";
    if (StringUtility.isStringPopulated(locale)) {
      if (StringUtility.stringsMatch(locale, StringUtility.ENGLISH)) {
        errorMessage = exception.getErrorMessage();
      } else if (StringUtility.stringsMatch(locale, StringUtility.ARABIC)) {
        errorMessage = exception.getErrorMessageAr();
      }
    } else {
      errorMessage = exception.getErrorMessage();
    }
    ResponseEntity<BaseGenericResponse<AnyType>> responseEntity = new ResponseEntity<BaseGenericResponse<AnyType>>(
        this.generateBaseGenericResponseFailure(exception.getErrorCode(), errorMessage),
        exception.getHttpStatus() != null ? exception.getHttpStatus() : httpStatus);
    return responseEntity;
  }

  public BaseGenericResponse<AnyType> generateBaseGenericResponseFailure(int errorCode, String errorMessage) {
    MyLogger.info("Generating the Base Response Failure using :::" + errorCode + " and " + errorMessage);
    var baseGenericResponse = new BaseGenericResponse<AnyType>();
    baseGenericResponse.setMessage(errorMessage);
    baseGenericResponse.setStatus(errorCode);
    baseGenericResponse.setTransactionId(Thread.currentThread().getName());
    return baseGenericResponse;

  }

  @ExceptionHandler({ MultipartException.class, MaxUploadSizeExceededException.class, SizeLimitExceededException.class,
      IllegalStateException.class })
  protected ResponseEntity<BaseGenericResponse<AnyType>> handleBaseGenericResponseException(Exception exception,
      WebRequest webRequest) {
    String locale = webRequest.getHeader(StringUtility.LANGUAGE);
    MyLogger.info("Exception Caught::::");
    MyLogger.logStackTrace(exception);
    if (exception instanceof MultipartException || exception instanceof MaxUploadSizeExceededException
        || exception instanceof SizeLimitExceededException || exception instanceof IllegalStateException) {
      if (StringUtility.isStringPopulated(locale)) {
        return this.generateFailureBaseGenericResponseEntity(new BusinessException(ErrorCode.FILE_TOO_BIG),
            HttpStatus.BAD_REQUEST, locale);
      }
      return this.generateFailureBaseGenericResponseEntity(new BusinessException(ErrorCode.FILE_TOO_BIG),
          HttpStatus.BAD_REQUEST);
    }

    return null;
  }

  @ExceptionHandler(BusinessException.class)
  protected ResponseEntity<BaseGenericResponse<AnyType>> handleBaseGenericResponseException(
      BusinessException businessException, WebRequest webRequest) {
    MyLogger.info("Exception Caught::::");
    MyLogger.error("Business Exception Error Message:" + businessException.getErrorMessage());
    MyLogger.logStackTrace(businessException);
    var locale = webRequest.getHeader(StringUtility.LANGUAGE);
    if (locale != null) {
      return this.generateFailureBaseGenericResponseEntity(businessException,
          this.determineErrorType(businessException.getErrorCode()), locale);
    }
    return this.generateFailureBaseGenericResponseEntity(businessException,
        this.determineErrorType(businessException.getErrorCode()));

  }

  @ExceptionHandler(IntegrationException.class)
  protected ResponseEntity<BaseGenericResponse<AnyType>> handleBaseGenericResponseException(
      IntegrationException integrationException, WebRequest webRequest) {
    MyLogger.error("Exception Caught");
    MyLogger.error("Integration Exception Error Code:" + integrationException.getErrorCode());
    MyLogger.error("Integration Exception Error Message:" + integrationException.getErrorMessage());
    MyLogger.logStackTrace(integrationException);
    BusinessException businessException = new BusinessException(integrationException.getErrorCode(),
        DateUtility.getCurrentDate(), integrationException.getErrorMessage(), integrationException.getErrorMessageAr(),
        integrationException.getDescription(), integrationException.getStackTrace());

    return this.handleBaseGenericResponseException(businessException, webRequest);
  }

  private HttpStatus determineErrorType(int errorCode) {
    MyLogger.info("Determine the Error Code:::" + errorCode);
    HttpStatus httpStatus = HttpStatus.BAD_REQUEST;
    if (errorCode == ErrorCode.NO_DATA_FOUND.getCode()) {
      httpStatus = HttpStatus.BAD_REQUEST;
    } else if (errorCode == ErrorCode.OPERATION_NOT_PERFORMED.getCode()) {
      httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
    }

    return httpStatus;
  }

}
