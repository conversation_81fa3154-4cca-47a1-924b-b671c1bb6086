package innovitics.azimut.utilities.datautilities;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import innovitics.azimut.utilities.logging.MyLogger;

@Component
public final class NumberUtility {
  protected static final Logger logger = LoggerFactory.getLogger(NumberUtility.class);

  public static boolean areDoubleValuesMatching(Double value1, Double value2) {
    boolean result = false;
    logger.info("Are the values " + value1 + " and " + value2 + " matching?");

    if (value1 != null && value2 != null && value1.doubleValue() == value2.doubleValue())
      result = true;
    else
      result = false;

    logger.info("result=" + result);
    return result;
  }

  public static boolean areLongValuesMatching(Long value1, Long value2) {
    boolean result = false;
    logger.info("Are the values " + value1 + " and " + value2 + " matching?");

    if (value1 != null && value2 != null && value1.longValue() == value2.longValue())
      result = true;
    else
      result = false;

    logger.info("result=" + result);
    return result;
  }

  public static boolean areIntegerValuesMatching(Integer value1, Integer value2) {
    boolean result = false;
    logger.info("Are the values " + value1 + " and " + value2 + " matching?");

    if (value1 != null && value2 != null && value1.intValue() == value2.intValue())
      result = true;
    else
      result = false;

    logger.info("result=" + result);
    return result;
  }

  public static boolean isNewValueLessThanOrEqualOldValue(Integer oldValue, Integer newValue) {
    boolean result = false;

    if (oldValue != null && newValue != null && newValue.longValue() <= oldValue.longValue())
      result = true;

    logger.info("result=" + result);
    return result;
  }

  public static String changeFormat(Double value) {
    logger.info("value to be formatted::" + value);
    DecimalFormat decFormat = new DecimalFormat("###,###");
    String str = decFormat.format(value);

    return str;
  }

  public static BigDecimal changeFormat(BigDecimal value) {
    return value.setScale(2, RoundingMode.HALF_EVEN);
  }

  public <T> List<T> difference(List<T> list1, List<T> list2) {
    List<T> list = new ArrayList<T>();
    for (T t : list1) {
      if (list2.contains(t)) {
        list.add(t);
      }
    }
    return list;
  }

  public static boolean areTheTwoListsIdentical(List<Long> list1, List<Long> list2) {
    boolean result = true;
    int counter = 0;
    for (Long valueFromList1 : list1) {
      for (Long valueFromList2 : list2) {
        if (areLongValuesMatching(valueFromList1, valueFromList2)) {
          counter++;
        }
      }
    }
    result = (NumberUtility.areIntegerValuesMatching(counter, list1.size())
        && NumberUtility.areIntegerValuesMatching(counter, list2.size()));
    MyLogger.info("Are the two lists are identical::" + result);
    return result;
  }

  public static boolean isNumberEven(int number) {
    boolean result = (number % 2) == 0;
    MyLogger.info("Is the number even ::" + result);
    return result;
  }
}
