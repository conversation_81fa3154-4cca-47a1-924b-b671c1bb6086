package innovitics.azimut.services;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import innovitics.azimut.controllers.admin.DTOs.DividendDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.models.Dividend;
import innovitics.azimut.repositories.fund.DividendRepository;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.dbutilities.DatabaseConditions;
import innovitics.azimut.utilities.dbutilities.SearchCriteria;
import innovitics.azimut.utilities.dbutilities.specifications.child.DividendSpecification;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class DividendService extends AbstractService<Dividend, String> {

  @Autowired
  DividendRepository dividendRepository;

  @Autowired
  DividendSpecification dividendSpecification;

  private @Autowired NavService navService;

  public List<Dividend> getAllDividends() {
    return dividendRepository.findAll();
  }

  public Page<Dividend> getFilteredDividends(DatabaseConditions databaseConditions) {

    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    if (this.searchCriteriaListUtility.isListPopulated(databaseConditions.getSearchCriteria())) {
      searchCriteriaList = databaseConditions.getSearchCriteria();

    }
    this.changeDateFormat(searchCriteriaList, "createdAt");
    this.changeDataType(searchCriteriaList, "fundId", Long.class);
    MyLogger.info("Search Criteria:::" + searchCriteriaList.toString());

    Page<Dividend> dividends = this.dividendRepository.findAll(
        this.dividendSpecification.findByCriteria(searchCriteriaList),
        databaseConditions.getPageRequest());

    return dividends;
  }

  public void addDividend(DividendDto businessDividend) throws BusinessException {
    if (businessDividend != null && businessDividend.getDividend() != null) {
      Dividend dividend = businessDividend.getDividend();
      SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
      List<String> dates = new ArrayList<>();
      dates.add(simpleDateFormat.format(dividend.getDividendDate()));
      var dividendNavs = navService.getNavsWithDividendsDates(dates, dividend.getFundId());
      if (!NumberUtility.areIntegerValuesMatching(dividendNavs.size(), 2))
        throw new BusinessException(ErrorCode.INVALID_DIVIDEND_DATE);
      dividend.setCreatedAt(new Date());
      this.dividendRepository.save(dividend);
    }
  }

  public void editDividend(DividendDto businessDividend) throws BusinessException {
    if (businessDividend != null && businessDividend.getDividend() != null) {
      Dividend dividend = businessDividend.getDividend();
      SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
      List<String> dates = new ArrayList<>();
      dates.add(simpleDateFormat.format(dividend.getDividendDate()));
      var dividendNavs = navService.getNavsWithDividendsDates(dates, dividend.getFundId());
      if (!NumberUtility.areIntegerValuesMatching(dividendNavs.size(), 2))
        throw new BusinessException(ErrorCode.INVALID_DIVIDEND_DATE);
      var dbDividend = getDividend(dividend.getId());
      dividend.setCreatedAt(dbDividend.getCreatedAt());
      this.dividendRepository.save(dividend);
    }
  }

  public void deleteDividend(DividendDto businessDividend) {
    if (businessDividend != null && businessDividend.getDividend() != null) {
      Dividend dividend = businessDividend.getDividend();
      this.dividendRepository.delete(dividend);
    }
  }

  public void updateDividend(Dividend dividend) {
    this.dividendRepository.save(dividend);
  }

  public Dividend getDividend(Long id) {
    return this.dividendRepository.getById(id);
  }

}
