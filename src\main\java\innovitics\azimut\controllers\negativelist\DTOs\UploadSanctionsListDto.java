package innovitics.azimut.controllers.negativelist.DTOs;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import org.springframework.web.multipart.MultipartFile;

import lombok.Data;

@Data
public class UploadSanctionsListDto {
    
    @NotNull(message = "File is required")
    private MultipartFile file;
    
    @NotBlank(message = "List source is required")
    private String listSource;
    
    private String listType;
    
    private String description;
    
    private Boolean replaceExisting = false;
}
