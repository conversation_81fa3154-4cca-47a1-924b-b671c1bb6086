package innovitics.azimut.rest.entities.paymob;

import com.fasterxml.jackson.annotation.JsonProperty;

import innovitics.azimut.rest.entities.BaseRestRequest;
import innovitics.azimut.rest.models.paymob.PaymobBillingData;
import innovitics.azimut.rest.models.paymob.PaymobItem;
import lombok.Data;

@Data
public class PaymobInitiatePaymentRequest extends BaseRestRequest {

  private Integer amount;
  private String currency = "EGP";
  @JsonProperty("payment_methods")
  private Integer[] paymentMethods;
  private PaymobItem[] items;
  @JsonProperty("billing_data")
  private PaymobBillingData billingData;
  private PaymobBillingData customer;
  @JsonProperty("special_reference")
  private String specialReference;
  private Integer expiration = 3600;
  @JsonProperty("redirection_url")
  private String redirectionUrl;
  @JsonProperty("notification_url")
  private String notificationUrl;
}
