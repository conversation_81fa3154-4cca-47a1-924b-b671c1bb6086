package innovitics.azimut.rest.entities.teacomputers;

import lombok.Data;

@Data
public class ClientFundResponse extends TeaComputerResponse{
	private String clientName;
	private String mobile;
	private Double  quantity;
	private Double avgcost;
	private String tradePrice;
	private Double availableToBuy;
	private Double availableToSell;
	private String  statusName;
	private String clientStatus;
	private String fundID;
	private String certificateName;
	private String assetClass;
	private String currencyID;
	private String currencyName;
	private String currencyRate;

	@Override
	public String toString() {
		return "GetClientFundsResponse [clientName=" + clientName + ", mobile=" + mobile + ", quantity=" + quantity
				+ ", avgcost=" + avgcost + ", tradePrice=" + tradePrice + ", availableToBuy=" + availableToBuy
				+ ", availableToSell=" + availableToSell + ", statusName=" + statusName + ", clientStatus="
				+ clientStatus + ", fundID=" + fundID + ", certificateName=" + certificateName + ", assetClass="
				+ assetClass + ", currencyID=" + currencyID + ", currencyName=" + currencyName + ", currencyRate="
				+ currencyRate + "]";
	}
	
}
