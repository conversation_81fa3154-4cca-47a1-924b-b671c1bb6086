package innovitics.azimut.models.digitalregistry;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

@Entity
@Table(name = "api_registry")
@Data
public class ApiRegistry {
  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE)
  Long id;

  Date createdAt;
  String ip;

  public ApiRegistry(String ip) {
    this.ip = ip;
    this.createdAt = new Date();
  }

  public ApiRegistry() {
  }
}
