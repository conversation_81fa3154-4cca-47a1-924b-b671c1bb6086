package innovitics.azimut.repositories.user;

import java.util.Date;
import java.util.List;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import innovitics.azimut.models.user.User;

@Repository
public interface UserDynamicRepository extends JpaRepository<User, Long>, JpaSpecificationExecutor<User> {

  @Query(value = "update app_users set is_reviewed=1, kyc_status=0, user_step=12 where id=?", nativeQuery = true)
  @Modifying
  @Transactional
  void flagTheUserAsReviewed(Long userId);

  @Query(value = "update app_users set kyc_status=?, user_step=13 where id=?", nativeQuery = true)
  @Modifying
  @Transactional
  void assignUserKycFlag(Integer kycStatus, Long userId);

  @Query(value = "update app_users set messaging_token=NULL where id=?", nativeQuery = true)
  @Modifying
  @Transactional
  void removeMessagingToken(Long userId);

  @Query(value = "update app_users set logged_in=1,last_login=? where id=?", nativeQuery = true)
  @Modifying
  @Transactional
  void updateUserLogin(Date date, Long userId);

  @Query(value = "SELECT  distinct lower(referral_code)" +
      "FROM app_users where referral_code is not null and referral_code <> \"\"", nativeQuery = true)
  List<String> getReferralCodes();

  @Query(value = "SELECT " +
      "COUNT(*), kyc_status, is_verified, signed_pdf, is_old " +
      "FROM app_users " +
      "where deleted_at is null " +
      "and created_at > :start_date and created_at < :end_date " +
      "GROUP BY kyc_status, is_verified, signed_pdf, is_old", nativeQuery = true)
  List<Object[]> countGroupByKycStatusFiltered(@Param("start_date") Date startDate, @Param("end_date") Date endDate);

  @Query(value = "SELECT COUNT(*) FROM app_users " +
      "where deleted_at is null " +
      "and pdf_signed_at > :start_date and pdf_signed_at < :end_date ", nativeQuery = true)
  Integer countSignedFiltered(@Param("start_date") Date startDate, @Param("end_date") Date endDate);

  @Query(value = "SELECT COUNT(distinct reviews.user_id)" +
      "FROM reviews join app_users on reviews.user_id = app_users.id " +
      "where reviews.deleted_at is null and reviews.result = 2 " +
      "and app_users.deleted_at is null " +
      "and reviews.created_at > :start_date and reviews.created_at < :end_date ", nativeQuery = true)
  Integer countRejectedFiltered(@Param("start_date") Date startDate, @Param("end_date") Date endDate);

  @Query(value = "select count(*) FROM (" +
      "SELECT reviews.user_id FROM reviews " +
      "join app_users on reviews.user_id = app_users.id " +
      "where reviews.deleted_at is null " +
      "and app_users.deleted_at is null " +
      "and reviews.created_at > :start_date and reviews.created_at < :end_date " +
      "GROUP BY reviews.user_id " +
      "HAVING COUNT(*) = COUNT(CASE WHEN reviews.result = 1 THEN 1 END) ) c", nativeQuery = true)
  Integer countAcceptedFiltered(@Param("start_date") Date startDate, @Param("end_date") Date endDate);
}
