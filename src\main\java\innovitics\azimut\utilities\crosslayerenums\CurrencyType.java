package innovitics.azimut.utilities.crosslayerenums;

public enum CurrencyType {

  EGYPTIAN_POUND(1, "E<PERSON>", "جم"),
  US_DOLLAR(32, "USD", "دولار"),

  ;

  CurrencyType(long typeId, String type, String typeAr) {
    this.typeId = typeId;
    this.type = type;
    this.typeAr = typeAr;
  }

  private final long typeId;
  private final String type;
  private final String typeAr;

  public long getTypeId() {
    return typeId;
  }

  public String getType() {
    return type;
  }

  public String getTypeAr() {
    return typeAr;
  }

  public static CurrencyType getById(long id) {
    for (CurrencyType currencyType : values()) {
      if (currencyType.typeId == id) {
        return currencyType;
      }
    }
    return null;
  }
}
