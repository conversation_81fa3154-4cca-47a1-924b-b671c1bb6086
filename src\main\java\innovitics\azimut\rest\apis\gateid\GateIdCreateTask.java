package innovitics.azimut.rest.apis.gateid;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import innovitics.azimut.rest.entities.gateid.GateIdCreateTaskOutput;

@Service
public class GateIdCreateTask
    extends RestGateIdApiBase<Void, GateIdCreateTaskOutput> {

  @Override
  protected String generateURL(String params) {
    return this.configProperties.getGateIdUrl() + "/users/graphql";
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.POST;
  }

  @Override
  protected HttpEntity<?> generateRequestFromInput(Void input) {
    String request = "{\"query\": \"" +
        "      query MyQuery {" +
        "        addTask(projectId: " + configProperties.getGateIdProjectId() + ") { id  }" +
        "      }" +
        "   \"}";
    return new HttpEntity<>(request, this.generateHeaders());
  }

}
