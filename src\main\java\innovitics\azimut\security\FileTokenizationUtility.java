package innovitics.azimut.security;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

import innovitics.azimut.businessmodels.user.Token;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.logging.MyLogger;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

@Component
public class FileTokenizationUtility {

  public Token generateTokenUsingFileLocation(String key, String fileLocation, String expiration) {
    UserDetails userDetails = new User(fileLocation, " ", new ArrayList<>());
    Token token = this.generateToken(userDetails, expiration, key);
    return token;
  }

  private String extractUrl(String token, String key) {
    return extractClaim(token, Claims::getSubject, key);
  }

  public Boolean validateToken(String token, String fileLocation, String fileName) {
    MyLogger.info("Validating the file token::::::::::::::::::::::::::::::::");
    MyLogger.info("File name::::" + fileName);
    MyLogger.info("File location::" + fileLocation);

    final String url = extractUrl(token, this.padKey(fileName));

    MyLogger.info("URL::" + url);
    boolean stringsMatch = url.equals(fileLocation);
    boolean expiring = isTokenExpired(token, fileName);

    MyLogger.info("stringsMatch::" + stringsMatch);
    MyLogger.info("expiring::" + expiring);
    MyLogger.info("total:::" + (stringsMatch && !expiring));

    return (stringsMatch && !expiring);
  }

  protected String createToken(Map<String, Object> claims, String subject, String expiration, String key) {
    String token = "";
    token = Jwts.builder().setClaims(claims).setSubject(subject).setIssuedAt(new Date(System.currentTimeMillis()))
        .setExpiration(this.calculateExpirationDate(expiration))
        .signWith(SignatureAlgorithm.HS512, this.padKey(key)).compact();

    return token;

  }

  protected Date calculateExpirationDate(String expiration) {
    Date date = new Date(System.currentTimeMillis() + (1000 * 60) * Long.parseLong(expiration));
    return date;
  }

  protected Token generateToken(UserDetails userDetails, String expiration, String key) {
    Map<String, Object> claims = new HashMap<>();
    String tokenString = this.createToken(claims, userDetails.getUsername(), expiration, key);
    Token token = new Token();
    token.setTokenString(tokenString);
    return token;
  }

  public <T> T extractClaim(String token, Function<Claims, T> claimsResolver, String key) {
    final Claims claims = extractAllClaims(token, key);
    return claimsResolver.apply(claims);
  }

  protected Claims extractAllClaims(String token, String key) {
    return Jwts.parser().setSigningKey(key).parseClaimsJws(token).getBody();
  }

  public Boolean isTokenExpired(String token, String key) {
    return extractExpiration(token, key).before(new Date());
  }

  public Date extractExpiration(String token, String key) {
    return extractClaim(token, Claims::getExpiration, key);
  }

  String padKey(String key) {
    if (StringUtility.isStringPopulated(key)) {
      if (key.length() < StringUtility.MINIMUM_KEY_LENGTH) {
        StringBuffer stringBuffer = new StringBuffer(key);
        stringBuffer.append(StringUtility.KEY_PADDING);
        return stringBuffer.toString();
      } else {
        return key;
      }
    } else {
      return StringUtility.KEY_PADDING;
    }
  }
}
