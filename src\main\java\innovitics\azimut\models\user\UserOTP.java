package innovitics.azimut.models.user;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import innovitics.azimut.models.DbBaseEntity;
import innovitics.azimut.models.OTPMethod;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Entity
@Table(name = "user_otp")
@Setter
@Getter
@ToString
public class UserOTP extends DbBaseEntity {
  private String userPhone;
  private String otp;
  private Integer numberOfTimes;
  private Date nextTrial;
  private String functionality;
  private OTPMethod otpMethod;
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  private Date deletedAt;
  private String sessionInfo;
  private Integer contractType;
  private Long userId;
  private String assessmentId;
}
