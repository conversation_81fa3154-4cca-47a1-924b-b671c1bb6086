package innovitics.azimut.repositories.notification;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;

import innovitics.azimut.models.Notification;

public interface NotificationRepository
    extends JpaRepository<Notification, Long>, EntityGraphJpaSpecificationExecutor<Notification> {

  @Query(value = "update notifications set is_read=1 where id=?", nativeQuery = true)
  @Modifying
  @Transactional
  void readNotification(Long id);

}
