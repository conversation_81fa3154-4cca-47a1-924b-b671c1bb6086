package innovitics.azimut.rest.entities.vl;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class VLConfirmOtpRequest {
  @JsonProperty("UserName")
  private String username;
  @JsonProperty("Password")
  private String password;
  @JsonProperty("REQUESTCODE")
  private String requestCode;
  @JsonProperty("PIN")
  private String otp;

  @Override
  public String toString() {
    return "VLConfirmOtpRequest [requestCode=" + requestCode + ", otp=" + otp + "]";
  }

}