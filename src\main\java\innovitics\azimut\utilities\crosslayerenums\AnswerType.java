package innovitics.azimut.utilities.crosslayerenums;

import java.util.ArrayList;
import java.util.List;

import innovitics.azimut.utilities.datautilities.BooleanUtility;

public enum AnswerType {

  RADIO(1, "RADIO", false),
  CHECK(2, "CHECK", false),
  DROP(3, "DROP", true),
  TEXT(4, "TEXT", true),
  RICH(5, "RICH", true),
  EMAIL(6, "EMAIL", true),
  CALENDER(7, "CALENDER", true),
  DOCUMENT(8, "DOCUMENT", true),
  PHONE(9, "PHONE", true);

  AnswerType(int typeId, String type, boolean isText) {
    this.typeId = typeId;
    this.type = type;
    this.isText = isText;
  }

  private final int typeId;
  private final String type;
  private final boolean isText;

  public int getTypeId() {
    return typeId;
  }

  public String getType() {
    return type;
  }

  public boolean isText() {
    return isText;
  }

  public static List<String> getAnswerTypes(boolean isText) {
    List<String> answerTypes = new ArrayList<String>();
    for (AnswerType answerType : values()) {
      if (BooleanUtility.areValuesIdentical(isText, answerType.isText()))
        answerTypes.add(answerType.getType());
    }
    return answerTypes;
  }
}
