package innovitics.azimut.businessmodels;

import innovitics.azimut.utilities.CustomJsonRootName;
import innovitics.azimut.utilities.crosslayerenums.OrderType;
import innovitics.azimut.utilities.crosslayerenums.TransactionOrderType;
import innovitics.azimut.utilities.crosslayerenums.TransactionStatus;
import innovitics.azimut.utilities.crosslayerenums.TransactionType;
import lombok.Data;

@CustomJsonRootName(plural = "transactions", singular = "transaction")
@Data
public class BusinessTransaction {

  private TransactionType transactionType;
  private Double amount;
  private String currency;
  private String trxDate;
  private TransactionStatus transactionStatus;
  private OrderType orderType;
  private TransactionOrderType transactionOrderType;
  private Integer status;
  private Integer type;
  private Integer order;
  private String searchFromDate;
  private String searchToDate;
  private String sorting;
  private Long azIdType;
  private String azId;

}
